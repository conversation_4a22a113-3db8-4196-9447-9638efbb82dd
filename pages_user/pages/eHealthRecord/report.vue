<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <my-header slot="gHeader" title="职业健康检查统计分析" />
    <view slot="gBody" class="page-body">

      <view class="infoCard grace-box-shadow">
        <view class="cardTitle">
          <view class="titlePoint"></view>
          <view class="titleText"><text>基本信息</text></view>
        </view>
        <view class="cardItem">
          <view><text>姓名：{{ userInfo.name || '暂无' }}</text></view>
          <view><text>年龄：{{ userInfo.age || '暂无' }}</text></view>
          <view><text>性别：{{ userInfo.gender === '0' ? '男' : (userInfo.gender === '1' ? '女' : '未知') }}</text></view>
          <!-- <view><text>婚姻状况：{{ userInfo.marriage }}</text></view> -->
        </view>
        <!-- <view class="cardItem">
          <view><text>手机：{{ userInfo.phoneNum }}</text></view>
        </view>
        <view class="cardItem">
          <view><text>证件号：{{ userInfo.idNo }}</text></view>
        </view> -->
      </view>


      <view class="infoCard grace-box-shadow">
        <view class="cardTitle">
          <view class="titlePoint"></view>
          <view class="titleText">
            <view>历次体检结果统计</view>
            <view style="display: flex;justify-content: space-between;">
            </view>
          </view>
        </view>


          <view class="cardItem">
            <view style="margin-right: 1em;">
              检查类型
            </view>
            <picker mode=selector @change="examTypeChangeForPie" range-key="label" :value="examTypeIndexForPie"
              :range="examTypeOption">
              <view class="uni-input">{{ examTypeLabelForPie }}</view>
            </picker>
          </view>

          <view class="cardItem">
            <view class="pieContainer" style="height: 32vh;">
              <!-- echarts板块 -->
              <view style="width: 100%;height: 100%;" v-show="!pieIsEmpty">
                <!-- #ifdef MP-WEIXIN -->
                <uni-ec-canvas class="uni-ec-canvas" id="pie-chart" canvas-id="multi-charts-pie" :ec="pieEc"
                  ref="pieCanvas"></uni-ec-canvas>
                <!-- #endif -->
                <!-- #ifdef H5 -->
                <view ref="pieContainer" id="pieContainer" class="chartContainer"></view>
                <!-- #endif -->
              </view>
              <!-- 添加无数据提示 -->
              <view v-if="pieIsEmpty" class="no-data-tip">
                <text>暂无相关检查数据</text>
              </view>
            </view>
          </view>
      </view>


      <view class="infoCard grace-box-shadow">
        <view class="cardTitle">
          <view class="titlePoint"></view>
          <view class="titleText"><text>身体健康状况变化趋势</text></view>
        </view>
        <view class="cardItem">
          <view style="margin-right: 1em;">
            检查类型
          </view>
          <picker mode=selector @change="examTypeChange" range-key="label" :value="examTypeIndex"
            :range="examTypeOption">
            <view class="uni-input">{{ examTypeLabel }}</view>
          </picker>
        </view>
        <view class="cardItem">
          <view style="margin-right: 1em;">
            检查指标
          </view>
          <picker mode=selector @change="bindPickerChange" range-key="label" :value="pickerIndex" :range="pickerArray">
            <view class="uni-input">{{ pickerLabel || '暂无数据' }}</view>
          </picker>
        </view>
        <!-- </view> -->

        <!-- <view class="infoCard grace-box-shadow"> -->
        <!-- <view class="cardTitle">
          <view class="titlePoint"></view>
          <view class="titleText">
            <view>变化趋势图</view>
            <view style="display: flex;justify-content: space-between;">
            </view>
          </view>
        </view> -->

        <view class="cardItem">
          <view class="container">
            <!-- echarts板块 -->
            <view style="width: 100%;height: 100%;">
              <!-- #ifdef MP-WEIXIN -->
              <uni-ec-canvas class="uni-ec-canvas" id="line-chart" canvas-id="multi-charts-line" :ec="ec"
                ref="canvas"></uni-ec-canvas>
              <!-- #endif -->
              <!-- #ifdef H5 -->
              <view ref="chartContainer" id="chartContainer" class="chartContainer"></view>
              <!-- #endif -->
            </view>
            <!-- 添加无数据提示 -->
            <view v-if="isEmpty" class="no-data-tip">
              <text>暂无相关检查数据</text>
            </view>
          </view>
        </view>

      </view>

    </view>
  </gracePage>
</template>

<script>
import healthApi from '@/api/health.js'
import recordApi from '@/api/record.js'

// #ifdef MP-WEIXIN
import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas';
import * as echarts from '@/components/uni-ec-canvas/echarts.js';
// #endif
// #ifdef H5
import * as echarts from 'echarts'  // 引入ECharts库
// #endif
import moment from 'moment'
// 趋势图
const option = {
  dataset: {
    source: []
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: [{
    type: 'category',
    axisLine: {
      lineStyle: {
        color: '#999'
      }
    },
    boundaryGap: false,
  }],
  yAxis: [{
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#DCDCDC'
      }
    },
    axisLine: {
      show: false,
      lineStyle: {
        color: '#A4A4A4'
      },
    },
    axisTick: {
      show: false
    },
    nameTextStyle: {
      color: '#A4A4A4'
    },
    splitArea: {
      show: false
    }
  }],
  series: [{
    name: '指标',
    type: 'line',
    encode: { x: 'year', y: 'result' },
    lineStyle: {
      normal: {
        width: 8,
        color: {
          type: 'linear',
          colorStops: [{
            offset: 0,
            color: '#A9F387' // 0% 处的颜色
          }, {
            offset: 1,
            color: '#48D8BF' // 100% 处的颜色
          }],
          globalCoord: false // 缺省为 false
        },
        shadowColor: 'rgba(72,216,191, 0.3)',
        shadowBlur: 10,
        shadowOffsetY: 20
      }
    },
    itemStyle: {
      normal: {
        color: '#fff',
        borderColor: '#A9F387',
        // color: '#A9F387'
        // borderColor: '#fff',
        borderWidth: 10,
      },
      // emphasis: {
      //   color: '#A9F387',
      // }
    },
    label: {
      normal: {
        show: true,
        position: 'top',
        color: '#A9F387'
      }
    },
    smooth: true
  }]
};

// 检查结果结论统计饼图
const pieOption = {
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)'
  },
  legend: {
    bottom: '5%',
    left: 'center',
    itemWidth: 12,
    itemHeight: 12,
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: '检查结论统计',
      type: 'pie',
      radius: ['20%', '60%'], // 使用环形图，避免标签重叠
      center: ['50%', '45%'], // 调整饼图位置，为图例留出空间
      avoidLabelOverlap: true,
      itemStyle: {
        borderRadius: 8,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}\n{c}',
        fontSize: 11,
        color: '#333',
        distanceToLabelLine: 5
      },
      labelLine: {
        show: true,
        length: 15,
        length2: 10,
        smooth: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      data: [],
      // 调整饼图颜色以匹配项目风格
      color: ['#3E73FE', '#008AFF', '#48D8BF', '#A9F387', '#FF9F40']
    }
  ]
};

// 空饼图状态的配置
const emptyPieOption = {
  ...pieOption,
  series: [
    {
      ...pieOption.series[0],
      name: '暂无数据',
      data: []
    }
  ]
};

// 空数据状态的配置
const emptyOption = {
  ...option,
  series: [{
    name: '暂无数据',
    type: 'line',
    data: [],
    label: {
      show: false
    }
  }],
  xAxis: [{
    type: 'category',
    data: [],
    axisLine: {
      lineStyle: {
        color: '#999'
      }
    },
    boundaryGap: false,
  }],
  yAxis: [{
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#DCDCDC'
      }
    },
    axisLine: {
      show: false,
      lineStyle: {
        color: '#A4A4A4'
      },
    },
    axisTick: {
      show: false
    },
    nameTextStyle: {
      color: '#A4A4A4'
    },
    splitArea: {
      show: false
    }
  }]
};

export default {
  data() {
    return {
      userInfo: {
        gender: "0", // 0: 男 1: 女
        name: "",
        age: "",
        department: "",
        workType: "",
        workYears: "",
      },
      ec: {
        option: {}
      },
      // 添加饼图ec配置
      pieEc: {
        option: {}
      },
      pickerArray: [
        // 根据检查项目动态生成
      ],
      pickerLabel: '',
      pickerIndex: 0,
      chartData: [],
      chartInstance: null,
      checkItems: [], // 存储所有检查项
      historyData: [], // 存储历史数据
      isEmpty: false, // 添加空数据状态标记
      pieIsEmpty: false, // 添加饼图空数据状态标记

      examTypeOption: [
        { value: '', label: '全部' },
        { value: '1', label: '岗前' },
        { value: '2', label: '在岗' },
        { value: '3', label: '离岗' },
      ],
      examTypeLabel: '全部',
      examTypeIndex: 0,
      // 为饼图添加独立的检查类型选择状态
      examTypeLabelForPie: '全部',
      examTypeIndexForPie: 0,
    };
  },

  async mounted() {
    try {
      // 获取基本信息
      await this.getBasicInfo();

      const res = await healthApi.reportList({ examType: this.examTypeOption[this.examTypeIndex].value });
      // 为饼图获取独立的数据
      const resForPie = await healthApi.reportList({ examType: this.examTypeOption[this.examTypeIndexForPie].value });

      if (!res.data || res.data.length === 0) {
        this.isEmpty = true;
      }

      // 处理数据,提取所有检查项和历史数据
      this.processCheckItems(res.data);

      // 处理数据，汇总所有的检查结果jobConclusion
      const pieData = this.processPie(resForPie.data)

      // 检查饼图是否有数据
      this.pieIsEmpty = !pieData || pieData.length === 0;

      // #ifdef MP-WEIXIN
      this.$refs.canvas.init(this.initChart);
      this.$refs.pieCanvas.init(this.initPieChart);
      // #endif

      // #ifdef H5
      this.initChartH5();
      this.initPieChartH5(pieData);
      // #endif

      this.getData();
    } catch (error) {
      console.error('数据获取失败:', error);
      this.isEmpty = true;
      this.pieIsEmpty = true;
    }
  },

  methods: {

    // 获取基本信息
    async getBasicInfo() {
      try {
        const res = await recordApi.getEHealthRecordBaseInfo();
        if (res.data) {
          // 更新用户信息
          this.userInfo = {
            ...this.userInfo,
            name: res.data.name || '',
            gender: res.data.gender || '0',
            age: res.data.age || '',
            department: res.data.department || '',
            workType: res.data.workType || '',
            workYears: res.data.workYears || '',
          };
        }
      } catch (error) {
        console.error('获取基本信息失败:', error);
      }
    },


    // 计算年龄
    calculateAge(birthDate) {
      const birth = new Date(birthDate);
      const today = new Date();
      let age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
      }
      return age.toString();
    },

    // 处理检查项数据
    processCheckItems(reports) {
      const historyMap = new Map(); // 用于存储每个检查项的历史数据

      // 按时间排序
      reports.sort((a, b) => new Date(a.registerTime) - new Date(b.registerTime));

      reports.forEach(report => {
        const reportDate = moment(report.registerTime).format('YYYY-MM-DD');

        report.checkDepartments.forEach(dept => {
          dept.checkProjects.forEach(project => {
            project.checkItems.forEach(item => {
              const ele = item.itemId;
              if (!ele) { return; }
              const itemKey = ele._id;

              // 如果是新的检查项，初始化数据
              if (!historyMap.has(itemKey)) {
                historyMap.set(itemKey, {
                  label: ele.projectName,
                  value: itemKey,
                  unit: ele.msrunt || '',
                  history: []
                });
              }

              // 添加历史数据
              historyMap.get(itemKey).history.push({
                year: reportDate,
                result: parseFloat(item.result)
              });
            });
          });
        });
      });

      // 直接从historyMap获取去重后的检查项数据
      this.historyData = Array.from(historyMap.values());

      // 生成checkItems，保持与原有数据结构的兼容性
      this.checkItems = this.historyData.map(item => ({
        label: item.label,
        value: item.value,
        unit: item.unit,
        result: item.history[item.history.length - 1]?.result || '' // 使用最新的检查结果
      }));

      // 更新picker选项
      this.pickerArray = this.checkItems.map(item => ({
        value: item.value,
        label: item.label
      }));

      this.pickerLabel = this.pickerArray[0]?.label || '';
    },

    // 处理检查结论
    processPie(reports) {
      // 每个报告都有jobConclusion结论数组

      // 1 目前未见异常
      // 2 复查
      // 3 疑似职业病
      // 4 禁忌证
      // 5 其他疾病或异常
      const conclusionMap = {
        '1': '目前未见异常',
        '2': '复查',
        '3': '疑似职业病',
        '4': '禁忌证',
        '5': '其他疾病或异常'
      }

      const conclusionCount = {}

      reports.forEach((item) => { // 遍历每个报告
        // 遍历每个结论
        item.jobConclusion.forEach(conclusion => {
          // 统计每个结论的数量
          conclusionCount[conclusion] = (conclusionCount[conclusion] || 0) + 1;
        });
      });

      const pieData = Object.entries(conclusionCount).map(([key, count]) => ({
        name: conclusionMap[key] || key,
        value: count
      }));

      return pieData;
    },

    // 为饼图添加独立的检查类型切换功能
    async examTypeChangeForPie(e) {
      this.examTypeIndexForPie = e.detail.value
      this.examTypeLabelForPie = this.examTypeOption[this.examTypeIndexForPie].label
      await this.updateDataForPie()
    },

    // 为饼图添加独立的数据更新功能
    async updateDataForPie() {
      const res = await healthApi.reportList({ examType: this.examTypeOption[this.examTypeIndexForPie].value });

      let pieData = [];
      if (res.data && res.data.length > 0) {
        // 处理饼图数据，汇总所有的检查结果jobConclusion
        pieData = this.processPie(res.data)
        this.pieIsEmpty = false;
      } else {
        this.pieIsEmpty = true;
      }

      // #ifdef MP-WEIXIN
      this.$refs.pieCanvas.init(this.initPieChart);
      // #endif

      // #ifdef H5
      this.initPieChartH5(pieData);
      // #endif
    },

    async updateData() {
      this.clearData()
      const res = await healthApi.reportList({ examType: this.examTypeOption[this.examTypeIndex].value });

      if (!res.data || res.data.length === 0) {
        this.isEmpty = true;
      } else {
        this.isEmpty = false;
      }
      // 处理数据,提取所有检查项和历史数据
      this.processCheckItems(res.data);
    },

    // 获取数据
    getData() {
      if (!this.checkItems.length) {
        this.isEmpty = true;
        option.dataset.source = [];
        option.series[0].name = `暂无数据`;
        // #ifdef MP-WEIXIN
        this.$refs.canvas.init(this.initChart);
        // #endif
        // #ifdef H5
        this.chartInstance.setOption(option);
        // #endif
        return;
      }

      const selectedItem = this.checkItems[this.pickerIndex];
      const historyItem = this.historyData.find(item => item.value === selectedItem.value);

      if (historyItem && historyItem.history.length > 0) {
        this.isEmpty = false;
        // 按年份排序历史数据
        historyItem.history.sort((a, b) => a.year - b.year);

        option.dataset.source = historyItem.history;
        option.series[0].name = `${selectedItem.label}(${selectedItem.unit})`;

        // #ifdef MP-WEIXIN
        this.$refs.canvas.init(this.initChart);
        // #endif

        // #ifdef H5
        this.chartInstance.setOption(option);
        // #endif
      } else {
        this.isEmpty = true;
        // #ifdef MP-WEIXIN
        this.$refs.canvas.init(this.initChart);
        // #endif

        // #ifdef H5
        this.chartInstance.setOption(emptyOption);
        // #endif
      }
    },
    // 指标切换
    bindPickerChange: function (e) {
      this.pickerIndex = e.detail.value
      this.pickerLabel = this.pickerArray[this.pickerIndex].label;
      option.series[0].name = this.pickerLabel;
      this.getData();
    },

    // 检查类型切换
    async examTypeChange(e) {
      this.examTypeIndex = e.detail.value
      this.examTypeLabel = this.examTypeOption[this.examTypeIndex].label
      await this.updateData()
      this.getData();
    },

    // 清空残留数据
    clearData() {
      this.checkItems = []
      this.pickerArray = []
      this.pickerLabel = ''
      this.historyData = []
      this.isEmpty = true;
    },

    // 小程序初始化图表 
    initChart(canvas, width, height, canvasDpr) {
      let chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: canvasDpr
      });
      canvas.setChart(chart);
      chart.setOption(this.isEmpty ? emptyOption : option);
      return chart;
    },

    // 小程序初始化饼图
    initPieChart(canvas, width, height, canvasDpr) {
      let chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: canvasDpr
      });
      canvas.setChart(chart);

      // 获取当前饼图数据
      const res = this.processPie([]);
      chart.setOption(res.length > 0 ? { ...pieOption, series: [{ ...pieOption.series[0], data: res }] } : emptyPieOption);
      return chart;
    },

    // H5
    initChartH5() {
      const dom = document.getElementById('chartContainer');
      this.chartInstance = echarts.init(dom);
      this.chartInstance.setOption(this.isEmpty ? emptyOption : option);
    },

    // H5初始化饼图
    initPieChartH5(pieData) {
      const dom = document.getElementById('pieContainer');
      if (!this.pieChartInstance) {
        this.pieChartInstance = echarts.init(dom);
      }

      const optionToSet = pieData && pieData.length > 0 ?
        { ...pieOption, series: [{ ...pieOption.series[0], data: pieData }] } :
        emptyPieOption;
      this.pieChartInstance.setOption(optionToSet);
    }

  }
};
</script>

<style scoped lang="scss">
// 页面主体样式，PC端占满宽度
.page-body {
  padding: 0 25rpx;
  width: 100%;

  // PC端样式
  /* #ifdef H5 */
  @media screen and (min-width: 768px) {
    padding: 0 40rpx;
    max-width: none;
    margin: 0;
  }
  /* #endif */
}

.chartContainer {
  width: 100%;
  height: 100%;
}

.infoCard {
  padding: 15px;
  border-radius: 10px;
  margin-top: 12.5px;
  position: relative;

  .cardTitle {
    margin-bottom: 16px;

    .titleText {
      font-weight: 600;
      font-size: 28rpx;
      line-height: 28rpx;
      letter-spacing: 0px;
      color: #555555;

      display: flex;
      justify-content: space-between;
    }

    .titlePoint {
      position: absolute;
      left: 0px;
      top: 13px;
      width: 6px;
      height: 20px;
      border-radius: 3px 3px 0px 3px;
      opacity: 1;

      background: #3E73FE;
    }
  }

  .cardItem {
    display: flex;
    margin-bottom: 12px;
    justify-content: space-between;

    font-size: 24rpx;
    font-weight: normal;
    line-height: 28rpx;
    letter-spacing: 0px;
    color: #555555;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.pieContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 32vh;
  width: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 48vh;
  width: 100%;
  position: relative;
}

.no-data-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 28rpx;
}

picker {
  .uni-input {
    color: #A8ABB2;
  }

  .uni-input::after {
    content: '>';
    display: inline-block;
    transform: rotate(90deg) scaleY(1.5) translate(0, -0.25em);
    margin-right: 1em;
  }
}
</style>
