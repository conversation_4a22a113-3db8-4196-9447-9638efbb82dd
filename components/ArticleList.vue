<template>
	<view class="grace-article-list">
		<view class="grace-article-list-item" v-for="(item, index) in articles" :key="index" @tap="newstap(item)">
			<!-- 文章标题 -->
			<!-- <view style="width:100%;">
				<text class="grace-article-list-title" 
				:style="{lineHeight:titleStyle.lineHeight, fontSize:titleStyle.fontSize, color:titleStyle.color}">{{item.title}}</text>
			</view> -->
			<!-- 图片区 -->
			<view class="grace-article-list-img1 grace-article-list-img">
				<!-- <gImg :src="item.sImg?item.sImg:'default.png' | imgPath" @error="toDefaultImg($event,index,'articles','sImg')" :width="705" :height="300"></gImg> -->
				 <image :src="item.sImg?item.sImg:'default.png' | imgPath" @error="toDefaultImg($event,index,'articles','sImg')" style="width: 100%; " mode="aspectFill"></image>
				<view class="grace-img-card-title">
					<text class="title1">—— 职业病防治法 ——</text>
					<text class="title2">{{item.title}}</text>
					<text class="title3" v-if="item.title.length < 16">{{item.stitle}}</text>
				</view>
			</view>
			<!-- <view class="grace-article-list-more-imgs" v-if="item.imageArr.length == 1">
				<view class="grace-article-list-img2-in">
					<gImg :src="item.sImg | imgPath" :width="335" :height="200"></gImg>
				</view>
				<view class="grace-article-list-img2-in">
					<gImg :src="item.imageArr[0]|imgPath | imgPath" :width="335" :height="200"></gImg>
				</view>
			</view>
			<view class="grace-article-list-more-imgs" v-if="item.imageArr.length >= 2">
				<view class="grace-article-list-img3-in">
					<gImg :src="item.sImg | imgPath" :width="220" :height="150"></gImg>
				</view>
				<view class="grace-article-list-img3-in">
					<gImg :src="item.imageArr[0] | imgPath" :width="220" :height="150"></gImg>
				</view>
				<view class="grace-article-list-img3-in">
					<gImg :src="item.imageArr[1] | imgPath" :width="220" :height="150"></gImg>
				</view>
			</view> -->
			<!-- 底部 -->
			<view class="grace-article-list-footer">
				<text class="grace-article-list-footer-items grace-icons">&#xe69e; {{item.uAuthor?item.uAuthor.name||'职卫科技':''}}</text>
				<text class="grace-article-list-footer-items grace-icons grace-text-center">&#xe609; {{item.clickNum}}</text>
				<text class="grace-article-list-footer-items grace-icons grace-text-right">&#xe64c; {{item.updateDate.slice(0,10)}}</text>
			</view>
		</view> 
	</view>
</template>
<script>
import gImg from "@/graceUI/components/graceImg.vue";
import { imgPath,randomDefaultImg } from '@/common.js';
export default{
	props:{
		categoriesTitle: String, // 文章分类名称
		articles   : {type:Array, default:function(){return [];}},
		titleStyle : {type:Object, default:function(){return {'lineHeight':'50rpx', 'fontSize':'36rpx', 'color':'#323232'};}}
	},
	methods: {
		toDefaultImg(e, index, objName, key) {
			this[objName][index][key] = randomDefaultImg(this.defaultImgList);
		},
		newstap(item, title=""){
			// 更新数据池中的当前考试一级考试结果和统计
			item.categorieName = title || this.categoriesTitle || '';
			this.$store.commit("setCurArticle", item);
			uni.navigateTo({url:"/pages_learning/pages/learning/artInfo"});
		},
	},
	filters: {
		imgPath: img => imgPath(img),
	},
	components:{gImg}
}
</script>
<style scoped lang="scss">
.grace-article-list-img{
	position: relative;
	.grace-img-card-title{
		height: 200rpx;
		position: absolute;
		width: 90%;
		left: 5%;
		top: 50%;
		transform: translateY(-50%);
		color: #fff;
		text-align: center;
		.title1{
			font-size: 24rpx;
		}
		.title2{
			font-size: 40rpx;
			line-height: 60rpx;
			font-weight: bold;
			text-shadow: 1px 3px 5px #666;
			display: block;
			margin: 15rpx 0 20rpx;
		}
		.title3{
			display: inline-block;
			line-height: 34rpx;
		}
	}
}
/* .grace-article-list{} */
.grace-article-list-item{width:100%; overflow:hidden; padding:25rpx; border-bottom:25rpx solid #F8F8F8; box-sizing:border-box;}
.grace-article-list-title{display:block; width:100%; overflow:hidden;}
.grace-article-list-img1{margin-top:15rpx;}
.grace-article-list-more-imgs{display:flex; flex-direction:row; justify-content:space-between; flex-wrap:nowrap;}
.grace-article-list-img2-in{width:335rpx; height:200rpx; padding:20rpx 0;}
.grace-article-list-img3-in{width:220rpx; height:150rpx; padding:20rpx 0;}
.grace-article-list-footer{display:flex; flex-direction:row; justify-content:space-between; padding-top:20rpx;}
.grace-article-list-footer-items{width:33.3%; line-height:50rpx; font-size:26rpx; color:#888888; display:block;}
</style>