<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <!-- 页面头部 -->
    <view slot="gHeader">
      <view class="grace-header-body">
        <!-- <text
          class="grace-header-icons grace-icons icon-scancode grace-white"
          @click="scanCode"
        ></text> -->
        <view class="grace-header-content">
          <view style="width: 100%" @click="toUrl('/pages/index/search')">
            <!-- @click="toUrl('/pages/training/courses/search')"> -->
            <graceSearch placeholder="关键字搜索"></graceSearch>
          </view>
        </view>
        <!-- #ifndef MP-WEIXIN -->
        <text
          class="grace-header-icons grace-icons icon-user grace-white"
          @tap="
            toUrl(
              hasLogin ? '/pages_user/pages/user/info' : '/pages/login/login'
            )
          "
        ></text>
        <!-- #endif -->
      </view>
    </view>
    <!-- 页面主体 -->
    <view slot="gBody">
      <!-- 轮播图 start -->
      <!-- <swiper
        class="grace-swiper swiper3"
        autoplay="true"
        indicator-color="rgba(255, 255, 255, 1)"
        indicator-active-color="#4d65f3"
        interval="3000"
      >
        <swiper-item
          class="grace-swiper-item"
          v-for="(item, index) in swiperItems"
          :key="index"
        >
          <view
            class="grace-img-in"
            @tap="
              newstap(
                item,
                categories.informationPlatform.name || '健康企业信息平台'
              )
            "
          >
            <image
              :src="item.sImg | imgPath"
              class="grace-swiper-image"
              @error="toDefaultImg($event, index,'swiperItems','sImg')"
              mode="aspectFill"
            ></image>
            <view class="grace-swiper-title" v-if="item.title">{{
              item.title
            }}</view>
          </view>
        </swiper-item>
      </swiper> -->
      <!-- <view style="padding-left:20rpx; ">
				<graceSpeaker :vertical="true" iconColor="#E76B61" :interval="5000" iconClass="grace-icons icon-speaker" :msgs="speakerMsgs" v-if="speakerMsgs.length"></graceSpeaker>
			</view> -->
      <!-- <view class="grace-common-line"></view> -->
      <!-- 背景图片 -->
      <view class="bg" style="width: 100%">
        <image src="/static/bg.png" style="width: 100%"></image>
        <view class="box">
          <view class="title"> 职业健康 </view>
          <view class="title"> 数字化平台 </view>
        </view>
      </view>
      <view class="modules-box">
        <view class="modules" v-for="(value, key) in home" :key="key">
          <view class="modules_title">
            {{ key }}
          </view>
          <view class="module">
            <view
              class="module_item"
              v-for="(item, index) in value"
              :key="item.icon"
              @click="gotoPage(item.path)"
            >
              <view class="icon-box">
                <image
                  class="icon"
                  :src="item.icon"
                  style="width: 100rpx; height: 100rpx"
                ></image>
              </view>
              <view class="item_title">{{ item.title }}</view>
            </view>
          </view>
        </view>
      </view>
      <!-- 推荐图标 -->
      <!-- 	<physicalAndCheckResult class="dialog" :dialog="dialog" :closeDailog="closeDailog">
			</physicalAndCheckResult> -->
      <graceShade
        @closeShade="closeShade"
        ref="graceShade"
        style="width: 320px"
      >
        <view class="demo-msg grace-relative" @tap.stop="" style="width: 320px">
          <view v-if="dialog" class="dialog">
            <view v-if="noCheckResult && noPhysicalResult">
              <view class="title">健康建议</view>
              <view
                class="body"
                style="text-align: center; height: 60%; line-height: 160px"
                >暂无数据</view
              >
              <view class="line"></view>
              <view class="closeBtn" @click="closeDailogFun">我知道了</view>
            </view>
            <view v-else>
              <view class="title">健康建议</view>
              <view class="body" style="height: 60%">
                <text class="name">检测结果：</text>
                <text
                  class="result"
                  style="padding: 3px 6px; background-color: #c1ffa8"
                  v-if="checkResultData"
                  >未超标</text
                >
                <view v-if="checkResultData">
                  <text class="name">健康建议：</text>
                  <text class="result">您的岗位数据无异常,请继续保持!</text>
                </view>
                <text
                  class="result"
                  style="padding: 3px 6px; background-color: #ffa8a8"
                  v-if="!noCheckResult && factors.length"
                >
                  超标
                </text>
                <view v-if="!noCheckResult && factors.length">
                  <text class="name">健康建议：</text>
                  <text class="result">
                    您所在的岗位,&nbsp;&nbsp;{{
                      factors.join("、")
                    }}&nbsp;&nbsp;危害因素超出了职业接触限值的要求,请联系管理人员做好工程防护，并正确使用个人防护用品</text
                  >
                </view>
                <text
                  class="result"
                  style="padding: 3px 6px"
                  v-if="noCheckResult"
                >
                  无检测数据
                </text>
                <br />
                <text class="name">体检结果：</text>
                <text
                  v-if="noPhysicalResult"
                  style="padding: 3px 6px"
                  class="result"
                  >暂无体检结果</text
                >
                <text
                  class="result"
                  style="padding: 3px 6px"
                  :style="[styleObject]"
                  v-if="!noPhysicalResult && tjCwithO"
                >
                  {{ tjCwithO }}</text
                >
                <br />
                <text class="name" v-if="normal === '1'">健康建议：</text>
                <text
                  class="result"
                  style="padding: 3px 6px"
                  v-if="noPhysicalResult && normal === '1'"
                >
                  请联系管理人员进行体检
                </text>
                <text
                  class="result"
                  style="padding: 3px 6px"
                  v-if="!noPhysicalResult && physicalResult && normal === '1'"
                >
                  未见异常
                </text>
                <view v-if="!noPhysicalResult && physicalResult">
                  <text class="name">健康建议：</text>
                  <text class="result">您的岗位体检数据无异常,请继续保持!</text>
                </view>

                <text
                  class="result"
                  style="padding: 3px 6px"
                  v-if="!noPhysicalResult && tjCwithO"
                >
                  <text v-if="tjCwithO === '其他疾病或异常'">
                    您的体检结果为-{{ tjCwithO }},请按照医学建议做好个人健康管理
                  </text>
                  <text v-if="tjCwithO === '复查'">
                    您的体检结果为-{{ tjCwithO }},请按照医学建议做好个人健康管理
                  </text>
                  <text v-if="tjCwithO === '禁忌证'">
                    您的体检结果为-{{
                      tjCwithO
                    }},按要求安排调岗工作，做好个人健康管理。
                  </text>
                  <text v-if="tjCwithO === '疑似职业病'">
                    您的体检结果为-{{
                      tjCwithO
                    }},请联系管理人员进行职业病诊断，做好个人健康管理。
                  </text>
                </text>
              </view>
              <view class="line"></view>
              <view class="closeBtn" @click="closeDailogFun">我知道了</view>
            </view>
          </view>
        </view>
      </graceShade>
      <view style="white-space: nowrap; width: 100%">
        <u-notice-bar
          v-if="notices && notices.length > 0"
          style="white-space: nowrap; width: 95%"
          speed="250"
          direction="column"
          mode="link"
          :text="notices.map((e) => e.content)"
          @click="handleNotice"
        ></u-notice-bar>
      </view>
      <graceShade
        @closeShade="closeShade2"
        ref="graceShade2"
        style="width: 340px"
        class=".grace-flex-vcenter"
      >
        <view class="demo-msg grace-relative" @tap.stop="" style="width: 340px">
          <view v-if="questionnaireModel" class="dialog">
            <view class="title">问卷调查</view>
            <view
              class="body"
              style="
                margin-top: 10rpx;
                text-align: center;
                height: 100rpx;
                color: rgba(136, 136, 136, 1);
              "
            >
              为了更好的协助企业管理，企业邀请您填写调查问卷</view
            >
            <view class="line"></view>
            <view class=".grace-flex-center">
              <text class="closeBtn closeKnow" @click="closeDailogFun2"
                >我知道了</text
              >
              <text class="closeBtn" @click="gotoQuestionnaire">去填写</text>
            </view>
          </view>
        </view>
      </graceShade>
      <!-- <view
        class="grace-grids grace-bg-white"
        style="margin: 8px 0px; width: 100%"
      >
        <view
          class=""
          v-for="(item, index) in indexCate"
          :key="index"
          style="width: 20%; display: flex; justify-content: center"
        >
          <text
            style="
              text-align: center;
              margin: auto;
              display: flex;
              justify-content: center;
            "
            @click="gotoPage(item.title)"
          >
            <text
              class="grace-grids-icon grace-icons"
              :class="item.icon" style="border-radius: 50%;background: linear-gradient(135deg,#699afe,#699afe);"
            ></text>
            <text class="grace-grids-text middleText">{{ item.title }}</text>
          </text>
        </view>
      </view>
      <view class="grace-common-line"></view> -->

      <view class="grace-body grace-bg-white" style="width: 97%">
        <!-- 行业动态 start -->
        <!-- <view> -->
          <!-- <view class="grace-common-line"></view> -->
          <!-- <view
            class="grace-title"
            @click="toUrl('/pages_learning/pages/learning/industryNews')"
          >
            <view class="grace-title-border"></view>
            <text
              class="grace-title-text grace-black"
              v-if="categories && categories.industryNews"
              >{{ categories.industryNews.name || "行业动态" }}</text
            >
            <view data-cateid="1">
              <text class="grace-text-small grace-gray">更多</text>
              <text
                class="grace-text-small grace-gray grace-icons icon-arrow-right icon-left-margin"
              ></text>
            </view>
          </view> -->
          <!-- <view class="grace-img-card">
            <view
              class="grace-img-card-item"
              v-for="(item, index) in industryNews"
              :key="index"
              @tap="newstap(item, '行业动态')"
            >
              <view class="grace-img-card-img">
                <image
                  :src="item.sImg | imgPath"
                  mode="aspectFill"
                  @error="toDefaultImg($event, index, 'industryNews', 'sImg')"
                  class="grace-img-card-img"
                ></image>
                <view class="grace-img-card-title">
                  <text class="title1">—— 职业病防治法 ——</text>
                  <text class="title2">{{ item.title }}</text>
                  <text class="title3" v-if="item.title.length < 15">{{
                    item.stitle
                  }}</text>
                </view>
              </view>
              <text
                class="grace-img-card-title grace-ellipsis"
                v-if="item.title.length > 14 && item.stitle"
                >{{ item.stitle }}</text
              >
              <text class="grace-img-card-title grace-ellipsis" v-else>{{
                item.simpleComments | showContent
              }}</text> -->
              <!-- <view>
								<text class="grace-img-card-price">{{item.stitle || ''}}</text>
							</view> -->
            <!-- </view> -->
          <!-- </view> -->
        <!-- </view> -->
        <!-- 行业动态 end -->

        <!-- 在线培训 start -->
        <view v-if="myTraining.length">
          <view class="grace-common-line"></view>
          <view
            class="grace-title"
            @tap="
              toUrl(
                hasTraining
                  ? '/pages_train/pages/training/myTraining'
                  : '/pages_train/pages/training/myCourses'
              )
            "
          >
            <view class="grace-title-border"></view>
            <text class="grace-title-text grace-black">职业卫生培训</text>
            <view data-cateid="2">
              <text class="grace-text-small grace-gray">更多</text>
              <text
                class="grace-text-small grace-gray grace-icons icon-arrow-right icon-left-margin"
              ></text>
            </view>
          </view>
          <view class="grace-img-card">
            <view
              class="grace-img-card-item"
              @tap="
                toUrl('/pages/training/detail?personalTrainingId=' + item._id)
              "
              v-for="item in myTraining"
              :key="item._id"
            >
              <view class="grace-img-card-img">
                <image
                  :src="
                    (item.firstCourseDetail
                      ? item.firstCourseDetail.cover
                      : null) | imgPath
                  "
                  @error="
                    toDefaultImg(
                      $event,
                      index,
                      'myTraining',
                      'firstCourseDetail.cover'
                    )
                  "
                  mode="aspectFill"
                  class="grace-img-card-img"
                ></image>
              </view>
              <text class="grace-img-card-title grace-ellipsis">{{
                item.adminTrainingId
                  ? item.adminTrainingId.name
                  : item.employeesTrainingPlanId
                  ? item.employeesTrainingPlanId.name
                  : ""
              }}</text>
            </view>
          </view>
        </view>
        <!-- 在线培训 end -->
      </view>
      <!-- 占位视图 用于避开底部遮盖 -->
      <view style="height: 120rpx"></view>

      <!-- 林卉桐--防护用品领用确认弹框 -->
      <graceDialog
        closeBtnColor="#999"
        ref="graceDialog"
        @closeDialog="closeDialog2"
      >
        <view class="content2" slot="content">
          <text>请填写您要领取的数量</text>
          <br />
          <view style="height: 50px; line-height: 50px; white-space: nowrap"
            >口罩：
            <input
              class="uni-input"
              type="number"
              placeholder="数量"
              @blur="getPpeNum($event.detail.value, 'quantity_k')"
              style="
                display: inline-block;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                padding: 2px 10px;
                margin-right: 8px;
                width: 50%;
              "
            />&nbsp;&nbsp;个
          </view>
          <br />
          <view
            >耳塞：<input
              class="uni-input"
              type="number"
              placeholder="数量"
              @blur="getPpeNum($event.detail.value, 'quantity_e')"
              style="
                display: inline-block;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                padding: 2px 10px;
                margin-right: 8px;
                width: 50%;
              "
            />&nbsp;&nbsp;个</view
          >
          <!-- <text>{{PPE.info}}</text> -->
          <br />
          <button
            @tap="createCanvas"
            type="success"
            style="
              background-color: #4c84ff;
              border-radius: 4px;
              color: #fff !important;
              margin-left: 15%;
            "
          >
            签字确认
          </button>
        </view>
      </graceDialog>
      <view class="signature" v-show="showCanvas">
        <canvas
          class="mycanvas"
          canvas-id="mycanvas"
          @touchstart="touchstart"
          @touchmove="touchmove"
          @touchend="touchend"
        ></canvas>
        <view class="footer">
          <view class="left" @click="finish">保存</view>
          <view class="right" @click="clear">清除</view>
          <view class="close" @click="close">关闭</view>
        </view>
      </view>
    </view>
  </gracePage>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import learningApi from "@/api/learning.js";
import trainingApi from "@/api/training.js";
import { imgPath, randomDefaultImg } from "@/common.js";
import employeeApi from "@/api/employee.js"; //导入接口
import questionnaireApi from "@/api/questionnaire.js"; //导入接口
import graceDialog from "@/graceUI/components/graceDialog.vue";
import config from "@/common.js";
import physicalAndCheckResult from "@/pages_user/pages/user/physicalAndCheckResult.vue";
import userApi from "@/api/user.js";
import graceRightMessage from "@/graceUI/components/graceRightMessage.vue";
import { nextTick } from "vue";
export default {
  data() {
    return {
      defaultImgList: [
        "/static/upload/enterprise/jnFczjlqoZ/1708889211779236666.png",
      ],
      noticeArr: [],
      questionnaireModel: false,
      msgAimate: true,
      // ctx:{},
      PPE: {
        info: "本柜您可领取",
        data: "", // 需要出货的防护用品
        quantity_k: "", //口罩数量
        quantity_e: "", // 耳塞数量
        codeId: "", // 终端id
      },
      points: [],
      showCanvas: false,
      PPEId: [],
      swiperItems: [], // 轮播图
      indexCate: [
        {
          path: "/pages_user/pages/user/physicalAndCheckResult",
          title: "健康建议",
          icon: "icon-share orange one",
        },
        {
          path: "/pages_user/pages/user/diseasesOverhaul",
          title: "检修维护",
          icon: "icon-phone2 orange one",
        },
        {
          path: "/pages_user/pages/user/physicalExamination",
          title: "体检信息",
          icon: "icon-safe orange one",
        },
        {
          path: "/pages_user/pages/user/info",
          title: "岗位信息",
          icon: "icon-menu two",
        },
        {
          path: "/pages_user/pages/user/ppe",
          title: "防护用品",
          icon: "icon-article four",
        },
        // {
        //   path: "/pages_user/pages/user/comment",
        //   title: "反馈建议",
        //   icon: "icon-comments three",
        // },
      ],
      home: {},
      records: {
        电子健康档案: [
          {
            path: "",
            title: "基本信息",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "体检记录",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "诊断医疗",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "重点人群",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "工伤保险",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "卫生服务",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "档案授权",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "危害检测", // pages/eHealthRecord/harmFactor
            icon: "/static/home/<USER>",
          },
        ],
        职业健康检查: [
          {
            path: "",
            title: "体检机构",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "体检预约",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "预约记录",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "报告查询",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "授权查询",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "检查预警",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "检查统计",
            icon: "/static/home/<USER>",
          },
        ],
        "职业病诊断/鉴定申请": [
          {
            path: "",
            title: "诊断预约",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "诊断记录",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "鉴定申请",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "鉴定记录",
            icon: "/static/home/<USER>",
          },
					{
					  path: "",
					  title: "工伤认定",
					  icon: "/static/home/<USER>",
					},
        ],
        跨区域中毒救治: [
          {
            path: "",
            title: "远程会诊",
            icon: "/static/home/<USER>",
          },
          {
            path: "",
            title: "远程门诊",
            icon: "/static/home/<USER>",
          },
        ],
      },
      industryNews: [], // 行业动态
      myTraining: [], // 在线培训
      top: 0,
      speakerMsgs: [
        // { title: "您2021-03-03的体检结果已经出来的，请及时查收", url:"../index/index", opentype: "navigate"},
        // { title: "试卷已发送，请及时完成", url: "../index/index", opentype: "switchTab"}
      ],
      EnterpriseID: "",
      hasGetNews: false, // 已经获取新闻信息了
      hasTraining: false, // 是否有培训（管理员培训和员工培训）
      dialog: false,
      checkResultData: false, //检测结果未异常
      physicalResult: false, // 体检结果无异常
      noCheckResult: true, //没有检测数据
      noPhysicalResult: true, //没有体检数据
      //体检结果的各种[疑似职业病,复查,禁忌证,其他疾病或异常]
      factors: [], // 超标的因素
      noData: false, // 体检和检测都没有数据

      styleObject: {
        backgroundColor: "#C1FFA8",
      },
      tjCwithO: "",
      normal: "1",
      hasInfo: false,
    };
  },
  computed: {
    ...mapGetters(["userInfo", "hasLogin", "categories", "notices", "homeNav"]),
    fullRecords() {
      const baseRecords = this.records;
      if (this.userInfo && this.userInfo.hasOccupationalDisease) {
        return {
          ...baseRecords,
          全生命周期: [
            {
              path: "",
              title: "随访记录",
              icon: "/static/home/<USER>",
            },
            {
              path: "",
              title: "就诊预约",
              icon: "/static/home/<USER>",
            },
            {
              path: "",
              title: "诊疗服务",
              icon: "/static/home/<USER>",
            },
            {
              path: "",
              title: "用药服务",
              icon: "/static/home/<USER>",
            },
            {
              path: "",
              title: "康复指导",
              icon: "/static/home/<USER>",
            },
          ],
        };
      }
      return baseRecords;
    },
  },
  watch: {
    categories(newVal) {
      if (newVal && !this.hasGetNews) {
        this.getSwiperItems(); // 健康企业信息平台 文章
        this.getIndustryNews(); // 行业动态 文章
      }
    },
    userInfo(newVal) {
      if (newVal && !this.hasGetNews) {
        this.hasLogin && this.getTrainingList();
      }
      // Update home when userInfo changes
      Object.keys(this.fullRecords).forEach((key, index) => {
        this.$set(this.home, key, []);
        this.homeNav.forEach((nav) => {
          this.fullRecords[key].forEach((item) => {
            if (item.title === nav.title) {
              this.home[key].push(nav);
            }
          });
        });
      });

      Object.keys(this.home).forEach((key) => {
        const item = this.home[key];
        if (item.length <= 0) {
          delete this.home[key];
        }
      });
    },
    homeNav() {
      Object.keys(this.fullRecords).forEach((key, index) => {
        this.$set(this.home, key, []);
        this.homeNav.forEach((nav) => {
          this.fullRecords[key].forEach((item) => {
            if (item.title === nav.title) {
              this.home[key].push(nav);
            }
          });
        });
      });

      Object.keys(this.home).forEach((key) => {
        const item = this.home[key];
        if (item.length <= 0) {
          delete this.home[key];
        }
      });
    },
    dialog(valold, valnew) {
      if (valold) {
        this.getPhysicalAndCheckResult();
      }
    },
  },
  async mounted() {
    Object.keys(this.fullRecords).forEach((key, index) => {
      this.$set(this.home, key, []);
      this.homeNav.forEach((nav) => {
        this.fullRecords[key].forEach((item) => {
          if (item.title === nav.title) {
            this.home[key].push(nav);
            console.log(111, this.home[key]);
          }
        });
      });
    });

    Object.keys(this.home).forEach((key) => {
      const item = this.home[key];
      if (item.length <= 0) {
        delete this.home[key];
      }
    });
    if (this.categories) {
      this.getSwiperItems(); // 健康企业信息平台 文章
      this.getIndustryNews(); // 行业动态 文章
    }
    if (
      this.userInfo.companyStatus == 2 &&
      this.userInfo.companyId &&
      this.userInfo.companyId.length
    ) {
      this.EnterpriseID =
        this.userInfo.companyId[this.userInfo.companyId.length - 1] || "";
    }
    if (this.hasLogin) this.getTrainingList();
    if (this.hasLogin) {
      this.getPhysicalAndCheckResult();
    }
    const res = await questionnaireApi.getPersonalQuestionnaire({});
    if (res.status === 200) {
      this.questionnaireList = res.data;
      const hasPending = res.data.some((item) => item.isFilled === "待填写");
      if (hasPending) {
        console.log(123);
        this.questionnaireModel = true;
        this.$refs.graceShade2.showIt();
      } else {
        this.questionnaireModel = false;
      }
    }
  },
  methods: {
    goRemote() {
      uni.navigateTo({
        url: "/pages_remote/pages/remote/list",
      });
    },
    toDefaultImg(e, index, objName, key) {
      this[objName][index][key] = randomDefaultImg(this.defaultImgList);
    },
    handleNotice(index) {
      const target = this.notices[index];
      if (target.type === "reorientation") {
        uni.navigateTo({
          url: `/pages/reorientation/reorientation?id=${target.id}&employeeId=${this.userInfo.employeeId}`,
        });
      }
    },
    closeDailog() {
      this.dialog = false;
    },
    async getPhysicalAndCheckResult() {
      let companyID;
      if (this.userInfo.companyId && this.userInfo.companyId.length) {
        companyID = this.userInfo.companyId[0];
      }
      const userInfo = JSON.parse(JSON.stringify(this.userInfo));
      userInfo.companyId = companyID;

      const result = await userApi.getLastPhysicalDataAndDeteData(userInfo);
      let physicalData = result.data.res; //体检结果
      let checkResult = result.data.res2; //检测结果
      if (
        (physicalData && physicalData.length) ||
        (checkResult && checkResult.length)
      ) {
        this.hasInfo = true;
      }
      this.tjCwithO = ""; //体检诊断的病名
      if (physicalData && physicalData.length) {
        let tjTime = physicalData[0].checkDate;
        if (tjTime && new Date(tjTime).getFullYear() === 2023) {
          this.noPhysicalResult = false; //此时有体检结果
          this.tjCwithO = physicalData[0].CwithO;
        } else {
          this.noPhysicalResult = true;
        }
      } else {
        //此时没有体检结果
        this.noPhysicalResult = true;
      }
      if (this.tjCwithO && this.tjCwithO === "疑似职业病") {
        // 页面上不同病名需要有不同的颜色显示
        this.styleObject = {
          backgroundColor: "#FFDAA8",
        };
      }
      if (this.tjCwithO && this.tjCwithO === "禁忌证") {
        this.styleObject = {
          backgroundColor: "#FFA8A8",
        };
      }
      if (this.tjCwithO && this.tjCwithO === "复查") {
        this.styleObject = {
          backgroundColor: "#FFDAA8",
        };
      }
      if (this.tjCwithO && this.tjCwithO === "其他疾病或异常") {
        this.styleObject = {
          backgroundColor: "#FFDAA8",
        };
      }
      if (this.tjCwithO && this.tjCwithO === "目前未见异常") {
        this.physicalResult = true; //体检结果无异常
        this.normal = "2";
        this.styleObject = {
          backgroundColor: "#C1FFA8",
        };
      }
      let arr = [];
      if (checkResult && checkResult.length) {
        for (let i = 0; i < checkResult.length; i++) {
          for (let k = 0; k < checkResult[i].data.length; k++) {
            let time = checkResult[i].data[k].checkTime;
            if (time && new Date(time).getFullYear() === 2023) {
              this.noCheckResult = false; //此时有检测数据
              if (checkResult[i].data[k].percent > 1) {
                this.factors.push(checkResult[i].data[k].checkProject);
              }
            }
            for (let p = 0; p < checkResult[i].data.length; p++) {
              let temp = checkResult[i].data[p];
              if (temp.percent > 1) {
                arr.push(temp);
              }
            }
          }
        }
        setTimeout(() => {
          if (arr.length == 0) {
            this.checkResultData = true;
          }
        }, 1);
        this.factors = Array.from(new Set(this.factors));
      } else {
        this.noCheckResult = true; //此时没有检测数据
      }
    },
    closeShade: function () {
      this.$refs.graceShade.hideIt();
    },
    closeShade2: function () {
      this.$refs.graceShade2.hideIt();
    },
    closeDailogFun() {
      this.dialog = false;
      this.$refs.graceShade.hideIt();
    },
    closeDailogFun2() {
      this.questionnaireModel = false;
      this.$refs.graceShade2.hideIt();
    },
    gotoPage(path) {
      if (!path) return;
      console.log("aaaa", path);
      uni.navigateTo({
        url: path,
      });
    },
    scanCode() {
      this.PPE.info = "本柜您可领取";
      this.PPE.PPEId = [];
      const userInfo = this.userInfo;
      if (!userInfo._id) {
        uni.showToast({
          title: "您还未登录，请前往用户登录",
          icon: "none",
          duration: 3000,
        });
        setTimeout(function () {
          uni.navigateTo({
            url: "/pages/login/login",
          });
        }, 1000);
      }
      const that = this;
      uni.scanCode({
        scanType: ["barCode", "qrCode"],
        success: async function (res) {
          if (res.result === "863488050210916") {
            that.$refs.graceDialog.open();
            console.log(
              res.result,
              that.PPE.quantity_k,
              that.PPE.quantity_e,
              "机器的id----------"
            );
            const id = res.result;
            that.PPE.codeId = res.result;
          }
        },
        fail: function (err) {
          uni.showToast({
            title: err,
            icon: "none",
          });
        },
      });
    },
    getPpeNum(val, name) {
      if (name === "quantity_k") {
        this.PPE.quantity_k = val;
      } else if (name === "quantity_e") {
        this.PPE.quantity_e = val;
      }
    },
    closeDialog2: function () {
      this.$refs.graceDialog.hide();
    },
    graceDialog() {
      this.$refs.graceDialog.hide();
    },
    close: function () {
      this.showCanvas = false;
      this.clear();
    },
    createCanvas: function () {
      this.showCanvas = true;
      this.ctx = uni.createCanvasContext("mycanvas", this);
      this.ctx.lineWidth = 4;
      this.ctx.lineCap = "round";
      this.ctx.lineJoin = "round";
    },
    touchstart: function (e) {
      let startX = e.changedTouches[0].x;
      let startY = e.changedTouches[0].y;
      let startPoint = {
        X: startX,
        Y: startY,
      };
      this.points.push(startPoint);
      this.ctx.beginPath();
    },
    touchmove: function (e) {
      let moveX = e.changedTouches[0].x;
      let moveY = e.changedTouches[0].y;
      let movePoint = {
        X: moveX,
        Y: moveY,
      };
      this.points.push(movePoint);
      let len = this.points.length;
      if (len >= 2) {
        this.draw();
      }
    },
    touchend: function () {
      this.points = [];
    },
    draw: function () {
      let point1 = this.points[0];
      let point2 = this.points[1];
      this.points.shift();
      this.ctx.moveTo(point1.X, point1.Y);
      this.ctx.lineTo(point2.X, point2.Y);
      this.ctx.stroke();
      this.ctx.draw(true);
    },
    clear: function () {
      let that = this;
      uni.getSystemInfo({
        success: function (res) {
          let canvasw = res.windowWidth;
          let canvash = res.windowHeight;
          that.ctx.clearRect(0, 0, canvasw, canvash);
          that.ctx.draw(true);
        },
      });
    },
    finish: function () {
      let that = this;
      uni.canvasToTempFilePath(
        {
          canvasId: "mycanvas",
          fail: (err) => {},
          success: function (res) {
            uni.uploadFile({
              url: config.apiServer + "app/user/ppeSign",
              filePath: res.tempFilePath,
              name: "file",
              formData: {
                EnterpriseID: that.userInfo.companyId.length
                  ? that.userInfo.companyId[0]
                  : "",
                id: JSON.stringify(that.PPEId),
                num: JSON.stringify({
                  quantity_k: that.PPE.quantity_k,
                  quantity_e: that.PPE.quantity_e,
                }),
                userId: that.userInfo._id,
              },
              fail: (err) => {
                console.log("上传文件错误", err);
              },
              success: async (uploadFileRes) => {
                console.log(uploadFileRes, "文件上传结果");
                if (uploadFileRes.statusCode === 200) {
                  console.log("个人防护用品签字====");
                  that.clear();
                  that.showCanvas = false;
                  const result = await employeeApi.ppeCabinetQRcode({
                    data: JSON.stringify(that.PPE.data),
                    id: that.PPE.codeId,
                    num: {
                      quantity_k: that.PPE.quantity_k,
                      quantity_e: that.PPE.quantity_e,
                    },
                    companyId: that.userInfo.companyId[0],
                  });
                  console.log(result, "????????");
                  if (result.status === 200) {
                    uni.showToast({
                      title: "出货成功",
                      icon: "success",
                    });
                  } else {
                    uni.showToast({
                      title: "出货失败:" + result.message,
                      icon: "warn",
                    });
                  }
                }
              },
            });
            that.$refs.graceDialog.hide();
          },
        },
        this
      );
    },
    getSwiperItems() {
      this.categories.informationPlatform &&
        learningApi
          .list({
            size: 3,
            pageCurrent: 1,
            categories: this.categories.informationPlatform.id,
            isTop: 1,
          })
          .then((res) => {
            this.hasGetNews = true;
            this.swiperItems = res.data;
          });
    },
    getIndustryNews() {
      this.categories.industryNews &&
        learningApi
          .list({
            size: 4,
            pageCurrent: 1,
            categories: this.categories.industryNews.id,
          })
          .then((res) => {
            this.industryNews = res.data;
          });
    },
    newstap(item, title = "") {
      item.categorieName = title;
      this.$store.commit("setCurArticle", item);
      uni.navigateTo({
        url: "/pages_learning/pages/learning/artInfo",
      });
    },
    async getTrainingList() {
      if (!this.userInfo._id) return;
      const query = {
        size: 4,
        pageCurrent: 1,
        userId: this.userInfo._id,
        completeState: "false",
        trainingType: 2,
      };
      if (this.EnterpriseID) delete query.trainingType;
      trainingApi.personalTrainingList(query).then((res) => {
        this.myTraining = res.data.res || [];
        this.hasTraining = this.myTraining.some(
          (ele) => ele.trainingType !== 2
        );
      });
    },
    toUrl(url) {
      uni.navigateTo({
        url,
      });
    },
    gotoQuestionnaire() {
      uni.navigateTo({
        url: "/pages_user/pages/user/questionnaire",
      });
    },
  },
  filters: {
    imgPath: (img) => imgPath(img),
    showContent(simpleComments) {
      if (JSON.parse(simpleComments)[0].type == "contents") {
        return JSON.parse(simpleComments)[0].content;
      } else {
        return JSON.parse(simpleComments)[1].content || "";
      }
    },
  },
  components: {
    graceDialog,
    physicalAndCheckResult,
    graceRightMessage,
  },
  onLoad: function (options) {
    console.log(options, "是否有问卷");
  },
};
</script>
<style lang="scss" scoped>
.grace-img-card-img {
  position: relative;

  .grace-img-card-title {
    position: absolute;
    height: 280rpx;
    width: 86%;
    left: 7%;
    top: 50%;
    transform: translateY(-50%);
    color: #fff;
    text-align: center;

    .title1 {
      font-size: 24rpx;
    }

    .title2 {
      font-size: 40rpx;
      line-height: 60rpx;
      font-weight: bold;
      text-shadow: 1px 3px 5px #666;
      display: block;
      margin: 15rpx 0 20rpx;
    }

    .title3 {
      display: inline-block;
      line-height: 34rpx;
    }
  }
}

.grace-swiper {
  height: 300rpx;
}

.grace-swiper-item {
  height: 280rpx;
}

.swiper-image {
  width: 700rpx;
  height: 280rpx;
}

.grace-swiper.swiper3 {
  height: 360rpx;
}

/* 九宫格个性修饰 */
.grace-grids .grace-icons {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  color: #fff;
}

.grace-grids .grace-icons.one {
  background-color: #8dc63f;
}

.grace-grids .grace-icons.three {
  background-color: #008cff;
}

.grace-grids .grace-icons.two {
  background-color: #6739b6;
}

.grace-grids .grace-icons.four {
  background-color: #f37b1d;
}

.grace-grids-items {
  width: 20%;
  padding: 25rpx 0;
  box-sizing: border-box;
}

.grace-grids-image {
  width: 80rpx;
  height: 80rpx;
}

.grace-grids-icon {
  font-size: 52rpx;
}

.grace-grids .grace-icons {
  display: flex !important;
  align-items: bottom;
  justify-content: center;
  flex-direction: column;
}

/* 产品列表修饰 */
.grace-img-card-img {
  height: 340rpx;
  background-color: #f5f5f5;
}

.grace-img-card-price {
  margin-right: 20rpx;
}

/* 签字的 */
.webView {
  margin-top: 30px;
}

.signature {
  position: fixed;
  top: 10px;
  left: 2%;
  z-index: 999;
  width: 96%;
}

page {
  background: #fff;
}

.container {
  padding: 20rpx 0 120rpx 0;
  box-sizing: border-box;
}

.title {
  height: 50upx;
  line-height: 50upx;
  font-size: 16px;
}

.middleText {
  color: #636568;
  font-size: 16rpx;
}

.dialog {
  width: 70%;
  border-radius: 8px;
  background-color: white;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  padding: 0 20px;
  z-index: 999;
}

.title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  height: 40px;
  line-height: 40px;
  padding-top: 10px;
}

.name {
  font-size: 16px;
  line-height: 30px;
}

.result {
  line-height: 30px;
  font-size: 16px;
}

.closeBtn {
  text-align: center;
  padding: 20px;
  color: #576b95;
}

.line {
  padding-top: 20px;
  height: 1px;
  background-color: none;
  box-shadow: 0 0.5px 0 #eee;
}

.closeKnow {
  color: rgba(136, 136, 136, 1);
}

@import url("./home.scss");
</style>