!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,function(t){"use strict";"undefined"!=typeof window?N=window.__DEV__:"undefined"!=typeof global&&(N=global.__DEV__);var I=N=void 0===N?!0:N,E=2311;var y="object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:function(t){var e={},n=t.match(/Firefox\/([\d.]+)/),i=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge\/([\d.]+)/),t=/micromessenger/i.test(t);n&&(e.firefox=!0,e.version=n[1]);i&&(e.ie=!0,e.version=i[1]);o&&(e.edge=!0,e.version=o[1]);t&&(e.weChat=!0);return{browser:e,os:{},node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!e.ie&&!e.edge,pointerEventsSupported:"onpointerdown"in window&&(e.edge||e.ie&&11<=e.version),domSupported:"undefined"!=typeof document}}(navigator.userAgent);var z={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},B={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},R=Object.prototype.toString,N=Array.prototype,F=N.forEach,H=N.filter,V=N.slice,G=N.map,W=N.reduce,U={};function q(t,e){"createCanvas"===t&&(Z=null),U[t]=e}function M(t){if(null==t||"object"!=typeof t)return t;var e=t,n=R.call(t);if("[object Array]"===n){if(!mt(t))for(var e=[],i=0,o=t.length;i<o;i++)e[i]=M(t[i])}else if(B[n]){if(!mt(t)){var r=t.constructor;if(t.constructor.from)e=r.from(t);else{e=new r(t.length);for(i=0,o=t.length;i<o;i++)e[i]=M(t[i])}}}else if(!z[n]&&!mt(t)&&!at(t))for(var a in e={},t)t.hasOwnProperty(a)&&(e[a]=M(t[a]));return e}function g(t,e,n){if(!A(e)||!A(t))return n?M(e):t;for(var i in e){var o,r;e.hasOwnProperty(i)&&(o=t[i],!A(r=e[i])||!A(o)||b(r)||b(o)||at(r)||at(o)||ot(r)||ot(o)||mt(r)||mt(o)?!n&&i in t||(t[i]=M(e[i])):g(o,r,n))}return t}function j(t,e){for(var n=t[0],i=1,o=t.length;i<o;i++)n=g(n,t[i],e);return n}function C(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function T(t,e,n){for(var i in e)e.hasOwnProperty(i)&&(n?null!=e[i]:null==t[i])&&(t[i]=e[i]);return t}var Z,K=function(){return U.createCanvas()};function $(){return Z=Z||K().getContext("2d")}function m(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function Q(t,e){var n,i=t.prototype;function o(){}for(n in o.prototype=e.prototype,t.prototype=new o,i)i.hasOwnProperty(n)&&(t.prototype[n]=i[n]);(t.prototype.constructor=t).superClass=e}function i(t,e,n){T(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,n)}function J(t){if(t)return"string"!=typeof t&&"number"==typeof t.length}function D(t,e,n){if(t&&e)if(t.forEach&&t.forEach===F)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,o=t.length;i<o;i++)e.call(n,t[i],i,t);else for(var r in t)t.hasOwnProperty(r)&&e.call(n,t[r],r,t)}function S(t,e,n){if(t&&e){if(t.map&&t.map===G)return t.map(e,n);for(var i=[],o=0,r=t.length;o<r;o++)i.push(e.call(n,t[o],o,t));return i}}function tt(t,e,n,i){if(t&&e){if(t.reduce&&t.reduce===W)return t.reduce(e,n,i);for(var o=0,r=t.length;o<r;o++)n=e.call(i,n,t[o],o,t);return n}}function et(t,e,n){if(t&&e){if(t.filter&&t.filter===H)return t.filter(e,n);for(var i=[],o=0,r=t.length;o<r;o++)e.call(n,t[o],o,t)&&i.push(t[o]);return i}}function nt(t,e){var n=V.call(arguments,2);return function(){return t.apply(e,n.concat(V.call(arguments)))}}function l(t){var e=V.call(arguments,1);return function(){return t.apply(this,e.concat(V.call(arguments)))}}function b(t){return"[object Array]"===R.call(t)}function it(t){return"function"==typeof t}function k(t){return"[object String]"===R.call(t)}function A(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function ot(t){return!!z[R.call(t)]}function rt(t){return!!B[R.call(t)]}function at(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function st(t){return t!=t}function lt(t){for(var e=0,n=arguments.length;e<n;e++)if(null!=arguments[e])return arguments[e]}function ht(t,e){return null!=t?t:e}function ut(t,e,n){return null!=t?t:null!=e?e:n}function ct(){return Function.call.apply(V,arguments)}function dt(t){var e;return"number"==typeof t?[t,t,t,t]:2===(e=t.length)?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function p(t,e){if(!t)throw new Error(e)}function ft(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}U.createCanvas=function(){return document.createElement("canvas")};var pt="__ec_primitive__";function gt(t){t[pt]=!0}function mt(t){return t[pt]}function yt(t){var n=b(t),i=(this.data={},this);function e(t,e){n?i.set(t,e):i.set(e,t)}t instanceof yt?t.each(e):t&&D(t,e)}function P(t){return new yt(t)}function vt(){}yt.prototype={constructor:yt,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){for(var n in void 0!==e&&(t=nt(t,e)),this.data)this.data.hasOwnProperty(n)&&t(this.data[n],n)},removeKey:function(t){delete this.data[t]}};var _t=Object.freeze({__proto__:null,$override:q,assert:p,bind:nt,clone:M,concatArray:function(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];for(var o=t.length,i=0;i<e.length;i++)n[i+o]=e[i];return n},createCanvas:K,createHashMap:P,curry:l,defaults:T,each:D,eqNaN:st,extend:C,filter:et,find:function(t,e,n){if(t&&e)for(var i=0,o=t.length;i<o;i++)if(e.call(n,t[i],i,t))return t[i]},getContext:$,indexOf:m,inherits:Q,isArray:b,isArrayLike:J,isBuiltInObject:ot,isDom:at,isFunction:it,isObject:A,isPrimitive:mt,isString:k,isTypedArray:rt,map:S,merge:g,mergeAll:j,mixin:i,noop:vt,normalizeCssArray:dt,reduce:tt,retrieve:lt,retrieve2:ht,retrieve3:ut,setAsPrimitive:gt,slice:ct,trim:ft}),xt="undefined"==typeof Float32Array?Array:Float32Array;function wt(t,e){var n=new xt(2);return null==e&&(e=0),n[0]=t=null==t?0:t,n[1]=e,n}function bt(t,e){return t[0]=e[0],t[1]=e[1],t}function St(t){var e=new xt(2);return e[0]=t[0],e[1]=t[1],e}function Mt(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Ct(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t}function It(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Tt(t){return Math.sqrt(Dt(t))}function Dt(t){return t[0]*t[0]+t[1]*t[1]}function kt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function At(t,e){var n=Tt(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Pt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Ot=Pt;function Lt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Et=Lt;function zt(t,e,n){var i=e[0],e=e[1];return t[0]=n[0]*i+n[2]*e+n[4],t[1]=n[1]*i+n[3]*e+n[5],t}function Bt(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function Rt(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}N=Object.freeze({__proto__:null,add:Mt,applyTransform:zt,clone:St,copy:bt,create:wt,dist:Ot,distSquare:Et,distance:Pt,distanceSquare:Lt,div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},len:Tt,lenSquare:Dt,length:Tt,lengthSquare:Dt,lerp:function(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t},max:Rt,min:Bt,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:At,scale:kt,scaleAndAdd:Ct,set:function(t,e,n){return t[0]=e,t[1]=n,t},sub:It});function Nt(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this)}function Ft(t,e){return{target:t,topTarget:e&&e.topTarget}}Nt.prototype={constructor:Nt,_dragStart:function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(Ft(e,t),"dragstart",t.event))},_drag:function(t){var e,n,i,o,r=this._draggingTarget;r&&(e=t.offsetX,n=t.offsetY,i=e-this._x,o=n-this._y,this._x=e,this._y=n,r.drift(i,o,t),this.dispatchToElement(Ft(r,t),"drag",t.event),i=this.findHover(e,n,r).target,o=this._dropTarget,r!==(this._dropTarget=i))&&(o&&i!==o&&this.dispatchToElement(Ft(o,t),"dragleave",t.event),i)&&i!==o&&this.dispatchToElement(Ft(i,t),"dragenter",t.event)},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(Ft(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(Ft(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};function Ht(t){this._$handlers={},this._$eventProcessor=t}var Vt=Array.prototype.slice;function Gt(t,e,n,i,o,r){var a=t._$handlers;if("function"==typeof n&&(o=i,i=n,n=null),i&&e){h=n,l=(l=t)._$eventProcessor,n=h=null!=h&&l&&l.normalizeQuery?l.normalizeQuery(h):h,a[e]||(a[e]=[]);for(var s=0;s<a[e].length;s++)if(a[e][s].h===i)return t;var l={h:i,one:r,query:n,ctx:o||t,callAtLast:i.zrEventfulCallAtLast},h=a[e].length-1,r=a[e][h];r&&r.callAtLast?a[e].splice(h,0,l):a[e].push(l)}return t}Ht.prototype={constructor:Ht,one:function(t,e,n,i){return Gt(this,t,e,n,i,!0)},on:function(t,e,n,i){return Gt(this,t,e,n,i,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var n=this._$handlers;if(t)if(e){if(n[t]){for(var i=[],o=0,r=n[t].length;o<r;o++)n[t][o].h!==e&&i.push(n[t][o]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];else this._$handlers={};return this},trigger:function(t){var e=this._$handlers[t],n=this._$eventProcessor;if(e)for(var i=arguments,o=i.length,r=(3<o&&(i=Vt.call(i,1)),e.length),a=0;a<r;){var s=e[a];if(n&&n.filter&&null!=s.query&&!n.filter(t,s.query))a++;else{switch(o){case 1:s.h.call(s.ctx);break;case 2:s.h.call(s.ctx,i[1]);break;case 3:s.h.call(s.ctx,i[1],i[2]);break;default:s.h.apply(s.ctx,i)}s.one?(e.splice(a,1),r--):a++}}return n&&n.afterTrigger&&n.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],n=this._$eventProcessor;if(e)for(var i=arguments,o=i.length,r=(i=4<o?Vt.call(i,1,i.length-1):i)[i.length-1],a=e.length,s=0;s<a;){var l=e[s];if(n&&n.filter&&null!=l.query&&!n.filter(t,l.query))s++;else{switch(o){case 1:l.h.call(r);break;case 2:l.h.call(r,i[1]);break;case 3:l.h.call(r,i[1],i[2]);break;default:l.h.apply(r,i)}l.one?(e.splice(s,1),a--):s++}}return n&&n.afterTrigger&&n.afterTrigger(t),this}};var Wt=Math.log(2);function Xt(t,e,n,i,o,r){var a,s=i+"-"+o,l=t.length;if(r.hasOwnProperty(s))return r[s];if(1===e)return a=Math.round(Math.log((1<<l)-1&~o)/Wt),t[n][a];for(var h=i|1<<n,u=n+1;i&1<<u;)u++;for(var c=0,d=0,f=0;d<l;d++){var p=1<<d;p&o||(c+=(f%2?-1:1)*t[n][d]*Xt(t,e-1,u,h,o|p,r),f++)}return r[s]=c}function Yt(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},o=Xt(n,8,0,0,0,i);if(0!==o){for(var r=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==r[s]&&(r[s]=0),r[s]+=((a+s)%2?-1:1)*Xt(n,7,0===a?1:0,1<<a,1<<s,i)/o*e[a];return function(t,e,n){var i=e*r[6]+n*r[7]+1;t[0]=(e*r[0]+n*r[1]+r[2])/i,t[1]=(e*r[3]+n*r[4]+r[5])/i}}}var Ut="___zrEVENTSAVED",qt=[];function jt(t,e,n,i,o){if(e.getBoundingClientRect&&y.domSupported&&!Zt(e)){var r=e[Ut]||(e[Ut]={}),e=function(t,e,n){for(var i=n?"invTrans":"trans",o=e[i],r=e.srcCoords,a=!0,s=[],l=[],h=0;h<4;h++){var u=t[h].getBoundingClientRect(),c=2*h,d=u.left,u=u.top;s.push(d,u),a=a&&r&&d===r[c]&&u===r[1+c],l.push(t[h].offsetLeft,t[h].offsetTop)}return a&&o?o:(e.srcCoords=s,e[i]=n?Yt(l,s):Yt(s,l))}(function(t,e){var n=e.markers;if(!n){n=e.markers=[];for(var i=["left","right"],o=["top","bottom"],r=0;r<4;r++){var a=document.createElement("div"),s=a.style,l=r%2,h=(r>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",o[h]+":0",i[1-l]+":auto",o[1-h]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}}return n}(e,r),r,o);if(e)return e(t,n,i),!0}return!1}function Zt(t){return"CANVAS"===t.nodeName.toUpperCase()}var Kt="undefined"!=typeof window&&!!window.addEventListener,$t=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Qt=[];function Jt(t,e,n,i){return n=n||{},i||!y.canvasSupported?te(t,e,n):y.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):te(t,e,n),n}function te(t,e,n){if(y.domSupported&&t.getBoundingClientRect){var i,o=e.clientX,e=e.clientY;if(Zt(t))return i=t.getBoundingClientRect(),n.zrX=o-i.left,void(n.zrY=e-i.top);if(jt(Qt,t,o,e))return n.zrX=Qt[0],void(n.zrY=Qt[1])}n.zrX=n.zrY=0}function ee(t){return t||window.event}function ne(t,e,n){var i;return null==(e=ee(e)).zrX&&((i=e.type)&&0<=i.indexOf("touch")?(i=("touchend"!==i?e.targetTouches:e.changedTouches)[0])&&Jt(t,i,e,n):(Jt(t,e,e,n),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3),i=e.button,null==e.which&&void 0!==i&&$t.test(e.type))&&(e.which=1&i?1:2&i?3:4&i?2:0),e}function ie(){this._track=[]}var oe=Kt?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};function re(t){var e=t[1][0]-t[0][0],t=t[1][1]-t[0][1];return Math.sqrt(e*e+t*t)}ie.prototype={constructor:ie,recognize:function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,n){var i=t.touches;if(i){for(var o={points:[],touches:[],target:e,event:t},r=0,a=i.length;r<a;r++){var s=i[r],l=Jt(n,s,{});o.points.push([l.zrX,l.zrY]),o.touches.push(s)}this._track.push(o)}},_recognize:function(t){for(var e in ae)if(ae.hasOwnProperty(e)){e=ae[e](this._track,t);if(e)return e}}};var ae={pinch:function(t,e){var n,i=t.length;if(i)return n=(t[i-1]||{}).points,(i=(t[i-2]||{}).points||n)&&1<i.length&&n&&1<n.length?(i=re(n)/re(i),isFinite(i)||(i=1),e.pinchScale=i,i=[(n[0][0]+n[1][0])/2,(n[0][1]+n[1][1])/2],e.pinchX=i[0],e.pinchY=i[1],{type:"pinch",target:t[0].target,event:e}):void 0}};function se(){oe(this.event)}function le(){}le.prototype.dispose=function(){};function he(t,e,n,i){Ht.call(this),this.storage=t,this.painter=e,this.painterRoot=i,n=n||new le,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,Nt.call(this),this.setHandlerProxy(n)}var ue=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"];function ce(t,e,n){t=t.painter;return e<0||e>t.getWidth()||n<0||n>t.getHeight()}he.prototype={constructor:he,setHandlerProxy:function(e){this.proxy&&this.proxy.dispose(),e&&(D(ue,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},mousemove:function(t){var e=t.zrX,n=t.zrY,i=ce(this,e,n),o=this._hovered,r=o.target,i=(r&&!r.__zr&&(r=(o=this.findHover(o.x,o.y)).target),this._hovered=i?{x:e,y:n}:this.findHover(e,n)),e=i.target,n=this.proxy;n.setCursor&&n.setCursor(e?e.cursor:"default"),r&&e!==r&&this.dispatchToElement(o,"mouseout",t),this.dispatchToElement(i,"mousemove",t),e&&e!==r&&this.dispatchToElement(i,"mouseover",t)},mouseout:function(t){var e=t.zrEventControl,n=t.zrIsToLocalDOM;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"===e||n||this.trigger("globalout",{type:"globalout",event:t})},resize:function(t){this._hovered={}},dispatch:function(t,e){t=this[t];t&&t.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var o="on"+e,r={type:e,event:n,target:(t=t).target,topTarget:t.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:se};i&&(i[o]&&(r.cancelBubble=i[o].call(i,r)),i.trigger(e,r),i=i.parent,!r.cancelBubble););r.cancelBubble||(this.trigger(e,r),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[o]&&t[o].call(t,r),t.trigger&&t.trigger(e,r)}))}},findHover:function(t,e,n){for(var i,o=this.storage.getDisplayList(),r={x:t,y:e},a=o.length-1;0<=a;a--)if(o[a]!==n&&!o[a].ignore&&(i=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i,o=t;o;){if(o.clipPath&&!o.clipPath.contain(e,n))return!1;o.silent&&(i=!0),o=o.parent}return!i||"silent"}return!1}(o[a],t,e))&&(r.topTarget||(r.topTarget=o[a]),"silent"!==i)){r.target=o[a];break}return r},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new ie);var n=this._gestureMgr,i=("start"===e&&n.clear(),n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom));"end"===e&&n.clear(),i&&(e=i.type,t.gestureEvent=e,this.dispatchToElement({target:i.target},e,i.event))}},D(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){he.prototype[a]=function(t){var e,n,i=t.zrX,o=t.zrY,r=ce(this,i,o);if("mouseup"===a&&r||(n=(e=this.findHover(i,o)).target),"mousedown"===a)this._downEl=n,this._downPoint=[t.zrX,t.zrY],this._upEl=n;else if("mouseup"===a)this._upEl=n;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<Ot(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}}),i(he,Ht),i(he,Nt);var de="undefined"==typeof Float32Array?Array:Float32Array;function fe(){var t=new de(6);return pe(t),t}function pe(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function ge(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function me(t,e,n){var i=e[0]*n[0]+e[2]*n[1],o=e[1]*n[0]+e[3]*n[1],r=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],n=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=o,t[2]=r,t[3]=a,t[4]=s,t[5]=n,t}function ye(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function ve(t,e,n){var i=e[0],o=e[2],r=e[4],a=e[1],s=e[3],e=e[5],l=Math.sin(n),n=Math.cos(n);return t[0]=i*n+a*l,t[1]=-i*l+a*n,t[2]=o*n+s*l,t[3]=-o*l+n*s,t[4]=n*r+l*e,t[5]=n*e-l*r,t}function _e(t,e,n){var i=n[0],n=n[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function xe(t,e){var n=e[0],i=e[2],o=e[4],r=e[1],a=e[3],e=e[5],s=n*a-r*i;return s?(t[0]=a*(s=1/s),t[1]=-r*s,t[2]=-i*s,t[3]=n*s,t[4]=(i*e-a*o)*s,t[5]=(r*o-n*e)*s,t):null}var we=Object.freeze({__proto__:null,clone:function(t){var e=fe();return ge(e,t),e},copy:ge,create:fe,identity:pe,invert:xe,mul:me,rotate:ve,scale:_e,translate:ye}),be=pe;function Se(t){return 5e-5<t||t<-5e-5}function Me(t){(t=t||{}).position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null}var e=Me.prototype,Ce=(e.transform=null,e.needLocalTransform=function(){return Se(this.rotation)||Se(this.position[0])||Se(this.position[1])||Se(this.scale[0]-1)||Se(this.scale[1]-1)},[]),Ie=(e.updateTransform=function(){var t=this.parent,e=t&&t.transform,n=this.needLocalTransform(),i=this.transform;n||e?(i=i||fe(),n?this.getLocalTransform(i):be(i),e&&(n?me(i,t.transform,i):ge(i,t.transform)),this.transform=i,null!=(e=this.globalScaleRatio)&&1!==e&&(this.getGlobalScale(Ce),t=((Ce[1]-(n=Ce[1]<0?-1:1))*e+n)/Ce[1]||0,i[0]*=e=((Ce[0]-(n=Ce[0]<0?-1:1))*e+n)/Ce[0]||0,i[1]*=e,i[2]*=t,i[3]*=t),this.invTransform=this.invTransform||fe(),xe(this.invTransform,i)):i&&be(i)},e.getLocalTransform=function(t){return Me.getLocalTransform(this,t)},e.setTransform=function(t){var e=this.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)},e.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)},[]),Te=fe(),De=(e.setLocalTransform=function(t){var e,n,i,o;t&&(e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=this.position,o=this.scale,Se(e-1)&&(e=Math.sqrt(e)),Se(n-1)&&(n=Math.sqrt(n)),t[0]<0&&(e=-e),t[3]<0&&(n=-n),i[0]=t[4],i[1]=t[5],o[0]=e,o[1]=n,this.rotation=Math.atan2(-t[1]/n,t[0]/e))},e.decomposeTransform=function(){var t,e;this.transform&&(e=this.parent,t=this.transform,e&&e.transform&&(me(Ie,e.invTransform,t),t=Ie),(e=this.origin)&&(e[0]||e[1])&&(Te[4]=e[0],Te[5]=e[1],me(Ie,t,Te),Ie[4]-=e[0],Ie[5]-=e[1],t=Ie),this.setLocalTransform(t))},e.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},e.transformCoordToLocal=function(t,e){t=[t,e],e=this.invTransform;return e&&zt(t,t,e),t},e.transformCoordToGlobal=function(t,e){t=[t,e],e=this.transform;return e&&zt(t,t,e),t},Me.getLocalTransform=function(t,e){be(e=e||[]);var n=t.origin,i=t.scale||[1,1],o=t.rotation||0,t=t.position||[0,0];return n&&(e[4]-=n[0],e[5]-=n[1]),_e(e,e,i),o&&ve(e,e,o),n&&(e[4]+=n[0],e[5]+=n[1]),e[4]+=t[0],e[5]+=t[1],e},{linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-De.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*De.bounceIn(2*t):.5*De.bounceOut(2*t-1)+.5}});function ke(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}ke.prototype={constructor:ke,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)this._pausedTime+=e;else{var n,e=(t-this._startTime-this._pausedTime)/this._life;if(!(e<0))return e=Math.min(e,1),n="function"==typeof(n="string"==typeof(n=this.easing)?De[n]:n)?n(e):e,this.fire("frame",n),1===e?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){this[t="on"+t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};function Ae(){this.head=null,this.tail=null,this._len=0}function Pe(t){this._list=new Ae,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null}var e=Ae.prototype,Oe=(e.insert=function(t){t=new Oe(t);return this.insertEntry(t),t},e.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},e.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},e.len=function(){return this._len},e.clear=function(){this.head=this.tail=null,this._len=0},function(t){this.value=t,this.next,this.prev}),e=Pe.prototype,Le=(e.put=function(t,e){var n,i,o=this._list,r=this._map,a=null;return null==r[t]&&(i=o.len(),n=this._lastRemovedEntry,i>=this._maxSize&&0<i&&(i=o.head,o.remove(i),delete r[i.key],a=i.value,this._lastRemovedEntry=i),n?n.value=e:n=new Oe(e),n.key=t,o.insertEntry(n),r[t]=n),a},e.get=function(t){var t=this._map[t],e=this._list;if(null!=t)return t!==e.tail&&(e.remove(t),e.insertEntry(t)),t.value},e.clear=function(){this._list.clear(),this._map={}},{transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]});function Ee(t){return(t=Math.round(t))<0?0:255<t?255:t}function ze(t){return t<0?0:1<t?1:t}function Be(t){return t.length&&"%"===t.charAt(t.length-1)?Ee(parseFloat(t)/100*255):Ee(parseInt(t,10))}function Re(t){return t.length&&"%"===t.charAt(t.length-1)?ze(parseFloat(t)/100):ze(parseFloat(t))}function Ne(t,e,n){return n<0?n+=1:1<n&&--n,6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function Fe(t,e,n){return t+(e-t)*n}function He(t,e,n,i,o){t[0]=e,t[1]=n,t[2]=i,t[3]=o}function Ve(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Ge=new Pe(20),We=null;function Xe(t,e){We&&Ve(We,e),We=Ge.put(t,We||e.slice())}function Ye(t,e){if(t){e=e||[];var n=Ge.get(t);if(n)return Ve(e,n);n=(t+="").replace(/ /g,"").toLowerCase();if(n in Le)return Ve(e,Le[n]),Xe(t,e),e;if("#"===n.charAt(0))return 4===n.length?0<=(i=parseInt(n.substr(1),16))&&i<=4095?(He(e,(3840&i)>>4|(3840&i)>>8,240&i|(240&i)>>4,15&i|(15&i)<<4,1),Xe(t,e),e):void He(e,0,0,0,1):7===n.length?0<=(i=parseInt(n.substr(1),16))&&i<=16777215?(He(e,(16711680&i)>>16,(65280&i)>>8,255&i,1),Xe(t,e),e):void He(e,0,0,0,1):void 0;var i=n.indexOf("("),o=n.indexOf(")");if(-1!==i&&o+1===n.length){var r=n.substr(0,i),a=n.substr(i+1,o-(i+1)).split(","),s=1;switch(r){case"rgba":if(4!==a.length)return void He(e,0,0,0,1);s=Re(a.pop());case"rgb":return 3!==a.length?void He(e,0,0,0,1):(He(e,Be(a[0]),Be(a[1]),Be(a[2]),s),Xe(t,e),e);case"hsla":return 4!==a.length?void He(e,0,0,0,1):(a[3]=Re(a[3]),Ue(a,e),Xe(t,e),e);case"hsl":return 3!==a.length?void He(e,0,0,0,1):(Ue(a,e),Xe(t,e),e);default:return}}He(e,0,0,0,1)}}function Ue(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=Re(t[1]),o=Re(t[2]),i=o<=.5?o*(i+1):o+i-o*i,o=2*o-i;return He(e=e||[],Ee(255*Ne(o,i,n+1/3)),Ee(255*Ne(o,i,n)),Ee(255*Ne(o,i,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function qe(t,e){var n=Ye(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,255<n[i]?n[i]=255:t[i]<0&&(n[i]=0);return $e(n,4===n.length?"rgba":"rgb")}}function je(t){t=Ye(t);if(t)return((1<<24)+(t[0]<<16)+(t[1]<<8)+ +t[2]).toString(16).slice(1)}function Ze(t,e,n){var i,o,r;if(e&&e.length&&0<=t&&t<=1)return n=n||[],t=t*(e.length-1),i=Math.floor(t),r=Math.ceil(t),o=e[i],e=e[r],n[0]=Ee(Fe(o[0],e[0],r=t-i)),n[1]=Ee(Fe(o[1],e[1],r)),n[2]=Ee(Fe(o[2],e[2],r)),n[3]=ze(Fe(o[3],e[3],r)),n}function Ke(t,e,n){var i,o,r,a;if(e&&e.length&&0<=t&&t<=1)return t=t*(e.length-1),i=Math.floor(t),o=Math.ceil(t),a=Ye(e[i]),e=Ye(e[o]),a=$e([Ee(Fe(a[0],e[0],r=t-i)),Ee(Fe(a[1],e[1],r)),Ee(Fe(a[2],e[2],r)),ze(Fe(a[3],e[3],r))],"rgba"),n?{color:a,leftIndex:i,rightIndex:o,value:t}:a}function $e(t,e){var n;if(t&&t.length)return n=t[0]+","+t[1]+","+t[2],"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}var e=Object.freeze({__proto__:null,fastLerp:Ze,fastMapToColor:Ze,lerp:Ke,lift:qe,mapToColor:Ke,modifyAlpha:function(t,e){if((t=Ye(t))&&null!=e)return t[3]=ze(e),$e(t,"rgba")},modifyHSL:function(t,e,n,i){if(t=Ye(t))return t=function(t){var e,n,i,o,r,a,s,l,h,u;if(t)return u=t[0]/255,e=t[1]/255,n=t[2]/255,s=Math.min(u,e,n),o=((i=Math.max(u,e,n))+s)/2,0==(h=i-s)?a=r=0:(a=o<.5?h/(i+s):h/(2-i-s),s=((i-u)/6+h/2)/h,l=((i-e)/6+h/2)/h,h=((i-n)/6+h/2)/h,u===i?r=h-l:e===i?r=1/3+s-h:n===i&&(r=2/3+l-s),r<0&&(r+=1),1<r&&--r),u=[360*r,a,o],null!=t[3]&&u.push(t[3]),u}(t),null!=e&&(t[0]=(e=e,(e=Math.round(e))<0?0:360<e?360:e)),null!=n&&(t[1]=Re(n)),null!=i&&(t[2]=Re(i)),$e(Ue(t),"rgba")},parse:Ye,stringify:$e,toHex:je}),Qe=Array.prototype.slice;function Je(t,e){return t[e]}function tn(t,e,n){t[e]=n}function en(t,e,n){return(e-t)*n+t}function nn(t,e,n,i,o){var r=t.length;if(1===o)for(var a=0;a<r;a++)i[a]=en(t[a],e[a],n);else for(var s=r&&t[0].length,a=0;a<r;a++)for(var l=0;l<s;l++)i[a][l]=en(t[a][l],e[a][l],n)}function on(t,e,n){var i=t.length,o=e.length;if(i!==o)if(o<i)t.length=o;else for(var r=i;r<o;r++)t.push(1===n?e[r]:Qe.call(e[r]));for(var a=t[0]&&t[0].length,r=0;r<t.length;r++)if(1===n)isNaN(t[r])&&(t[r]=e[r]);else for(var s=0;s<a;s++)isNaN(t[r][s])&&(t[r][s]=e[r][s])}function rn(t,e,n,i,o,r,a,s,l){var h=t.length;if(1===l)for(var u=0;u<h;u++)s[u]=an(t[u],e[u],n[u],i[u],o,r,a);else for(var c=t[0].length,u=0;u<h;u++)for(var d=0;d<c;d++)s[u][d]=an(t[u][d],e[u][d],n[u][d],i[u][d],o,r,a)}function an(t,e,n,i,o,r,a){t=.5*(n-t),i=.5*(i-e);return(2*(e-n)+t+i)*a+(-3*(e-n)-2*t-i)*r+t*o+e}function sn(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function ln(t,e,n,i,r,o){var a=t._getter,s=t._setter,l="spline"===e,h=i.length;if(h){for(var u,c=J(i[0].value),d=!1,f=!1,p=c?J((P=(P=i)[P.length-1].value)&&P[0])?2:1:0,g=(i.sort(function(t,e){return t.time-e.time}),u=i[h-1].time,[]),m=[],y=i[0].value,v=!0,_=0;_<h;_++){g.push(i[_].time/u);var x,w=i[_].value;c&&function(t,e,n){if(t===e)return 1;var i=t.length;if(i===e.length){if(1===n){for(var o=0;o<i;o++)if(t[o]!==e[o])return}else for(var r=t[0].length,o=0;o<i;o++)for(var a=0;a<r;a++)if(t[o][a]!==e[o][a])return;return 1}}(w,y,p)||!c&&w===y||(v=!1),"string"==typeof(y=w)&&((x=Ye(w))?(w=x,d=!0):f=!0),m.push(w)}if(o||!v){for(var b=m[h-1],_=0;_<h-1;_++)c?on(m[_],b,p):!isNaN(m[_])||isNaN(b)||f||d||(m[_]=b);c&&on(a(t._target,r),b,p);var S,M,C,I,T,D,k=0,A=0,P=(d&&(D=[0,0,0,0]),new ke({target:t._target,life:u,loop:t._loop,delay:t._delay,onframe:function(t,e){var n;if(e<0)n=0;else if(e<A){for(n=Math.min(k+1,h-1);0<=n&&!(g[n]<=e);n--);n=Math.min(n,h-2)}else{for(n=k;n<h&&!(e<g[n]);n++);n=Math.min(n-1,h-2)}A=e;var i,o=g[(k=n)+1]-g[n];if(0!=o)if(S=(e-g[n])/o,l)if(C=m[n],M=m[0===n?n:n-1],I=m[h-2<n?h-1:n+1],T=m[h-3<n?h-1:n+2],c)rn(M,C,I,T,S,S*S,S*S*S,a(t,r),p);else{if(d)i=rn(M,C,I,T,S,S*S,S*S*S,D,1),i=sn(D);else{if(f)return.5<S?I:C;i=an(M,C,I,T,S,S*S,S*S*S)}s(t,r,i)}else if(c)nn(m[n],m[n+1],S,a(t,r),p);else{if(d)nn(m[n],m[n+1],S,D,1),i=sn(D);else{if(f)return.5<S?m[n+1]:m[n];i=en(m[n],m[n+1],S)}s(t,r,i)}},ondestroy:n}));return e&&"spline"!==e&&(P.easing=e),P}}}function hn(t,e,n,i){this._tracks={},this._target=t,this._loop=e||!1,this._getter=n||Je,this._setter=i||tn,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]}function un(){this.animators=[]}hn.prototype={when:function(t,e){var n,i=this._tracks;for(n in e)if(e.hasOwnProperty(n)){if(!i[n]){i[n]=[];var o=this._getter(this._target,n);if(null==o)continue;0!==t&&i[n].push({time:0,value:function(t){if(J(t)){var e=t.length;if(J(t[0])){for(var n=[],i=0;i<e;i++)n.push(Qe.call(t[i]));return n}return Qe.call(t)}return t}(o)})}i[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,n=0;n<e;n++)t[n].call(this)},start:function(t,e){function n(){--l||s._doneCallback()}var i,o,r,a,s=this,l=0;for(o in this._tracks)this._tracks.hasOwnProperty(o)&&(r=ln(this,t,n,this._tracks[o],o,e))&&(this._clipList.push(r),l++,this.animation&&this.animation.addClip(r),i=r);return i&&(a=i.onframe,i.onframe=function(t,e){a(t,e);for(var n=0;n<s._onframeList.length;n++)s._onframeList[n](t,e)}),l||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,n=this.animation,i=0;i<e.length;i++){var o=e[i];t&&o.onframe(this._target,1),n&&n.removeClip(o)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var cn=1,dn=cn="undefined"!=typeof window?Math.max(window.devicePixelRatio||1,1):cn;function fn(t,e,n,i,o,r,a,s){k(i)?(r=o,o=i,i=0):it(o)?(r=o,o="linear",i=0):it(i)?(r=i,i=0):n=it(n)?(r=n,500):n||500,t.stopAnimation(),function t(e,n,i,o,r,a,s){var l={};var h=0;for(var u in o)o.hasOwnProperty(u)&&(null!=i[u]?A(o[u])&&!J(o[u])?t(e,n?n+"."+u:u,i[u],o[u],r,a,s):(s?(l[u]=i[u],pn(e,n,u,o[u])):l[u]=o[u],h++):null==o[u]||s||pn(e,n,u,o[u]));0<h&&e.animate(n,!1).when(null==r?500:r,l).delay(a||0)}(t,"",t,e,n,i,s);var l=t.animators.slice(),h=l.length;function u(){--h||r&&r()}h||r&&r();for(var c=0;c<l.length;c++)l[c].done(u).start(o,a)}function pn(t,e,n,i){var o;e?((o={})[e]={},o[e][n]=i,t.attr(o)):t.attr(n,i)}function gn(t){Me.call(this,t),Ht.call(this,t),un.call(this,t),this.id=t.id||E++}gn.prototype={type:"element",name:"",__zr:null,ignore:!(un.prototype={constructor:un,animate:function(t,e){var n,i,o,r=!1,a=this,s=this.__zr;if(t){for(var l=t.split("."),h=a,r="shape"===l[0],u=0,c=l.length;u<c;u++)h=h&&h[l[u]];h&&(n=h)}else n=a;if(n)return i=a.animators,(o=new hn(n,e)).during(function(t){a.dirty(r)}).done(function(){i.splice(m(i,o),1)}),i.push(o),s&&s.animation.addAnimator(o),o;a.id},stopAnimation:function(t){for(var e=this.animators,n=e.length,i=0;i<n;i++)e[i].stop(t);return e.length=0,this},animateTo:function(t,e,n,i,o,r){fn(this,t,e,n,i,o,r)},animateFrom:function(t,e,n,i,o,r){fn(this,t,e,n,i,o,r,!0)}}),clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var n=this.transform;(n=n||(this.transform=[1,0,0,1,0,0]))[4]+=t,n[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(t,e){},attrKV:function(t,e){var n;"position"===t||"scale"===t||"origin"===t?e&&((n=(n=this[t])||(this[t]=[]))[0]=e[0],n[1]=e[1]):this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(A(t))for(var n in t)t.hasOwnProperty(n)&&this.attrKV(n,t[n]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),(this.clipPath=t).__zr=e,(t.__clipTarget=this).dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},i(gn,un),i(gn,Me),i(gn,Ht);var mn,yn,vn,_n,xn=zt,wn=Math.min,bn=Math.max;function X(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}X.prototype={constructor:X,union:function(t){var e=wn(t.x,this.x),n=wn(t.y,this.y);this.width=bn(t.x+t.width,this.x+this.width)-e,this.height=bn(t.y+t.height,this.y+this.height)-n,this.x=e,this.y=n},applyTransform:(mn=[],yn=[],vn=[],_n=[],function(t){var e;t&&(mn[0]=vn[0]=this.x,mn[1]=_n[1]=this.y,yn[0]=_n[0]=this.x+this.width,yn[1]=vn[1]=this.y+this.height,xn(mn,mn,t),xn(yn,yn,t),xn(vn,vn,t),xn(_n,_n,t),this.x=wn(mn[0],yn[0],vn[0],_n[0]),this.y=wn(mn[1],yn[1],vn[1],_n[1]),t=bn(mn[0],yn[0],vn[0],_n[0]),e=bn(mn[1],yn[1],vn[1],_n[1]),this.width=t-this.x,this.height=e-this.y)}),calculateTransform:function(t){var e=t.width/this.width,n=t.height/this.height,i=fe();return ye(i,i,[-this.x,-this.y]),_e(i,i,[e,n]),ye(i,i,[t.x,t.y]),i},intersect:function(t){if(!t)return!1;t instanceof X||(t=X.create(t));var e=this,n=e.x,i=e.x+e.width,o=e.y,e=e.y+e.height,r=t.x,a=t.x+t.width,s=t.y,t=t.y+t.height;return!(i<r||a<n||e<s||t<o)},contain:function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},clone:function(){return new X(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}};function h(t){for(var e in gn.call(this,t=t||{}),t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0}h.prototype={constructor:h,isGroup:!0,type:"group",silent:!(X.create=function(t){return new X(t.x,t.y,t.width,t.height)}),children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){var n;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(e=(n=this._children).indexOf(e))&&(n.splice(e,0,t),this._doAdd(t)),this},_doAdd:function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__storage,n=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof h)&&t.addChildrenToStorage(e),n&&n.refresh()},remove:function(t){var e=this.__zr,n=this.__storage,i=this._children,o=m(i,t);return o<0||(i.splice(o,1),t.parent=null,n&&(n.delFromStorage(t),t instanceof h)&&t.delChildrenFromStorage(n),e&&e.refresh()),this},removeAll:function(){for(var t,e=this._children,n=this.__storage,i=0;i<e.length;i++)t=e[i],n&&(n.delFromStorage(t),t instanceof h)&&t.delChildrenFromStorage(n),t.parent=null;return e.length=0,this},eachChild:function(t,e){for(var n=this._children,i=0;i<n.length;i++){var o=n[i];t.call(e,o,i)}return this},traverse:function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n];t.call(e,i),"group"===i.type&&i.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.addToStorage(n),n instanceof h&&n.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.delFromStorage(n),n instanceof h&&n.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,n=new X(0,0,0,0),i=t||this._children,o=[],r=0;r<i.length;r++){var a,s=i[r];s.ignore||s.invisible||(a=s.getBoundingRect(),(s=s.getLocalTransform(o))?(n.copy(a),n.applyTransform(s),(e=e||n.clone()).union(n)):(e=e||a.clone()).union(a))}return e||n}},Q(h,gn);var Sn=32,Mn=7;function Cn(t,e,n,i){var o=e+1;if(o===n)return 1;if(i(t[o++],t[e])<0){for(;o<n&&i(t[o],t[o-1])<0;)o++;var r=t,a=e,s=o;for(s--;a<s;){var l=r[a];r[a++]=r[s],r[s--]=l}}else for(;o<n&&0<=i(t[o],t[o-1]);)o++;return o-e}function In(t,e,n,i,o){for(i===e&&i++;i<n;i++){for(var r,a=t[i],s=e,l=i;s<l;)o(a,t[r=s+l>>>1])<0?l=r:s=1+r;var h=i-s;switch(h){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<h;)t[s+h]=t[s+h-1],h--}t[s]=a}}function Tn(t,e,n,i,o,r){var a=0,s=0,l=1;if(0<r(t,e[n+o])){for(s=i-o;l<s&&0<r(t,e[n+o+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=o,l+=o}else{for(s=o+1;l<s&&r(t,e[n+o-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);i=a,a=o-(l=s<l?s:l),l=o-i}for(a++;a<l;){var h=a+(l-a>>>1);0<r(t,e[n+h])?a=h+1:l=h}return l}function Dn(t,e,n,i,o,r){var a=0,s=0,l=1;if(r(t,e[n+o])<0){for(s=o+1;l<s&&r(t,e[n+o-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);var h=a,a=o-(l=s<l?s:l),l=o-h}else{for(s=i-o;l<s&&0<=r(t,e[n+o+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=o,l+=o}for(a++;a<l;){var u=a+(l-a>>>1);r(t,e[n+u])<0?l=u:a=u+1}return l}function kn(A,P){var O,L,E=Mn,z=0,B=(A.length,[]);function e(t){var e=O[t],n=L[t],i=O[t+1],o=L[t+1],t=(L[t]=n+o,t===z-3&&(O[t+1]=O[t+2],L[t+1]=L[t+2]),z--,Dn(A[i],A,e,n,0,P));if(e+=t,0!=(n-=t)&&0!==(o=Tn(A[e+n-1],A,i,o,o-1,P)))if(n<=o){var r=e,a=n,t=i,s=o,l=0;for(l=0;l<a;l++)B[l]=A[r+l];var h=0,u=t,c=r;if(A[c++]=A[u++],0==--s)for(l=0;l<a;l++)A[c+l]=B[h+l];else if(1===a){for(l=0;l<s;l++)A[c+l]=A[u+l];A[c+s]=B[h]}else{for(var d,f,p,g=E;;){f=d=0,p=!1;do{if(P(A[u],B[h])<0){if(A[c++]=A[u++],f++,(d=0)==--s){p=!0;break}}else if(A[c++]=B[h++],d++,f=0,1==--a){p=!0;break}}while((d|f)<g);if(p)break;do{if(0!==(d=Dn(A[u],B,h,a,0,P))){for(l=0;l<d;l++)A[c+l]=B[h+l];if(c+=d,h+=d,(a-=d)<=1){p=!0;break}}if(A[c++]=A[u++],0==--s){p=!0;break}if(0!==(f=Tn(B[h],A,u,s,0,P))){for(l=0;l<f;l++)A[c+l]=A[u+l];if(c+=f,u+=f,0===(s-=f)){p=!0;break}}if(A[c++]=B[h++],1==--a){p=!0;break}}while(g--,Mn<=d||Mn<=f);if(p)break;g<0&&(g=0),g+=2}if((E=g)<1&&(E=1),1===a){for(l=0;l<s;l++)A[c+l]=A[u+l];A[c+s]=B[h]}else{if(0===a)throw new Error;for(l=0;l<a;l++)A[c+l]=B[h+l]}}}else{var m=e,y=n,v=i,_=o,x=0;for(x=0;x<_;x++)B[x]=A[v+x];var w=m+y-1,b=_-1,S=v+_-1,M=0,C=0;if(A[S--]=A[w--],0==--y)for(M=S-(_-1),x=0;x<_;x++)A[M+x]=B[x];else if(1===_){for(C=(S-=y)+1,M=(w-=y)+1,x=y-1;0<=x;x--)A[C+x]=A[M+x];A[S]=B[b]}else{for(var I=E;;){var T=0,D=0,k=!1;do{if(P(B[b],A[w])<0){if(A[S--]=A[w--],T++,(D=0)==--y){k=!0;break}}else if(A[S--]=B[b--],D++,T=0,1==--_){k=!0;break}}while((T|D)<I);if(k)break;do{if(0!==(T=y-Dn(B[b],A,m,y,y-1,P))){for(y-=T,C=(S-=T)+1,M=(w-=T)+1,x=T-1;0<=x;x--)A[C+x]=A[M+x];if(0===y){k=!0;break}}if(A[S--]=B[b--],1==--_){k=!0;break}if(0!==(D=_-Tn(A[w],B,0,_,_-1,P))){for(_-=D,C=(S-=D)+1,M=(b-=D)+1,x=0;x<D;x++)A[C+x]=B[M+x];if(_<=1){k=!0;break}}if(A[S--]=A[w--],0==--y){k=!0;break}}while(I--,Mn<=T||Mn<=D);if(k)break;I<0&&(I=0),I+=2}if((E=I)<1&&(E=1),1===_){for(C=(S-=y)+1,M=(w-=y)+1,x=y-1;0<=x;x--)A[C+x]=A[M+x];A[S]=B[b]}else{if(0===_)throw new Error;for(M=S-(_-1),x=0;x<_;x++)A[M+x]=B[x]}}}}O=[],L=[],this.mergeRuns=function(){for(;1<z;){var t=z-2;if(1<=t&&L[t-1]<=L[t]+L[t+1]||2<=t&&L[t-2]<=L[t]+L[t-1])L[t-1]<L[t+1]&&t--;else if(L[t]>L[t+1])break;e(t)}},this.forceMergeRuns=function(){for(;1<z;){var t=z-2;0<t&&L[t-1]<L[t+1]&&t--,e(t)}},this.pushRun=function(t,e){O[z]=t,L[z]=e,z+=1}}function An(t,e,n,i){var o=(i=i||t.length)-(n=n||0);if(!(o<2)){var r=0;if(o<Sn)In(t,n,i,n+(r=Cn(t,n,i,e)),e);else{var a,s=new kn(t,e),l=function(t){for(var e=0;Sn<=t;)e|=1&t,t>>=1;return t+e}(o);do{}while((r=Cn(t,n,i,e))<l&&(In(t,n,n+(a=l<(a=o)?l:o),n+r,e),r=a),s.pushRun(n,r),s.mergeRuns(),n+=r,0!==(o-=r));s.forceMergeRuns()}}}function Pn(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function On(){this._roots=[],this._displayList=[],this._displayListLen=0}var Ln={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1};function En(t,e,n){return Ln.hasOwnProperty(e)?n*t.dpr:n}function zn(t){this.extendFrom(t,!1)}var Bn={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},Rn=9,Nn=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Fn(t,e,n){var i=null==e.x?0:e.x,o=null==e.x2?1:e.x2,r=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(i=i*n.width+n.x,o=o*n.width+n.x,r=r*n.height+n.y,a=a*n.height+n.y),i=isNaN(i)?0:i,o=isNaN(o)?1:o,r=isNaN(r)?0:r,a=isNaN(a)?0:a,t.createLinearGradient(i,r,o,a)}function Hn(t,e,n){var i=n.width,o=n.height,r=Math.min(i,o),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;return e.global||(a=a*i+n.x,s=s*o+n.y,l*=r),t.createRadialGradient(a,s,0,a,s,l)}for(var Vn=zn.prototype={constructor:zn,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!(On.prototype={constructor:On,traverse:function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,o=e.length;i<o;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,y.canvasSupported&&An(n,Pn)},_updateAndAddDisplayable:function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var i=t.clipPath;if(i){e=e?e.slice():[];for(var o=i,r=t;o;)o.parent=r,o.updateTransform(),e.push(o),o=(r=o).clipPath}if(t.isGroup){for(var a=t._children,s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,n)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof h&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var n=this._roots[e];n instanceof h&&n.delChildrenFromStorage(this)}this._roots=[],this._displayList=[],this._displayListLen=0}else if(t instanceof Array)for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);else{var o=m(this._roots,t);0<=o&&(this.delFromStorage(t),this._roots.splice(o,1),t instanceof h)&&t.delChildrenFromStorage(this)}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:Pn}),text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,n){var i=this,o=n&&n.style,r=!o||t.__attrCachedBy!==Bn.STYLE_BIND;t.__attrCachedBy=Bn.STYLE_BIND;for(var a=0;a<Nn.length;a++){var s=Nn[a],l=s[0];!r&&i[l]===o[l]||(t[l]=En(t,l,i[l]||s[1]))}!r&&i.fill===o.fill||(t.fillStyle=i.fill),!r&&i.stroke===o.stroke||(t.strokeStyle=i.stroke),!r&&i.opacity===o.opacity||(t.globalAlpha=null==i.opacity?1:i.opacity),!r&&i.blend===o.blend||(t.globalCompositeOperation=i.blend||"source-over"),this.hasStroke()&&(n=i.lineWidth,t.lineWidth=n/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1))},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&0<this.lineWidth},extendFrom:function(t,e){if(t)for(var n in t)!t.hasOwnProperty(n)||!0!==e&&(!1===e?this.hasOwnProperty(n):null==t[n])||(this[n]=t[n])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,n){for(var i=("radial"===e.type?Hn:Fn)(t,e,n),o=e.colorStops,r=0;r<o.length;r++)i.addColorStop(o[r].offset,o[r].color);return i}},Gn=0;Gn<Nn.length;Gn++){var Wn=Nn[Gn];Wn[0]in Vn||(Vn[Wn[0]]=Wn[1])}zn.getGradient=Vn.getGradient;function Xn(t,e){this.image=t,this.repeat=e,this.type="pattern"}function Yn(){return!1}function Un(t,e,n){var i=K(),o=e.getWidth(),e=e.getHeight(),r=i.style;return r&&(r.position="absolute",r.left=0,r.top=0,r.width=o+"px",r.height=e+"px",i.setAttribute("data-zr-dom-id",t)),i.width=o*n,i.height=e*n,i}function qn(t,e,n){var i;n=n||dn,"string"==typeof t?i=Un(t,e,n):A(t)&&(t=(i=t).id),this.id=t,(t=(this.dom=i).style)&&(i.onselectstart=Yn,t["-webkit-user-select"]="none",t["user-select"]="none",t["-webkit-touch-callout"]="none",t["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",t.padding=0,t.margin=0,t["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=n}qn.prototype={constructor:qn,__dirty:!0,__used:!(Xn.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")}),__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=Un("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var n=this.dpr,i=this.dom,o=i.style,r=this.domBack;o&&(o.width=t+"px",o.height=e+"px"),i.width=t*n,i.height=e*n,r&&(r.width=t*n,r.height=e*n,1!==n)&&this.ctxBack.scale(n,n)},clear:function(t,e){var n,i=this.dom,o=this.ctx,r=i.width,a=i.height,e=e||this.clearColor,t=this.motionBlur&&!t,s=this.lastFrameAlpha,l=this.dpr;t&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,r/l,a/l)),o.clearRect(0,0,r,a),e&&"transparent"!==e&&(e.colorStops?(n=e.__canvasGradient||zn.getGradient(o,e,{x:0,y:0,width:r,height:a}),e.__canvasGradient=n):e.image&&(n=Xn.prototype.getCanvasPattern.call(e,o)),o.save(),o.fillStyle=n||e,o.fillRect(0,0,r,a),o.restore()),t&&(i=this.domBack,o.save(),o.globalAlpha=s,o.drawImage(i,0,0,r,a),o.restore())}};var jn="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},Zn=new Pe(50);function Kn(t,e,n,i,o){return t?"string"==typeof t?(e&&e.__zrImageSrc===t||!n||(n={hostEl:n,cb:i,cbPayload:o},(i=Zn.get(t))?Qn(e=i.image)||i.pending.push(n):((e=new Image).onload=e.onerror=$n,Zn.put(t,e.__cachedImgObj={image:e,pending:[n]}),e.src=e.__zrImageSrc=t)),e):t:e}function $n(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function Qn(t){return t&&t.width&&t.height}var Jn={},ti=0,ei=5e3,ni=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,ii="12px sans-serif",oi={};function ri(t,e){var n=t+":"+(e=e||ii);if(Jn[n])return Jn[n];for(var i,o,r=(t+"").split("\n"),a=0,s=0,l=r.length;s<l;s++)a=Math.max((i=r[s],o=e,oi.measureText(i,o)).width,a);return ei<ti&&(ti=0,Jn={}),ti++,Jn[n]=a}function ai(t,e,n,i,o,r,a,s){var l,h,u,c,d,f;return a?(l=i,a=(u=gi(u=t,{rich:a=a,truncate:f=s,font:h=e,textAlign:c=n,textPadding:d=o,textLineHeight:r})).outerWidth,f=u.outerHeight,h=si(0,a,c),d=li(0,f,l),new X(h,d,a,f)):(u=n,c=i,a=pi(l=t,h=e,d=o,a=r,f=s),f=ri(l,h),d&&(f+=d[1]+d[3]),l=a.outerHeight,h=si(0,f,u),d=li(0,l,c),(u=new X(h,d,f,l)).lineHeight=a.lineHeight,u)}function si(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function li(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function hi(t,e,n){var i=e.textPosition,o=e.textDistance,r=n.x,a=n.y,o=o||0,s=n.height,l=n.width,h=s/2,u="left",c="top";switch(i){case"left":r-=o,a+=h,u="right",c="middle";break;case"right":r+=o+l,a+=h,c="middle";break;case"top":r+=l/2,a-=o,u="center",c="bottom";break;case"bottom":r+=l/2,a+=s+o,u="center";break;case"inside":r+=l/2,a+=h,u="center",c="middle";break;case"insideLeft":r+=o,a+=h,c="middle";break;case"insideRight":r+=l-o,a+=h,u="right",c="middle";break;case"insideTop":r+=l/2,a+=o,u="center";break;case"insideBottom":r+=l/2,a+=s-o,u="center",c="bottom";break;case"insideTopLeft":r+=o,a+=o;break;case"insideTopRight":r+=l-o,a+=o,u="right";break;case"insideBottomLeft":r+=o,a+=s-o,c="bottom";break;case"insideBottomRight":r+=l-o,a+=s-o,u="right",c="bottom"}return(t=t||{}).x=r,t.y=a,t.textAlign=u,t.textVerticalAlign=c,t}function ui(t,e,n,i,o){if(!e)return"";var r=(t+"").split("\n");o=ci(e,n,i,o);for(var a=0,s=r.length;a<s;a++)r[a]=di(r[a],o);return r.join("\n")}function ci(t,e,n,i){(i=C({},i)).font=e;for(var n=ht(n,"..."),o=(i.maxIterations=ht(i.maxIterations,2),i.minChar=ht(i.minChar,0)),r=(i.cnCharWidth=ri("国",e),i.ascCharWidth=ri("a",e)),a=(i.placeholder=ht(i.placeholder,""),t=Math.max(0,t-1)),s=0;s<o&&r<=a;s++)a-=r;e=ri(n,e);return a<e&&(n="",e=0),a=t-e,i.ellipsis=n,i.ellipsisWidth=e,i.contentWidth=a,i.containerWidth=t,i}function di(t,e){var n=e.containerWidth,i=e.font,o=e.contentWidth;if(!n)return"";var r=ri(t,i);if(!(r<=n)){for(var a=0;;a++){if(r<=o||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?function(t,e,n,i){for(var o=0,r=0,a=t.length;r<a&&o<e;r++){var s=t.charCodeAt(r);o+=0<=s&&s<=127?n:i}return r}(t,o,e.ascCharWidth,e.cnCharWidth):0<r?Math.floor(t.length*o/r):0,r=ri(t=t.substr(0,s),i)}""===t&&(t=e.placeholder)}return t}function fi(t){return ri("国",t)}function pi(t,e,n,i,o){null!=t&&(t+="");var i=ht(i,fi(e)),r=t?t.split("\n"):[],a=r.length*i,s=a,l=!0;if(n&&(s+=n[0]+n[2]),t&&o){var l=!1,h=o.outerHeight,u=o.outerWidth;if(null!=h&&h<s)t="",r=[];else if(null!=u)for(var c=ci(u-(n?n[1]+n[3]:0),e,o.ellipsis,{minChar:o.minChar,placeholder:o.placeholder}),d=0,f=r.length;d<f;d++)r[d]=di(r[d],c)}return{lines:r,height:a,outerHeight:s,lineHeight:i,canCacheByTextString:l}}function gi(t,e){var n={lines:[],width:0,height:0};if(null!=t&&(t+=""),t){for(var i,o=ni.lastIndex=0;null!=(i=ni.exec(t));){var r=i.index;o<r&&mi(n,t.substring(o,r)),mi(n,i[2],i[1]),o=ni.lastIndex}o<t.length&&mi(n,t.substring(o,t.length));var a=n.lines,s=0,l=0,h=[],u=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;u&&(null!=d&&(d-=u[1]+u[3]),null!=f)&&(f-=u[0]+u[2]);for(var p=0;p<a.length;p++){for(var g=a[p],m=0,y=0,v=0;v<g.tokens.length;v++){var _=(D=g.tokens[v]).styleName&&e.rich[D.styleName]||{},x=D.textPadding=_.textPadding,w=D.font=_.font||e.font,b=D.textHeight=ht(_.textHeight,fi(w));if(x&&(b+=x[0]+x[2]),D.height=b,D.lineHeight=ut(_.textLineHeight,e.textLineHeight,b),D.textAlign=_&&_.textAlign||e.textAlign,D.textVerticalAlign=_&&_.textVerticalAlign||"middle",null!=f&&s+D.lineHeight>f)return{lines:[],width:0,height:0};D.textWidth=ri(D.text,w);var S,M,C,I=_.textWidth,T=null==I||"auto"===I;"string"==typeof I&&"%"===I.charAt(I.length-1)?(D.percentWidth=I,h.push(D),I=0):(T&&(I=D.textWidth,S=(S=_.textBackgroundColor)&&S.image)&&(M=void 0,Qn(S="string"==typeof(C=S)?(M=Zn.get(C))&&M.image:C))&&(I=Math.max(I,S.width*b/S.height)),I+=M=x?x[1]+x[3]:0,null!=(C=null!=d?d-y:null)&&C<I&&(!T||C<M?(D.text="",D.textWidth=I=0):(D.text=ui(D.text,C-M,w,c.ellipsis,{minChar:c.minChar}),D.textWidth=ri(D.text,w),I=D.textWidth+M))),y+=D.width=I,_&&(m=Math.max(m,D.lineHeight))}g.width=y,s+=g.lineHeight=m,l=Math.max(l,y)}n.outerWidth=n.width=ht(e.textWidth,l),n.outerHeight=n.height=ht(e.textHeight,s),u&&(n.outerWidth+=u[1]+u[3],n.outerHeight+=u[0]+u[2]);for(p=0;p<h.length;p++){var D,k=(D=h[p]).percentWidth;D.width=parseInt(k,10)/100*l}}return n}function mi(t,e,n){for(var i=""===e,o=e.split("\n"),r=t.lines,a=0;a<o.length;a++){var s,l,h=o[a],u={styleName:n,text:h,isLineHolder:!h&&!i};a?r.push({tokens:[u]}):1===(l=(s=(r[r.length-1]||(r[0]={tokens:[]})).tokens).length)&&s[0].isLineHolder?s[0]=u:!h&&l&&!i||s.push(u)}}function yi(t,e){var n,i,o,r,a,s=e.x,l=e.y,h=e.width,u=e.height,e=e.r;h<0&&(s+=h,h=-h),u<0&&(l+=u,u=-u),"number"==typeof e?n=i=o=r=e:e instanceof Array?1===e.length?n=i=o=r=e[0]:2===e.length?(n=o=e[0],i=r=e[1]):3===e.length?(n=e[0],i=r=e[1],o=e[2]):(n=e[0],i=e[1],o=e[2],r=e[3]):n=i=o=r=0,h<n+i&&(n*=h/(a=n+i),i*=h/a),h<o+r&&(o*=h/(a=o+r),r*=h/a),u<i+o&&(i*=u/(a=i+o),o*=u/a),u<n+r&&(n*=u/(a=n+r),r*=u/a),t.moveTo(s+n,l),t.lineTo(s+h-i,l),0!==i&&t.arc(s+h-i,l+i,i,-Math.PI/2,0),t.lineTo(s+h,l+u-o),0!==o&&t.arc(s+h-o,l+u-o,o,0,Math.PI/2),t.lineTo(s+r,l+u),0!==r&&t.arc(s+r,l+u-r,r,Math.PI/2,Math.PI),t.lineTo(s,l+n),0!==n&&t.arc(s+n,l+n,n,Math.PI,1.5*Math.PI)}oi.measureText=function(t,e){var n=$();return n.font=e||ii,n.measureText(t)};var vi=ii,_i={left:1,right:1,center:1},xi={top:1,bottom:1,middle:1},wi=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],bi={},Si={};function Mi(t){Ci(t),D(t.rich,Ci)}function Ci(t){var e,n;t&&(t.font=(n=((e=t).fontSize||e.fontFamily)&&[e.fontStyle,e.fontWeight,(e.fontSize||12)+"px",e.fontFamily||"sans-serif"].join(" "))&&ft(n)||e.textFont||e.font,n=t.textAlign,t.textAlign=null==(n="middle"===n?"center":n)||_i[n]?n:"left",e=t.textVerticalAlign||t.textBaseline,t.textVerticalAlign=null==(e="center"===e?"middle":e)||xi[e]?e:"top",t.textPadding)&&(t.textPadding=dt(t.textPadding))}function Ii(t,e,n,i,o,r){(i.rich?function(t,e,n,i,o,r){r!==Rn&&(e.__attrCachedBy=Bn.NONE);r=t.__textCotentBlock;r&&!t.__dirtyText||(r=t.__textCotentBlock=gi(n,i));!function(t,e,n,i,o){var r=n.width,a=n.outerWidth,s=n.outerHeight,l=i.textPadding,h=Oi(Si,t,i,o),u=h.baseX,c=h.baseY,d=h.textAlign,h=h.textVerticalAlign,o=(Ti(e,i,o,u,c),si(u,a,d)),u=li(c,s,h),f=o,p=u;l&&(f+=l[3],p+=l[0]);var g=f+r;ki(i)&&Ai(t,e,i,o,u,a,s);for(var m,y=0;y<n.lines.length;y++){for(var v=n.lines[y],_=v.tokens,x=_.length,w=v.lineHeight,b=v.width,S=0,M=f,C=g,I=x-1;S<x&&(!(m=_[S]).textAlign||"left"===m.textAlign);)Di(t,e,m,i,w,p,M,"left"),b-=m.width,M+=m.width,S++;for(;0<=I&&"right"===(m=_[I]).textAlign;)Di(t,e,m,i,w,p,C,"right"),b-=m.width,C-=m.width,I--;for(M+=(r-(M-f)-(g-C)-b)/2;S<=I;)m=_[S],Di(t,e,m,i,w,p,M+m.width/2,"center"),M+=m.width,S++;p+=w}}(t,e,r,i,o)}:function(t,e,n,i,o,r){var a,s=ki(i),l=!1,h=e.__attrCachedBy===Bn.PLAIN_TEXT;r!==Rn?(r&&(a=r.style,l=!s&&h&&a),e.__attrCachedBy=s?Bn.NONE:Bn.PLAIN_TEXT):h&&(e.__attrCachedBy=Bn.NONE);r=i.font||vi;l&&r===(a.font||vi)||(e.font=r);h=t.__computedFont;t.__styleFont!==r&&(t.__styleFont=r,h=t.__computedFont=e.font);var r=i.textPadding,u=i.textLineHeight,c=t.__textCotentBlock;c&&!t.__dirtyText||(c=t.__textCotentBlock=pi(n,h,r,u,i.truncate));var u=c.outerHeight,d=c.lines,f=c.lineHeight,c=Oi(Si,t,i,o),p=c.baseX,g=c.baseY,m=c.textAlign||"left",c=c.textVerticalAlign,o=(Ti(e,i,o,p,g),li(g,u,c)),y=p,v=o;(s||r)&&(g=ri(n,h),r&&(g+=r[1]+r[3]),c=si(p,g,m),s&&Ai(t,e,i,c,o,g,u),r)&&(y=Bi(p,m,r),v+=r[0]);e.textAlign=m,e.textBaseline="middle",e.globalAlpha=i.opacity||1;for(var _=0;_<wi.length;_++){var x=wi[_],w=x[0],b=x[1],S=i[w];l&&S===a[w]||(e[b]=En(e,b,S||x[2]))}v+=f/2;var n=i.textStrokeWidth,h=l?a.textStrokeWidth:null,s=!l||n!==h,t=!l||s||i.textStroke!==a.textStroke,M=Li(i.textStroke,n),C=Ei(i.textFill);M&&(s&&(e.lineWidth=n),t)&&(e.strokeStyle=M);!C||l&&i.textFill===a.textFill||(e.fillStyle=C);if(1===d.length)M&&e.strokeText(d[0],y,v),C&&e.fillText(d[0],y,v);else for(_=0;_<d.length;_++)M&&e.strokeText(d[_],y,v),C&&e.fillText(d[_],y,v),v+=f})(t,e,n,i,o,r)}function Ti(t,e,n,i,o){var r;n&&e.textRotation&&("center"===(r=e.textOrigin)?(i=n.width/2+n.x,o=n.height/2+n.y):r&&(i=r[0]+n.x,o=r[1]+n.y),t.translate(i,o),t.rotate(-e.textRotation),t.translate(-i,-o))}function Di(t,e,n,i,o,r,a,s){var l=i.rich[n.styleName]||{},h=(l.text=n.text,n.textVerticalAlign),u=r+o/2,h=("top"===h?u=r+n.height/2:"bottom"===h&&(u=r+o-n.height/2),!n.isLineHolder&&ki(l)&&Ai(t,e,l,"right"===s?a-n.width:"center"===s?a-n.width/2:a,u-n.height/2,n.width,n.height),n.textPadding),r=(h&&(a=Bi(a,s,h),u-=n.height/2-h[2]-n.textHeight/2),f(e,"shadowBlur",ut(l.textShadowBlur,i.textShadowBlur,0)),f(e,"shadowColor",l.textShadowColor||i.textShadowColor||"transparent"),f(e,"shadowOffsetX",ut(l.textShadowOffsetX,i.textShadowOffsetX,0)),f(e,"shadowOffsetY",ut(l.textShadowOffsetY,i.textShadowOffsetY,0)),f(e,"textAlign",s),f(e,"textBaseline","middle"),f(e,"font",n.font||vi),Li(l.textStroke||i.textStroke,void 0)),o=Ei(l.textFill||i.textFill),t=ht(l.textStrokeWidth,i.textStrokeWidth);r&&(f(e,"lineWidth",t),f(e,"strokeStyle",r),e.strokeText(n.text,a,u)),o&&(f(e,"fillStyle",o),e.fillText(n.text,a,u))}function ki(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function Ai(t,e,n,i,o,r,a){var s,l,h=n.textBackgroundColor,u=n.textBorderWidth,c=n.textBorderColor,d=k(h);f(e,"shadowBlur",n.textBoxShadowBlur||0),f(e,"shadowColor",n.textBoxShadowColor||"transparent"),f(e,"shadowOffsetX",n.textBoxShadowOffsetX||0),f(e,"shadowOffsetY",n.textBoxShadowOffsetY||0),(d||u&&c)&&(e.beginPath(),(s=n.textBorderRadius)?yi(e,{x:i,y:o,width:r,height:a,r:s}):e.rect(i,o,r,a),e.closePath()),d?(f(e,"fillStyle",h),null!=n.fillOpacity?(l=e.globalAlpha,e.globalAlpha=n.fillOpacity*n.opacity,e.fill(),e.globalAlpha=l):e.fill()):A(h)&&(s=Kn(h.image,null,t,Pi,h))&&Qn(s)&&e.drawImage(s,i,o,r,a),u&&c&&(f(e,"lineWidth",u),f(e,"strokeStyle",c),null!=n.strokeOpacity?(l=e.globalAlpha,e.globalAlpha=n.strokeOpacity*n.opacity,e.stroke(),e.globalAlpha=l):e.stroke())}function Pi(t,e){e.image=t}function Oi(t,e,n,i){var o,r=n.x||0,a=n.y||0,s=n.textAlign,l=n.textVerticalAlign;return i&&((o=n.textPosition)instanceof Array?(r=i.x+zi(o[0],i.width),a=i.y+zi(o[1],i.height)):(r=(o=e&&e.calculateTextPosition?e.calculateTextPosition(bi,n,i):hi(bi,n,i)).x,a=o.y,s=s||o.textAlign,l=l||o.textVerticalAlign),e=n.textOffset)&&(r+=e[0],a+=e[1]),(t=t||{}).baseX=r,t.baseY=a,t.textAlign=s,t.textVerticalAlign=l,t}function f(t,e,n){t[e]=En(t,e,n),t[e]}function Li(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ei(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function zi(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function Bi(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Ri(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function Ni(){}var Fi=new X;function Hi(t){for(var e in gn.call(this,t=t||{}),t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new zn(t.style),this._rect=null,this.__clipPaths=null}function Vi(t){Hi.call(this,t)}Hi.prototype={constructor:Hi,type:"displayable",__dirty:!0,invisible:!(Ni.prototype={constructor:Ni,drawRectText:function(t,e){var n,i=this.style,o=(e=i.textRect||e,this.__dirty&&Mi(i),i.text);null!=o&&(o+=""),Ri(o,i)&&(t.save(),n=this.transform,i.transformText?this.setTransform(t):n&&(Fi.copy(e),Fi.applyTransform(n),e=Fi),Ii(this,t,o,i,e,Rn),t.restore())}}),z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(t){},afterBrush:function(t){},brush:function(t,e){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){t=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(t[0],t[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?gn.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new zn(t),this.dirty(!1),this},calculateTextPosition:null},Q(Hi,gn),i(Hi,Ni),Vi.prototype={constructor:Vi,type:"image",brush:function(t,e){var n,i,o,r,a,s,l=this.style,h=l.image,e=(l.bind(t,this,e),this._image=Kn(h,this._image,this,this.onload));e&&Qn(e)&&(h=l.x||0,n=l.y||0,i=l.width,o=l.height,r=e.width/e.height,null==i&&null!=o?i=o*r:null==o&&null!=i?o=i/r:null==i&&null==o&&(i=e.width,o=e.height),this.setTransform(t),l.sWidth&&l.sHeight?(a=l.sx||0,s=l.sy||0,t.drawImage(e,a,s,l.sWidth,l.sHeight,h,n,i,o)):l.sx&&l.sy?(a=l.sx,s=l.sy,t.drawImage(e,a,s,i-a,o-s,h,n,i,o)):t.drawImage(e,h,n,i,o),null!=l.text)&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new X(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},Q(Vi,Hi);var Gi=314159;function Wi(t){return parseInt(t,10)}var Xi=new X(0,0,0,0),Yi=new X(0,0,0,0);function Ui(t,e,n){this.type="canvas";var i,o=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=C({},n||{}),this.dpr=n.devicePixelRatio||dn,this._singleCanvas=o;(r=(this.root=t).style)&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var r=this._zlevelList=[],e=this._layers={};this._layerConfig={},this._needsManuallyCompositing=!1,o?(o=t.width,i=t.height,null!=n.width&&(o=n.width),null!=n.height&&(i=n.height),this.dpr=n.devicePixelRatio||1,t.width=o*this.dpr,t.height=i*this.dpr,this._width=o,this._height=i,(n=new qn(t,this,this.dpr)).__builtin__=!0,n.initContext(),(e[Gi]=n).zlevel=Gi,r.push(Gi),this._domRoot=t):(this._width=this._getSize(0),this._height=this._getSize(1),n=this._domRoot=(o=this._width,i=this._height,(e=document.createElement("div")).style.cssText=["position:relative","width:"+o+"px","height:"+i+"px","padding:0","margin:0","border-width:0"].join(";")+";",e),t.appendChild(n)),this._hoverlayer=null,this._hoverElements=[]}function qi(t){this.stage=(t=t||{}).stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,Ht.call(this)}Ui.prototype={constructor:Ui,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(t){var e=this.storage.getDisplayList(!0),n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var i=0;i<n.length;i++){var o,r=n[i],r=this._layers[r];!r.__builtin__&&r.refresh&&(o=0===i?this._backgroundColor:null,r.refresh(o))}return this.refreshHover(),this},addHover:function(t,e){var n;if(!t.__hoverMir)return((n=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent})).__from=t).__hoverMir=n,e&&n.setStyle(e),this._hoverElements.push(n),n},removeHover:function(t){var e=t.__hoverMir,n=this._hoverElements,e=m(n,e);0<=e&&n.splice(e,1),t.__hoverMir=null},clearHover:function(t){for(var e=this._hoverElements,n=0;n<e.length;n++){var i=e[n].__from;i&&(i.__hoverMir=null)}e.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length;if((n=this._hoverlayer)&&n.clear(),e){An(t,this.storage.displayableSortFunc);var n,i={};(n=n||(this._hoverlayer=this.getLayer(1e5))).ctx.save();for(var o=0;o<e;){var r=t[o],a=r.__from;a&&a.__zr?(o++,a.invisible||(r.transform=a.transform,r.invTransform=a.invTransform,r.__clipPaths=a.__clipPaths,this._doPaintEl(r,n,!0,i))):(t.splice(o,1),a.__hoverMir=null,e--)}n.ctx.restore()}},getHoverLayer:function(){return this.getLayer(1e5)},_paintList:function(t,e,n){var i,o;this._redrawId!==n||(e=e||!1,this._updateLayerStatus(t),i=this._doPaintList(t,e),this._needsManuallyCompositing&&this._compositeManually(),i)||(o=this,jn(function(){o._paintList(t,e,n)}))},_compositeManually:function(){var e=this.getLayer(Gi).ctx,n=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,n,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,n,i)})},_doPaintList:function(t,e){for(var n=[],i=0;i<this._zlevelList.length;i++){var o=this._zlevelList[i];(s=this._layers[o]).__builtin__&&s!==this._hoverlayer&&(s.__dirty||e)&&n.push(s)}for(var r=!0,a=0;a<n.length;a++){var s,l,h=(s=n[a]).ctx,u={},c=(h.save(),e?s.__startIndex:s.__drawIndex),d=!e&&s.incremental&&Date.now,f=d&&Date.now(),p=s.zlevel===this._zlevelList[0]?this._backgroundColor:null;s.__startIndex!==s.__endIndex&&(c!==s.__startIndex||(l=t[c]).incremental&&l.notClear&&!e)||s.clear(!1,p),-1===c&&(console.error("For some unknown reason. drawIndex is -1"),c=s.__startIndex);for(var g=c;g<s.__endIndex;g++){var m=t[g];if(this._doPaintEl(m,s,e,u),m.__dirty=m.__dirtyText=!1,d)if(15<Date.now()-f)break}s.__drawIndex=g,s.__drawIndex<s.__endIndex&&(r=!1),u.prevElClipPaths&&h.restore(),h.restore()}return y.wxa&&D(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),r},_doPaintEl:function(t,e,n,i){var o=e.ctx,r=t.transform;if((e.__dirty||n)&&!t.invisible&&0!==t.style.opacity&&(!r||r[0]||r[3])&&(!t.culling||(e=t,n=this._width,r=this._height,Xi.copy(e.getBoundingRect()),e.transform&&Xi.applyTransform(e.transform),Yi.width=n,Yi.height=r,Xi.intersect(Yi)))){e=t.__clipPaths,n=i.prevElClipPaths;if((!n||function(t,e){if(t!==e){if(!t||!e||t.length!==e.length)return 1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return 1}}(e,n))&&(n&&(o.restore(),i.prevElClipPaths=null,i.prevEl=null),e)){o.save();for(var a=e,s=o,l=0;l<a.length;l++){var h=a[l];h.setTransform(s),s.beginPath(),h.buildPath(s,h.shape),s.clip(),h.restoreTransform(s)}i.prevElClipPaths=e}t.beforeBrush&&t.beforeBrush(o),t.brush(o,i.prevEl||null),(i.prevEl=t).afterBrush&&t.afterBrush(o)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Gi);var n=this._layers[t];return n||((n=new qn("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?g(n,this._layerConfig[t],!0):this._layerConfig[t-.01]&&g(n,this._layerConfig[t-.01],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},insertLayer:function(t,e){var n,i=this._layers,o=this._zlevelList,r=o.length,a=null,s=-1,l=this._domRoot;if(!i[t]&&(n=e)&&(n.__builtin__||"function"==typeof n.resize&&"function"==typeof n.refresh)){if(0<r&&t>o[0]){for(s=0;s<r-1&&!(o[s]<t&&o[s+1]>t);s++);a=i[o[s]]}o.splice(s+1,0,t),(i[t]=e).virtual||(a?(n=a.dom).nextSibling?l.insertBefore(e.dom,n.nextSibling):l.appendChild(e.dom):l.firstChild?l.insertBefore(e.dom,l.firstChild):l.appendChild(e.dom))}},eachLayer:function(t,e){for(var n,i=this._zlevelList,o=0;o<i.length;o++)n=i[o],t.call(e,this._layers[n],n)},eachBuiltinLayer:function(t,e){for(var n,i,o=this._zlevelList,r=0;r<o.length;r++)i=o[r],(n=this._layers[i]).__builtin__&&t.call(e,n,i)},eachOtherLayer:function(t,e){for(var n,i,o=this._zlevelList,r=0;r<o.length;r++)i=o[r],(n=this._layers[i]).__builtin__||t.call(e,n,i)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){o&&(o.__endIndex!==t&&(o.__dirty=!0),o.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++)if((a=t[n]).zlevel!==t[n-1].zlevel||a.incremental){this._needsManuallyCompositing=!0;break}for(var i,o=null,r=0,n=0;n<t.length;n++){var a,s,l=(a=t[n]).zlevel;i!==l&&(i=l,r=0),a.incremental?((s=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,r=1):s=this.getLayer(l+(0<r?.01:0),this._needsManuallyCompositing),s.__builtin__||s.id,s!==o&&(s.__used=!0,s.__startIndex!==n&&(s.__dirty=!0),s.__startIndex=n,s.incremental?s.__drawIndex=-1:s.__drawIndex=n,e(n),o=s),a.__dirty&&(s.__dirty=!0,s.incremental)&&s.__drawIndex<0&&(s.__drawIndex=n)}e(n),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var n=this._layerConfig;n[t]?g(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var o=this._zlevelList[i];o!==t&&o!==t+.01||g(this._layers[o],n[t],!0)}}},delLayer:function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(m(n,t),1))},resize:function(e,n){if(this._domRoot.style){var t=this._domRoot,i=(t.style.display="none",this._opts);if(null!=e&&(i.width=e),null!=n&&(i.height=n),e=this._getSize(0),n=this._getSize(1),t.style.display="",this._width!==e||n!==this._height){for(var o in t.style.width=e+"px",t.style.height=n+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(e,n);D(this._progressiveLayers,function(t){t.resize(e,n)}),this.refresh(!0)}this._width=e,this._height=n}else{if(null==e||null==n)return;this._width=e,this._height=n,this.getLayer(Gi).resize(e,n)}return this},clearLayer:function(t){t=this._layers[t];t&&t.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(this._singleCanvas&&!this._compositeManually)return this._layers[Gi].dom;var e=new qn("image",this,(t=t||{}).pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,i=e.dom.height,o=e.ctx;this.eachLayer(function(t){t.__builtin__?o.drawImage(t.dom,0,0,n,i):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var r={},a=this.storage.getDisplayList(!0),s=0;s<a.length;s++){var l=a[s];this._doPaintEl(l,e,!0,r)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e,n=this._opts,i=["width","height"][t],o=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],t=["paddingRight","paddingBottom"][t];return null!=n[i]&&"auto"!==n[i]?parseFloat(n[i]):(n=this.root,e=document.defaultView.getComputedStyle(n),(n[o]||Wi(e[i])||Wi(n.style[i]))-(Wi(e[r])||0)-(Wi(e[t])||0)|0)},pathToImage:function(t,e){e=e||this.dpr;var n=document.createElement("canvas"),i=n.getContext("2d"),o=t.getBoundingRect(),r=t.style,a=r.shadowBlur*e,s=r.shadowOffsetX*e,l=r.shadowOffsetY*e,r=r.hasStroke()?r.lineWidth:0,h=Math.max(r/2,a-s),s=Math.max(r/2,s+a),u=Math.max(r/2,a-l),r=Math.max(r/2,l+a),l=o.width+h+s,a=o.height+u+r,s=(n.width=l*e,n.height=a*e,i.scale(e,e),i.clearRect(0,0,l,a),i.dpr=e,{position:t.position,rotation:t.rotation,scale:t.scale});t.position=[h-o.x,u-o.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(i);r=new Vi({style:{x:0,y:0,image:n}});return null!=s.position&&(r.position=t.position=s.position),null!=s.rotation&&(r.rotation=t.rotation=s.rotation),null!=s.scale&&(r.scale=t.scale=s.scale),r}},qi.prototype={constructor:qi,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),n=0;n<e.length;n++)this.addClip(e[n])},removeClip:function(t){t=m(this._clips,t);0<=t&&this._clips.splice(t,1)},removeAnimator:function(t){for(var e=t.getClips(),n=0;n<e.length;n++)this.removeClip(e[n]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,n=this._clips,i=n.length,o=[],r=[],a=0;a<i;a++){var s=n[a],l=s.step(t,e);l&&(o.push(l),r.push(s))}for(a=0;a<i;)n[a]._needsRemove?(n[a]=n[i-1],n.pop(),i--):a++;for(i=o.length,a=0;a<i;a++)r[a].fire(o[a]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){var e=this;this._running=!0,jn(function t(){e._running&&(jn(t),e._paused||e._update())})},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){t=new hn(t,(e=e||{}).loop,e.getter,e.setter);return this.addAnimator(t),t}},i(qi,Ht);var ji,Zi=y.domSupported,Ki=(ji={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:cn=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:S(cn,function(t){var e=t.replace("mouse","pointer");return ji.hasOwnProperty(e)?e:t})}),$i={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]};function Qi(t){return"mousewheel"===t&&y.browser.firefox?"DOMMouseScroll":t}function Ji(t){t=t.pointerType;return"pen"===t||"touch"===t}function to(t){t&&(t.zrByTouch=!0)}function eo(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}function no(t,e){this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}var cn=no.prototype,io=(cn.stopPropagation=cn.stopImmediatePropagation=cn.preventDefault=vt,{mousedown:function(t){t=ne(this.dom,t),this._mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=ne(this.dom,t);var e=this._mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||ho(this,!0),this.trigger("mousemove",t)},mouseup:function(t){t=ne(this.dom,t),ho(this,!1),this.trigger("mouseup",t)},mouseout:function(t){t=ne(this.dom,t),this._pointerCapturing&&(t.zrEventControl="no_globalout");var e=t.toElement||t.relatedTarget;t.zrIsToLocalDOM=eo(this,e),this.trigger("mouseout",t)},touchstart:function(t){to(t=ne(this.dom,t)),this._lastTouchMoment=new Date,this.handler.processGesture(t,"start"),io.mousemove.call(this,t),io.mousedown.call(this,t)},touchmove:function(t){to(t=ne(this.dom,t)),this.handler.processGesture(t,"change"),io.mousemove.call(this,t)},touchend:function(t){to(t=ne(this.dom,t)),this.handler.processGesture(t,"end"),io.mouseup.call(this,t),+new Date-this._lastTouchMoment<300&&io.click.call(this,t)},pointerdown:function(t){io.mousedown.call(this,t)},pointermove:function(t){Ji(t)||io.mousemove.call(this,t)},pointerup:function(t){io.mouseup.call(this,t)},pointerout:function(t){Ji(t)||io.mouseout.call(this,t)}}),oo=(D(["click","mousewheel","dblclick","contextmenu"],function(e){io[e]=function(t){t=ne(this.dom,t),this.trigger(e,t)}}),{pointermove:function(t){Ji(t)||oo.mousemove.call(this,t)},pointerup:function(t){oo.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this._pointerCapturing;ho(this,!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}});function ro(i,o){var r=o.domHandlers;y.pointerEventsSupported?D(Ki.pointer,function(e){so(o,e,function(t){r[e].call(i,t)})}):(y.touchEventsSupported&&D(Ki.touch,function(n){so(o,n,function(t){var e;r[n].call(i,t),(e=o).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),D(Ki.mouse,function(e){so(o,e,function(t){t=ee(t),o.touching||r[e].call(i,t)})}))}function ao(i,o){function t(n){so(o,n,function(t){var e;t=ee(t),eo(i,t.target)||(e=t,t=ne(i.dom,new no(i,e),!0),o.domHandlers[n].call(i,t))},{capture:!0})}y.pointerEventsSupported?D($i.pointer,t):y.touchEventsSupported||D($i.mouse,t)}function so(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,t=t.domTarget,e=Qi(e),n=n,i=i,Kt?t.addEventListener(e,n,i):t.attachEvent("on"+e,n)}function lo(t){var e,n,i,o,r,a=t.mounted;for(e in a)a.hasOwnProperty(e)&&(n=t.domTarget,i=Qi(e),o=a[e],r=t.listenerOpts[e],Kt?n.removeEventListener(i,o,r):n.detachEvent("on"+i,o));t.mounted={}}function ho(t,e){var n;t._mayPointerCapture=null,Zi&&t._pointerCapturing^e&&(t._pointerCapturing=e,n=t._globalHandlerScope,e?ao(t,n):lo(n))}function uo(t,e){this.domTarget=t,this.domHandlers=e,this.mounted={},this.listenerOpts={},this.touchTimer=null,this.touching=!1}function co(t,e){Ht.call(this),this.dom=t,this.painterRoot=e,this._localHandlerScope=new uo(t,io),Zi&&(this._globalHandlerScope=new uo(document,oo)),this._pointerCapturing=!1,this._mayPointerCapture=null,ro(this,this._localHandlerScope)}var cn=co.prototype,fo=(cn.dispose=function(){lo(this._localHandlerScope),Zi&&lo(this._globalHandlerScope)},cn.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},i(co,Ht),!y.canvasSupported),po={canvas:Ui},go={},mo="4.3.2";function yo(t,e){t=new vo(E++,t,e);return go[t.id]=t}function vo(t,e,n){n=n||{},this.dom=e,this.id=t;var i=this,o=new On,r=n.renderer;if(fo){if(!po.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");r="vml"}else r&&po[r]||(r="canvas");var r=new po[r](e,o,n,t),e=(this.storage=o,this.painter=r,y.node||y.worker?null:new co(r.getViewportRoot(),r.root)),a=(this.handler=new he(o,r,e,r.root),this.animation=new qi({stage:{update:nt(this.flush,this)}}),this.animation.start(),this._needsRefresh,o.delFromStorage),s=o.addToStorage;o.delFromStorage=function(t){a.call(o,t),t&&t.removeSelfFromZr(i)},o.addToStorage=function(t){s.call(o,t),t.addSelfToZr(i)}}vo.prototype={constructor:vo,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=this._needsRefreshHover=!1,this.painter.refresh(),this._needsRefresh=this._needsRefreshHover=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover)return t=this.painter.addHover(t,e),this.refreshHover(),t},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){this.painter.resize((t=t||{}).width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,n){this.handler.on(t,e,n)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){var t;this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,t=this.id,delete go[t]}};var cn=Object.freeze({__proto__:null,dispose:function(t){if(t)t.dispose();else{for(var e in go)go.hasOwnProperty(e)&&go[e].dispose();go={}}return this},getInstance:function(t){return go[t]},init:yo,registerPainter:function(t,e){po[t]=e},version:mo}),_o=D,xo=A,wo=b,bo="series\0";function So(t){return t instanceof Array?t:null==t?[]:[t]}function Mo(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,o=n.length;i<o;i++){var r=n[i];!t.emphasis[e].hasOwnProperty(r)&&t[e].hasOwnProperty(r)&&(t.emphasis[e][r]=t[e][r])}}}var Co=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function Io(t){return!xo(t)||wo(t)||t instanceof Date?t:t.value}function To(t,o){o=(o||[]).slice();var r=S(t||[],function(t,e){return{exist:t}});return _o(o,function(t,e){if(xo(t)){for(var n=0;n<r.length;n++)if(!r[n].option&&null!=t.id&&r[n].exist.id===t.id+"")return r[n].option=t,void(o[e]=null);for(n=0;n<r.length;n++){var i=r[n].exist;if(!(r[n].option||null!=i.id&&null!=t.id||null==t.name||ko(t)||ko(i)||i.name!==t.name+""))return r[n].option=t,void(o[e]=null)}}}),_o(o,function(t,e){if(xo(t)){for(var n=0;n<r.length;n++){var i=r[n].exist;if(!r[n].option&&!ko(i)&&null==t.id){r[n].option=t;break}}n>=r.length&&r.push({option:t})}}),r}function Do(t){t=t.name;return t&&t.indexOf(bo)}function ko(t){return xo(t)&&t.id&&0===(t.id+"").indexOf("\0_ec_\0")}function Ao(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?b(t.dataIndex)?S(t.dataIndex,function(t){return e.indexOfRawIndex(t)}):e.indexOfRawIndex(t.dataIndex):null!=t.name?b(t.name)?S(t.name,function(t){return e.indexOfName(t)}):e.indexOfName(t.name):void 0}function Po(){var e="__\0ec_inner_"+Oo+++"_"+Math.random().toFixed(5);return function(t){return t[e]||(t[e]={})}}var Oo=0;function Lo(o,r,a){k(r)&&((t={})[r+"Index"]=0,r=t);var t=a&&a.defaultMainType,s=(!t||Eo(r,t+"Index")||Eo(r,t+"Id")||Eo(r,t+"Name")||(r[t+"Index"]=0),{});return _o(r,function(t,e){var n,i,t=r[e];"dataIndex"===e||"dataIndexInside"===e?s[e]=t:(n=(e=e.match(/^(\w+)(Index|Id|Name)$/)||[])[1],e=(e[2]||"").toLowerCase(),!n||!e||null==t||"index"===e&&"none"===t||a&&a.includeMainTypes&&m(a.includeMainTypes,n)<0||(i={mainType:n},"index"===e&&"all"===t||(i[e]=t),e=o.queryComponents(i),s[n+"Models"]=e,s[n+"Model"]=e[0]))}),s}function Eo(t,e){return t&&t.hasOwnProperty(e)}function zo(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function Bo(t){return"auto"===t?y.domSupported?"html":"richText":t||"html"}var Ro=".",No="___EC__COMPONENT__CONTAINER___";function Fo(t){var e={main:"",sub:""};return t&&(t=t.split(Ro),e.main=t[0]||"",e.sub=t[1]||""),e}function Ho(t,i){(t.$constructor=t).extend=function(e){I&&D(i,function(t){e[t]||console.warn("Method `"+t+"` should be implemented"+(e.type?" in "+e.type:"")+".")});function t(){(e.$constructor||n).apply(this,arguments)}var n=this;return C(t.prototype,e),t.extend=this.extend,t.superCall=Wo,t.superApply=Xo,Q(t,this),t.superClass=n,t}}var Vo=0;function Go(t){var e=["__\0is_clz",Vo++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,I&&p(!t.isInstance,'The method "is" can not be defined.'),t.isInstance=function(t){return!(!t||!t[e])}}function Wo(t,e){var n=ct(arguments,2);return this.superClass.prototype[e].apply(t,n)}function Xo(t,e,n){return this.superClass.prototype[e].apply(t,n)}function Yo(n,t){t=t||{};var i,o={};n.registerClass=function(t,e){var n;return e&&(n=e,p(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(n),'componentType "'+n+'" illegal'),(e=Fo(e)).sub?e.sub!==No&&(function(t){var e=o[t.main];e&&e[No]||(e=o[t.main]={___EC__COMPONENT__CONTAINER___:!0});return e}(e)[e.sub]=t):(I&&o[e.main]&&console.warn(e.main+" exists."),o[e.main]=t)),t},n.getClass=function(t,e,n){var i=o[t];if(i&&i[No]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return i},n.getClassesByMainType=function(t){t=Fo(t);var n=[],t=o[t.main];return t&&t[No]?D(t,function(t,e){e!==No&&n.push(t)}):n.push(t),n},n.hasClass=function(t){return t=Fo(t),!!o[t.main]},n.getAllClassMainTypes=function(){var n=[];return D(o,function(t,e){n.push(e)}),n},n.hasSubTypes=function(t){t=Fo(t);t=o[t.main];return t&&t[No]},n.parseClassType=Fo,t.registerWhenExtend&&(i=n.extend)&&(n.extend=function(t){var e=i.call(this,t);return n.registerClass(e,t.type)})}function Uo(a){for(var t=0;t<a.length;t++)a[t][1]||(a[t][1]=a[t][0]);return function(t,e,n){for(var i={},o=0;o<a.length;o++){var r=a[o][1];e&&0<=m(e,r)||n&&m(n,r)<0||null!=(r=t.getShallow(r))&&(i[a[o][0]]=r)}return i}}var qo=Uo([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),n={getLineStyle:function(t){t=qo(this,t);return t.lineDash=this.getLineDash(t.lineWidth),t},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),n=Math.max(t,2),t=4*t;return"solid"!==e&&null!=e&&("dashed"===e?[t,t]:[n,n])}},jo=Uo([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),o={getAreaStyle:function(t,e){return jo(this,t,e)}},Zo=Math.pow,Ko=Math.sqrt,$o=1e-8,Qo=Ko(3),Jo=wt(),tr=wt(),er=wt();function nr(t){return-$o<t&&t<$o}function ir(t){return $o<t||t<-$o}function or(t,e,n,i,o){var r=1-o;return r*r*(r*t+3*o*e)+o*o*(o*i+3*r*n)}function rr(t,e,n,i,o){var r=1-o;return 3*(((e-t)*r+2*(n-e)*o)*r+(i-n)*o*o)}function ar(t,e,n,i,o){var r,a=6*n-12*e+6*t,i=9*e+3*i-3*t-9*n,n=3*e-3*t,e=0;return nr(i)?ir(a)&&0<=(r=-n/a)&&r<=1&&(o[e++]=r):nr(t=a*a-4*i*n)?o[0]=-a/(2*i):0<t&&(t=(-a-(n=Ko(t)))/(2*i),0<=(r=(-a+n)/(2*i))&&r<=1&&(o[e++]=r),0<=t)&&t<=1&&(o[e++]=t),e}function sr(t,e,n,i,o,r){var a=(e-t)*o+t,e=(n-e)*o+e,n=(i-n)*o+n,s=(e-a)*o+a,e=(n-e)*o+e,o=(e-s)*o+s;r[0]=t,r[1]=a,r[2]=s,r[3]=o,r[4]=o,r[5]=e,r[6]=n,r[7]=i}function lr(t,e,n,i){var o=1-i;return o*(o*t+2*i*e)+i*i*n}function hr(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function ur(t,e,n){n=t+n-2*e;return 0==n?.5:(t-e)/n}function cr(t,e,n,i,o){var r=(e-t)*i+t,e=(n-e)*i+e,i=(e-r)*i+r;o[0]=t,o[1]=r,o[2]=i,o[3]=i,o[4]=e,o[5]=n}var dr=Math.min,fr=Math.max,pr=Math.sin,gr=Math.cos,mr=2*Math.PI,yr=wt(),vr=wt(),_r=wt();function xr(t,e,n){if(0!==t.length){for(var i=t[0],o=i[0],r=i[0],a=i[1],s=i[1],l=1;l<t.length;l++)i=t[l],o=dr(o,i[0]),r=fr(r,i[0]),a=dr(a,i[1]),s=fr(s,i[1]);e[0]=o,e[1]=a,n[0]=r,n[1]=s}}function wr(t,e,n,i,o,r){o[0]=dr(t,n),o[1]=dr(e,i),r[0]=fr(t,n),r[1]=fr(e,i)}var br=[],Sr=[];function Mr(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null}var Y={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Cr=[],Ir=[],Tr=[],Dr=[],kr=Math.min,Ar=Math.max,Pr=Math.cos,Or=Math.sin,Lr=Math.sqrt,Er=Math.abs,zr="undefined"!=typeof Float32Array;function Br(t,e,n,i,o,r,a){var s;if(0!==o)return s=0,!(e+(o=o)<a&&i+o<a||a<e-o&&a<i-o||t+o<r&&n+o<r||r<t-o&&r<n-o)&&(t===n?Math.abs(r-t)<=o/2:(r=(s=(e-i)/(t-n))*r-a+(t*i-n*e)/(t-n))*r/(s*s+1)<=o/2*o/2)}function Rr(t,e,n,i,o,r,a,s,l,h,u){if(0!==l)return!(e+(l=l)<u&&i+l<u&&r+l<u&&s+l<u||u<e-l&&u<i-l&&u<r-l&&u<s-l||t+l<h&&n+l<h&&o+l<h&&a+l<h||h<t-l&&h<n-l&&h<o-l&&h<a-l)&&function(t,e,n,i,o,r,a,s,l,h,u){var c,d,f,p,g=.005,m=1/0;Jo[0]=l,Jo[1]=h;for(var y=0;y<1;y+=.05)tr[0]=or(t,n,o,a,y),tr[1]=or(e,i,r,s,y),(f=Et(Jo,tr))<m&&(c=y,m=f);for(var m=1/0,v=0;v<32&&!(g<1e-4);v++)d=c+g,tr[0]=or(t,n,o,a,p=c-g),tr[1]=or(e,i,r,s,p),f=Et(tr,Jo),0<=p&&f<m?(c=p,m=f):(er[0]=or(t,n,o,a,d),er[1]=or(e,i,r,s,d),p=Et(er,Jo),d<=1&&p<m?(c=d,m=p):g*=.5);return u&&(u[0]=or(t,n,o,a,c),u[1]=or(e,i,r,s,c)),Ko(m)}(t,e,n,i,o,r,a,s,h,u,null)<=l/2}function Nr(t,e,n,i,o,r,a,s,l){if(0!==a)return!(e+(a=a)<l&&i+a<l&&r+a<l||l<e-a&&l<i-a&&l<r-a||t+a<s&&n+a<s&&o+a<s||s<t-a&&s<n-a&&s<o-a)&&function(t,e,n,i,o,r,a,s,l){var h,u=.005,c=1/0;Jo[0]=a,Jo[1]=s;for(var d=0;d<1;d+=.05)tr[0]=lr(t,n,o,d),tr[1]=lr(e,i,r,d),(m=Et(Jo,tr))<c&&(h=d,c=m);for(var c=1/0,f=0;f<32&&!(u<1e-4);f++){var p=h-u,g=h+u,m=(tr[0]=lr(t,n,o,p),tr[1]=lr(e,i,r,p),Et(tr,Jo));0<=p&&m<c?(h=p,c=m):(er[0]=lr(t,n,o,g),er[1]=lr(e,i,r,g),p=Et(er,Jo),g<=1&&p<c?(h=g,c=p):u*=.5)}return l&&(l[0]=lr(t,n,o,h),l[1]=lr(e,i,r,h)),Ko(c)}(t,e,n,i,o,r,s,l,null)<=a/2}Mr.prototype={constructor:Mr,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e,n){this._ux=Er((n=n||0)/dn/t)||0,this._uy=Er(n/dn/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return(this._ctx=t)&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(Y.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var n=Er(t-this._xi)>this._ux||Er(e-this._yi)>this._uy||this._len<5;return this.addData(Y.L,t,e),this._ctx&&n&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),n&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,n,i,o,r){return this.addData(Y.C,t,e,n,i,o,r),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,n,i,o,r):this._ctx.bezierCurveTo(t,e,n,i,o,r)),this._xi=o,this._yi=r,this},quadraticCurveTo:function(t,e,n,i){return this.addData(Y.Q,t,e,n,i),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,n,i):this._ctx.quadraticCurveTo(t,e,n,i)),this._xi=n,this._yi=i,this},arc:function(t,e,n,i,o,r){return this.addData(Y.A,t,e,n,n,i,o-i,0,r?0:1),this._ctx&&this._ctx.arc(t,e,n,i,o,r),this._xi=Pr(o)*n+t,this._yi=Or(o)*n+e,this},arcTo:function(t,e,n,i,o){return this._ctx&&this._ctx.arcTo(t,e,n,i,o),this},rect:function(t,e,n,i){return this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Y.R,t,e,n,i),this},closePath:function(){this.addData(Y.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,n),t.closePath()),this._xi=e,this._yi=n,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t;for(var e=this._dashIdx=0,n=0;n<t.length;n++)e+=t[n];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!zr||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},appendPath:function(t){for(var e=(t=t instanceof Array?t:[t]).length,n=0,i=this._len,o=0;o<e;o++)n+=t[o].len();zr&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(o=0;o<e;o++)for(var r=t[o].data,a=0;a<r.length;a++)this.data[i++]=r[a];this._len=i},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var n=0;n<arguments.length;n++)e[this._len++]=arguments[n];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var n,i,o=this._dashSum,r=this._dashOffset,a=this._lineDash,s=this._ctx,l=this._xi,h=this._yi,u=t-l,c=e-h,d=Lr(u*u+c*c),f=l,p=h,g=a.length;for(r<0&&(r=o+r),f-=(r%=o)*(u/=d),p-=r*(c/=d);0<u&&f<=t||u<0&&t<=f||0===u&&(0<c&&p<=e||c<0&&e<=p);)f+=u*(n=a[i=this._dashIdx]),p+=c*n,this._dashIdx=(i+1)%g,0<u&&f<l||u<0&&l<f||0<c&&p<h||c<0&&h<p||s[i%2?"moveTo":"lineTo"]((0<=u?kr:Ar)(f,t),(0<=c?kr:Ar)(p,e));this._dashOffset=-Lr((u=f-t)*u+(c=p-e)*c)},_dashedBezierTo:function(t,e,n,i,o,r){var a,s,l,h,u,c=this._dashSum,d=this._dashOffset,f=this._lineDash,p=this._ctx,g=this._xi,m=this._yi,y=or,v=0,_=this._dashIdx,x=f.length,w=0;for(d<0&&(d=c+d),d%=c,a=0;a<1;a+=.1)s=y(g,t,n,o,a+.1)-y(g,t,n,o,a),l=y(m,e,i,r,a+.1)-y(m,e,i,r,a),v+=Lr(s*s+l*l);for(;_<x&&!(d<(w+=f[_]));_++);for(a=(w-d)/v;a<=1;)h=y(g,t,n,o,a),u=y(m,e,i,r,a),_%2?p.moveTo(h,u):p.lineTo(h,u),a+=f[_]/v,_=(_+1)%x;_%2!=0&&p.lineTo(o,r),this._dashOffset=-Lr((s=o-h)*s+(l=r-u)*l)},_dashedQuadraticTo:function(t,e,n,i){var o=n,r=i;n=(n+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,n,i,o,r)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,zr)&&(this.data=new Float32Array(t))},getBoundingRect:function(){Cr[0]=Cr[1]=Tr[0]=Tr[1]=Number.MAX_VALUE,Ir[0]=Ir[1]=Dr[0]=Dr[1]=-Number.MAX_VALUE;for(var t,e=this.data,n=0,i=0,o=0,r=0,a=0;a<e.length;){var B=e[a++];switch(1===a&&(o=n=e[a],r=i=e[a+1]),B){case Y.M:n=o=e[a++],i=r=e[a++],Tr[0]=o,Tr[1]=r,Dr[0]=o,Dr[1]=r;break;case Y.L:wr(n,i,e[a],e[a+1],Tr,Dr),n=e[a++],i=e[a++];break;case Y.C:V=H=y=s=F=m=g=p=f=d=N=R=c=u=h=l=void 0;var s,l=n,h=i,u=e[a++],c=e[a++],R=e[a++],N=e[a++],d=e[a],f=e[a+1],p=Tr,g=Dr,m=ar,F=or,y=m(l,u,R,d,br);for(p[0]=1/0,p[1]=1/0,g[0]=-1/0,g[1]=-1/0,s=0;s<y;s++){var H=F(l,u,R,d,br[s]);p[0]=dr(H,p[0]),g[0]=fr(H,g[0])}for(y=m(h,c,N,f,Sr),s=0;s<y;s++){var V=F(h,c,N,f,Sr[s]);p[1]=dr(V,p[1]),g[1]=fr(V,g[1])}p[0]=dr(l,p[0]),g[0]=fr(l,g[0]),p[0]=dr(d,p[0]),g[0]=fr(d,g[0]),p[1]=dr(h,p[1]),g[1]=fr(h,g[1]),p[1]=dr(f,p[1]),g[1]=fr(f,g[1]),n=e[a++],i=e[a++];break;case Y.Q:m=n,O=i,M=e[a++],x=e[a++],S=e[a],v=e[a+1],b=Tr,C=Dr,t=w=t=_=void 0,_=lr,t=fr(dr((w=ur)(m,M,S),1),0),w=fr(dr(w(O,x,v),1),0),M=_(m,M,S,t),t=_(O,x,v,w),b[0]=dr(m,S,M),b[1]=dr(O,v,t),C[0]=fr(m,S,M),C[1]=fr(O,v,t),n=e[a++],i=e[a++];break;case Y.A:var v,_=e[a++],x=e[a++],w=e[a++],b=e[a++],S=e[a++],M=e[a++]+S,C=(a+=1,1-e[a++]),I=(1===a&&(o=Pr(S)*w+_,r=Or(S)*b+x),z=v=W=G=E=L=O=P=A=k=D=T=I=void 0,_),T=x,D=w,k=b,A=S,P=M,O=C,L=Tr,E=Dr,G=Bt,W=Rt;if((v=Math.abs(A-P))%mr<1e-4&&1e-4<v)L[0]=I-D,L[1]=T-k,E[0]=I+D,E[1]=T+k;else{yr[0]=gr(A)*D+I,yr[1]=pr(A)*k+T,vr[0]=gr(P)*D+I,vr[1]=pr(P)*k+T,G(L,yr,vr),W(E,yr,vr),(A%=mr)<0&&(A+=mr),(P%=mr)<0&&(P+=mr),P<A&&!O?P+=mr:A<P&&O&&(A+=mr),O&&(v=P,P=A,A=v);for(var z=0;z<P;z+=Math.PI/2)A<z&&(_r[0]=gr(z)*D+I,_r[1]=pr(z)*k+T,G(L,_r,L),W(E,_r,E))}n=Pr(M)*w+_,i=Or(M)*b+x;break;case Y.R:wr(o=n=e[a++],r=i=e[a++],o+e[a++],r+e[a++],Tr,Dr);break;case Y.Z:n=o,i=r}Bt(Cr,Cr,Tr),Rt(Ir,Ir,Dr)}return 0===a&&(Cr[0]=Cr[1]=Ir[0]=Ir[1]=0),new X(Cr[0],Cr[1],Ir[0]-Cr[0],Ir[1]-Cr[1])},rebuildPath:function(t){for(var e,n,i,o,r=this.data,a=this._ux,s=this._uy,l=this._len,h=0;h<l;){var u=r[h++];switch(1===h&&(e=i=r[h],n=o=r[h+1]),u){case Y.M:e=i=r[h++],n=o=r[h++],t.moveTo(i,o);break;case Y.L:c=r[h++],d=r[h++],(Er(c-i)>a||Er(d-o)>s||h===l-1)&&(t.lineTo(c,d),i=c,o=d);break;case Y.C:t.bezierCurveTo(r[h++],r[h++],r[h++],r[h++],r[h++],r[h++]),i=r[h-2],o=r[h-1];break;case Y.Q:t.quadraticCurveTo(r[h++],r[h++],r[h++],r[h++]),i=r[h-2],o=r[h-1];break;case Y.A:var c=r[h++],d=r[h++],f=r[h++],p=r[h++],g=r[h++],m=r[h++],y=r[h++],v=r[h++],_=p<f?f:p,x=p<f?1:f/p,w=p<f?p/f:1,m=g+m;.001<Math.abs(f-p)?(t.translate(c,d),t.rotate(y),t.scale(x,w),t.arc(0,0,_,g,m,1-v),t.scale(1/x,1/w),t.rotate(-y),t.translate(-c,-d)):t.arc(c,d,_,g,m,1-v),1===h&&(e=Pr(g)*f+c,n=Or(g)*p+d),i=Pr(m)*f+c,o=Or(m)*p+d;break;case Y.R:e=i=r[h],n=o=r[h+1],t.rect(r[h++],r[h++],r[h++],r[h++]);break;case Y.Z:t.closePath(),i=e,o=n}}}},Mr.CMD=Y;var Fr=2*Math.PI;function Hr(t){return(t%=Fr)<0&&(t+=Fr),t}var Vr=2*Math.PI;function Gr(t,e,n,i,o,r){var a;return e<r&&i<r||r<e&&r<i||i===e?0:(a=i<e?1:-1,1!=(r=(r-e)/(i-e))&&0!=r||(a=i<e?.5:-.5),(i=r*(n-t)+t)===o?1/0:o<i?a:0)}var Wr=Mr.CMD,Xr=2*Math.PI,Yr=1e-4;var Ur=[-1,-1,-1],qr=[-1,-1];function jr(t,e,n,i,o,r,a,s,l,h){if(e<h&&i<h&&r<h&&s<h||h<e&&h<i&&h<r&&h<s)return 0;p=Ur,f=(f=s)+3*((c=i)-(d=r))-(u=e),y=(d=3*(d-2*c+u))*(c=3*(c-u))-9*f*(u=u-(h=h)),u=c*c-3*d*u,v=0,nr(h=d*d-3*f*c)&&nr(y)?nr(d)?p[0]=0:0<=(g=-c/d)&&g<=1&&(p[v++]=g):nr(c=y*y-4*h*u)?(m=-(u=y/h)/2,0<=(g=-d/f+u)&&g<=1&&(p[v++]=g),0<=m&&m<=1&&(p[v++]=m)):0<c?(c=h*d+1.5*f*(-y-(u=Ko(c))),0<=(g=(-d-((u=(u=h*d+1.5*f*(-y+u))<0?-Zo(-u,1/3):Zo(u,1/3))+(c=c<0?-Zo(-c,1/3):Zo(c,1/3))))/(3*f))&&g<=1&&(p[v++]=g)):(u=(2*h*d-3*f*y)/(2*Ko(h*h*h)),c=Math.acos(u)/3,g=(-d-2*(y=Ko(h))*(u=Math.cos(c)))/(3*f),m=(-d+y*(u+Qo*Math.sin(c)))/(3*f),h=(-d+y*(u-Qo*Math.sin(c)))/(3*f),0<=g&&g<=1&&(p[v++]=g),0<=m&&m<=1&&(p[v++]=m),0<=h&&h<=1&&(p[v++]=h));var u,c,d,f,p,g,m,y,v,_=v;if(0===_)return 0;for(var x,w,b=0,S=-1,M=0;M<_;M++){var C=Ur[M],I=0===C||1===C?.5:1;or(t,n,o,a,C)<l||(S<0&&(S=ar(e,i,r,s,qr),qr[1]<qr[0]&&1<S&&(w=void 0,w=qr[0],qr[0]=qr[1],qr[1]=w),w=or(e,i,r,s,qr[0]),1<S)&&(x=or(e,i,r,s,qr[1])),2===S?C<qr[0]?b+=w<e?I:-I:C<qr[1]?b+=x<w?I:-I:b+=s<x?I:-I:C<qr[0]?b+=w<e?I:-I:b+=s<w?I:-I)}return b}function Zr(t,e,n,i,o,r,a,s){if(e<s&&i<s&&r<s||s<e&&s<i&&s<r)return 0;c=Ur,u=(l=e)-2*(h=i)+(u=r),h=2*(h-l),l-=s=s,s=0,nr(u)?ir(h)&&0<=(d=-l/h)&&d<=1&&(c[s++]=d):nr(l=h*h-4*u*l)?0<=(d=-h/(2*u))&&d<=1&&(c[s++]=d):0<l&&(f=(-h-(l=Ko(l)))/(2*u),0<=(d=(-h+l)/(2*u))&&d<=1&&(c[s++]=d),0<=f)&&f<=1&&(c[s++]=f);var l,h,u,c,d,f,p=s;if(0===p)return 0;var g=ur(e,i,r);if(0<=g&&g<=1){for(var m=0,y=lr(e,i,r,g),v=0;v<p;v++){var _=0===Ur[v]||1===Ur[v]?.5:1;lr(t,n,o,Ur[v])<a||(Ur[v]<g?m+=y<e?_:-_:m+=r<y?_:-_)}return m}return _=0===Ur[0]||1===Ur[0]?.5:1,lr(t,n,o,Ur[0])<a?0:r<e?_:-_}function Kr(t,e,n,i,o){for(var r,a,s=0,l=0,h=0,u=0,c=0,d=0;d<t.length;){var f=t[d++];switch(f===Wr.M&&1<d&&(n||(s+=Gr(l,h,u,c,i,o))),1===d&&(u=l=t[d],c=h=t[d+1]),f){case Wr.M:l=u=t[d++],h=c=t[d++];break;case Wr.L:if(n){if(Br(l,h,t[d],t[d+1],e,i,o))return!0}else s+=Gr(l,h,t[d],t[d+1],i,o)||0;l=t[d++],h=t[d++];break;case Wr.C:if(n){if(Rr(l,h,t[d++],t[d++],t[d++],t[d++],t[d],t[d+1],e,i,o))return!0}else s+=jr(l,h,t[d++],t[d++],t[d++],t[d++],t[d],t[d+1],i,o)||0;l=t[d++],h=t[d++];break;case Wr.Q:if(n){if(Nr(l,h,t[d++],t[d++],t[d],t[d+1],e,i,o))return!0}else s+=Zr(l,h,t[d++],t[d++],t[d],t[d+1],i,o)||0;l=t[d++],h=t[d++];break;case Wr.A:var p=t[d++],g=t[d++],m=t[d++],y=t[d++],v=t[d++],_=t[d++],x=(d+=1,1-t[d++]),w=Math.cos(v)*m+p,b=Math.sin(v)*y+g,S=(1<d?s+=Gr(l,h,w,b,i,o):(u=w,c=b),(i-p)*y/m+p);if(n){if(function(t,e,n,i,o,r,a,s,l){if(0!==a)return a=a,s-=t,l-=e,!(n<(t=Math.sqrt(s*s+l*l))-a||t+a<n)&&(Math.abs(i-o)%Vr<1e-4||((o=r?(e=i,i=Hr(o),Hr(e)):(i=Hr(i),Hr(o)))<i&&(o+=Vr),(t=Math.atan2(l,s))<0&&(t+=Vr),i<=t&&t<=o)||i<=t+Vr&&t+Vr<=o)}(p,g,y,v,v+_,x,e,S,o))return!0}else s+=function(t,e,n,i,o,r,a,s){if(n<(s-=e)||s<-n)return 0;if(e=Math.sqrt(n*n-s*s),Ur[0]=-e,Ur[1]=e,(n=Math.abs(i-o))<1e-4)return 0;if(n%Xr<1e-4)return o=Xr,u=r?1:-1,a>=Ur[i=0]+t&&a<=Ur[1]+t?u:0;(o=r?(e=i,i=Hr(o),Hr(e)):(i=Hr(i),Hr(o)))<i&&(o+=Xr);for(var l=0,h=0;h<2;h++){var u,c=Ur[h];a<c+t&&(u=r?1:-1,i<=(c=(c=Math.atan2(s,c))<0?Xr+c:c)&&c<=o||i<=c+Xr&&c+Xr<=o)&&(l+=u=c>Math.PI/2&&c<1.5*Math.PI?-u:u)}return l}(p,g,y,v,v+_,x,S,o);l=Math.cos(v+_)*m+p,h=Math.sin(v+_)*y+g;break;case Wr.R:u=l=t[d++],c=h=t[d++],w=u+t[d++],b=c+t[d++];if(n){if(Br(u,c,w,c,e,i,o)||Br(w,c,w,b,e,i,o)||Br(w,b,u,b,e,i,o)||Br(u,b,u,c,e,i,o))return!0}else s=(s+=Gr(w,c,w,b,i,o))+Gr(u,b,u,c,i,o);break;case Wr.Z:if(n){if(Br(l,h,u,c,e,i,o))return!0}else s+=Gr(l,h,u,c,i,o);l=u,h=c}}return n||(r=h,a=c,Math.abs(r-a)<Yr)||(s+=Gr(l,h,u,c,i,o)||0),0!==s}var $r=Xn.prototype.getCanvasPattern,Qr=Math.abs,Jr=new Mr(!0);function a(t){Hi.call(this,t),this.path=null}a.prototype={constructor:a,type:"path",__dirtyPath:!0,strokeContainThreshold:5,segmentIgnoreThreshold:0,subPixelOptimize:!1,brush:function(t,e){var n,i=this.style,o=this.path||Jr,r=i.hasStroke(),a=i.hasFill(),s=i.fill,l=i.stroke,h=a&&!!s.colorStops,u=r&&!!l.colorStops,c=a&&!!s.image,d=r&&!!l.image,e=(i.bind(t,this,e),this.setTransform(t),this.__dirty&&(h&&(f=f||this.getBoundingRect(),this._fillGradient=i.getGradient(t,s,f)),u)&&(f=f||this.getBoundingRect(),this._strokeGradient=i.getGradient(t,l,f)),h?t.fillStyle=this._fillGradient:c&&(t.fillStyle=$r.call(s,t)),u?t.strokeStyle=this._strokeGradient:d&&(t.strokeStyle=$r.call(l,t)),i.lineDash),f=i.lineDashOffset,h=!!t.setLineDash,c=this.getGlobalScale();o.setScale(c[0],c[1],this.segmentIgnoreThreshold),this.__dirtyPath||e&&!h&&r?(o.beginPath(t),e&&!h&&(o.setLineDash(e),o.setLineDashOffset(f)),this.buildPath(o,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),a&&(null!=i.fillOpacity?(n=t.globalAlpha,t.globalAlpha=i.fillOpacity*i.opacity,o.fill(t),t.globalAlpha=n):o.fill(t)),e&&h&&(t.setLineDash(e),t.lineDashOffset=f),r&&(null!=i.strokeOpacity?(n=t.globalAlpha,t.globalAlpha=i.strokeOpacity*i.opacity,o.stroke(t),t.globalAlpha=n):o.stroke(t)),e&&h&&t.setLineDash([]),null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(t,e,n){},createPathProxy:function(){this.path=new Mr},getBoundingRect:function(){var t,e,n=this._rect,i=this.style,o=!n;return o&&(t=(t=this.path)||(this.path=new Mr),this.__dirtyPath&&(t.beginPath(),this.buildPath(t,this.shape,!1)),n=t.getBoundingRect()),this._rect=n,i.hasStroke()?(t=this._rectWithStroke||(this._rectWithStroke=n.clone()),(this.__dirty||o)&&(t.copy(n),o=i.lineWidth,e=i.strokeNoScale?this.getLineScale():1,i.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),1e-10<e)&&(t.width+=o/e,t.height+=o/e,t.x-=o/e/2,t.y-=o/e/2),t):n},contain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),o=this.style;if(t=n[0],e=n[1],i.contain(t,e)){n=this.path.data;if(o.hasStroke()){var i=o.lineWidth,r=o.strokeNoScale?this.getLineScale():1;if(1e-10<r&&(o.hasFill()||(i=Math.max(i,this.strokeContainThreshold)),Kr(n,i/r,!0,t,e)))return!0}if(o.hasFill())return Kr(n,0,!1,t,e)}return!1},dirty:function(t){(t=null==t?!0:t)&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Hi.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var n=this.shape;if(n){if(A(t))for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);else n[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&1e-10<Qr(t[0]-1)&&1e-10<Qr(t[3]-1)?Math.sqrt(Qr(t[0]*t[3]-t[2]*t[1])):1}},a.extend=function(o){function t(t){a.call(this,t),o.style&&this.style.extendFrom(o.style,!1);var e=o.shape;if(e){this.shape=this.shape||{};var n,i=this.shape;for(n in e)!i.hasOwnProperty(n)&&e.hasOwnProperty(n)&&(i[n]=e[n])}o.init&&o.init.call(this,t)}for(var e in Q(t,a),o)"style"!==e&&"shape"!==e&&(t.prototype[e]=o[e]);return t},Q(a,Hi);var ta=Mr.CMD,ea=[[],[],[]],na=Math.sqrt,ia=Math.atan2;function oa(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(ua(t,e))}var ra=Math.sqrt,aa=Math.sin,sa=Math.cos,la=Math.PI,ha=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},ua=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(ha(t)*ha(e))};function ca(t,e,n,i,o,r,a,s,l,h,u){var l=l*(la/180),c=sa(l)*(t-n)/2+aa(l)*(e-i)/2,d=-1*aa(l)*(t-n)/2+sa(l)*(e-i)/2,f=c*c/(a*a)+d*d/(s*s),f=(1<f&&(a*=ra(f),s*=ra(f)),(o===r?-1:1)*ra((a*a*(s*s)-a*a*(d*d)-s*s*(c*c))/(a*a*(d*d)+s*s*(c*c)))||0),o=f*a*d/s,f=f*-s*c/a,t=(t+n)/2+sa(l)*o-aa(l)*f,n=(e+i)/2+aa(l)*o+sa(l)*f,e=oa([1,0],[(c-o)/a,(d-f)/s]),i=[(c-o)/a,(d-f)/s],c=[(-1*c-o)/a,(-1*d-f)/s],o=oa(i,c);ua(i,c)<=-1&&(o=la),1<=ua(i,c)&&(o=0),0===r&&0<o&&(o-=2*la),1===r&&o<0&&(o+=2*la),u.addData(h,t,n,a,s,e,o,l,r)}var da=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,fa=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function pa(t,e){var x=function(t){if(!t)return new Mr;for(var e,n=0,i=0,o=n,r=i,a=new Mr,s=Mr.CMD,l=t.match(da),h=0;h<l.length;h++){for(var u,c=l[h],d=c.charAt(0),f=c.match(fa)||[],p=f.length,g=0;g<p;g++)f[g]=parseFloat(f[g]);for(var m=0;m<p;){var y,v,_,x,w,b=n,S=i;switch(d){case"l":n+=f[m++],i+=f[m++],u=s.L,a.addData(u,n,i);break;case"L":n=f[m++],i=f[m++],u=s.L,a.addData(u,n,i);break;case"m":n+=f[m++],i+=f[m++],u=s.M,a.addData(u,n,i),o=n,r=i,d="l";break;case"M":n=f[m++],i=f[m++],u=s.M,a.addData(u,n,i),o=n,r=i,d="L";break;case"h":n+=f[m++],u=s.L,a.addData(u,n,i);break;case"H":n=f[m++],u=s.L,a.addData(u,n,i);break;case"v":i+=f[m++],u=s.L,a.addData(u,n,i);break;case"V":i=f[m++],u=s.L,a.addData(u,n,i);break;case"C":u=s.C,a.addData(u,f[m++],f[m++],f[m++],f[m++],f[m++],f[m++]),n=f[m-2],i=f[m-1];break;case"c":u=s.C,a.addData(u,f[m++]+n,f[m++]+i,f[m++]+n,f[m++]+i,f[m++]+n,f[m++]+i),n+=f[m-2],i+=f[m-1];break;case"S":var M=n,C=i,I=a.len(),T=a.data;e===s.C&&(M+=n-T[I-4],C+=i-T[I-3]),u=s.C,b=f[m++],S=f[m++],n=f[m++],i=f[m++],a.addData(u,M,C,b,S,n,i);break;case"s":M=n,C=i;I=a.len(),T=a.data;e===s.C&&(M+=n-T[I-4],C+=i-T[I-3]),u=s.C,b=n+f[m++],S=i+f[m++],n+=f[m++],i+=f[m++],a.addData(u,M,C,b,S,n,i);break;case"Q":b=f[m++],S=f[m++],n=f[m++],i=f[m++],u=s.Q,a.addData(u,b,S,n,i);break;case"q":b=f[m++]+n,S=f[m++]+i,n+=f[m++],i+=f[m++],u=s.Q,a.addData(u,b,S,n,i);break;case"T":M=n,C=i;I=a.len(),T=a.data;e===s.Q&&(M+=n-T[I-4],C+=i-T[I-3]),n=f[m++],i=f[m++],u=s.Q,a.addData(u,M,C,n,i);break;case"t":M=n,C=i;I=a.len(),T=a.data;e===s.Q&&(M+=n-T[I-4],C+=i-T[I-3]),n+=f[m++],i+=f[m++],u=s.Q,a.addData(u,M,C,n,i);break;case"A":y=f[m++],v=f[m++],_=f[m++],x=f[m++],w=f[m++],ca(b=n,S=i,n=f[m++],i=f[m++],x,w,y,v,_,u=s.A,a);break;case"a":y=f[m++],v=f[m++],_=f[m++],x=f[m++],w=f[m++],ca(b=n,S=i,n+=f[m++],i+=f[m++],x,w,y,v,_,u=s.A,a)}}"z"!==d&&"Z"!==d||(u=s.Z,a.addData(u),n=o,i=r),e=u}return a.toStatic(),a}(t);return(e=e||{}).buildPath=function(t){var e;t.setData?(t.setData(x.data),(e=t.getContext())&&t.rebuildPath(e)):x.rebuildPath(e=t)},e.applyTransform=function(t){for(var e,n,i,o,r=t,a=x.data,s=ta.M,l=ta.C,h=ta.L,u=ta.R,c=ta.A,d=ta.Q,f=0,p=0;f<a.length;){switch(e=a[f++],p=f,n=0,e){case s:case h:n=1;break;case l:n=3;break;case d:n=2;break;case c:var g=r[4],m=r[5],y=na(r[0]*r[0]+r[1]*r[1]),v=na(r[2]*r[2]+r[3]*r[3]),_=ia(-r[1]/v,r[0]/y);a[f]*=y,a[f++]+=g,a[f]*=v,a[f++]+=m,a[f++]*=y,a[f++]*=v,a[f++]+=_,a[f++]+=_,p=f+=2;break;case u:o[0]=a[f++],o[1]=a[f++],zt(o,o,r),a[p++]=o[0],a[p++]=o[1],o[0]+=a[f++],o[1]+=a[f++],zt(o,o,r),a[p++]=o[0],a[p++]=o[1]}for(i=0;i<n;i++)(o=ea[i])[0]=a[f++],o[1]=a[f++],zt(o,o,r),a[p++]=o[0],a[p++]=o[1]}this.dirty(!0)},e}function ga(t){Hi.call(this,t)}ga.prototype={constructor:ga,type:"text",brush:function(t,e){var n=this.style,i=(this.__dirty&&Mi(n),n.fill=n.stroke=n.shadowBlur=n.shadowColor=n.shadowOffsetX=n.shadowOffsetY=null,n.text);null!=i&&(i+=""),Ri(i,n)?(this.setTransform(t),Ii(this,t,i,n,null,e),this.restoreTransform(t)):t.__attrCachedBy=Bn.NONE},getBoundingRect:function(){var t,e=this.style;return this.__dirty&&Mi(e),this._rect||(e.text,(t=ai(e.text+"",e.font,e.textAlign,e.textVerticalAlign,e.textPadding,e.textLineHeight,e.rich)).x+=e.x||0,t.y+=e.y||0,Li(e.textStroke,e.textStrokeWidth)&&(e=e.textStrokeWidth,t.x-=e/2,t.y-=e/2,t.width+=e,t.height+=e),this._rect=t),this._rect}},Q(ga,Hi);var ma=a.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,n){n&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),ya=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]];function va(s){return y.browser.ie&&11<=y.browser.version?function(){var t,e=this.__clipPaths,n=this.style;if(e)for(var i=0;i<e.length;i++){var o=e[i],r=o&&o.shape,o=o&&o.type;if(r&&("sector"===o&&r.startAngle===r.endAngle||"rect"===o&&(!r.width||!r.height))){for(var a=0;a<ya.length;a++)ya[a][2]=n[ya[a][0]],n[ya[a][0]]=ya[a][1];t=!0;break}}if(s.apply(this,arguments),t)for(a=0;a<ya.length;a++)n[ya[a][0]]=ya[a][2]}:s}var _a=a.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:va(a.prototype.brush),buildPath:function(t,e){var n=e.cx,i=e.cy,o=Math.max(e.r0||0,0),r=Math.max(e.r,0),a=e.startAngle,s=e.endAngle,e=e.clockwise,l=Math.cos(a),h=Math.sin(a);t.moveTo(l*o+n,h*o+i),t.lineTo(l*r+n,h*r+i),t.arc(n,i,r,a,s,!e),t.lineTo(Math.cos(s)*o+n,Math.sin(s)*o+i),0!==o&&t.arc(n,i,o,s,a,e),t.closePath()}}),xa=a.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var n=e.cx,i=e.cy,o=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,o,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,o,!0)}});function wa(t,e,n,i,o,r,a){t=.5*(n-t),i=.5*(i-e);return(2*(e-n)+t+i)*a+(-3*(e-n)-2*t-i)*r+t*o+e}function ba(t,e,n){var i=e.points,o=e.smooth;if(i&&2<=i.length){if(o&&"spline"!==o)for(var r=function(t,e,n,i){var o,r,a=[],s=[],l=[],h=[];if(i){for(var u=[1/0,1/0],c=[-1/0,-1/0],d=0,f=t.length;d<f;d++)Bt(u,u,t[d]),Rt(c,c,t[d]);Bt(u,u,i[0]),Rt(c,c,i[1])}for(d=0,f=t.length;d<f;d++){var p=t[d];if(n)o=t[d?d-1:f-1],r=t[(d+1)%f];else{if(0===d||d===f-1){a.push(St(t[d]));continue}o=t[d-1],r=t[d+1]}It(s,r,o),kt(s,s,e);var g=Pt(p,o),m=Pt(p,r),y=g+m,y=(0!==y&&(g/=y,m/=y),kt(l,s,-g),kt(h,s,m),Mt([],p,l)),g=Mt([],p,h);i&&(Rt(y,y,u),Bt(y,y,c),Rt(g,g,u),Bt(g,g,c)),a.push(y),a.push(g)}return n&&a.push(a.shift()),a}(i,o,n,e.smoothConstraint),a=(t.moveTo(i[0][0],i[0][1]),i.length),s=0;s<(n?a:a-1);s++){var l=r[2*s],h=r[2*s+1],u=i[(s+1)%a];t.bezierCurveTo(l[0],l[1],h[0],h[1],u[0],u[1])}else{"spline"===o&&(i=function(t,e){for(var n=t.length,i=[],o=0,r=1;r<n;r++)o+=Pt(t[r-1],t[r]);for(var a=(a=o/2)<n?n:a,r=0;r<a;r++){var s,l,h=r/(a-1)*(e?n:n-1),u=Math.floor(h),h=h-u,c=t[u%n],u=e?(s=t[(u-1+n)%n],l=t[(u+1)%n],t[(u+2)%n]):(s=t[0===u?u:u-1],l=t[n-2<u?n-1:u+1],t[n-3<u?n-1:u+2]),d=h*h,f=h*d;i.push([wa(s[0],c[0],l[0],u[0],h,d,f),wa(s[1],c[1],l[1],u[1],h,d,f)])}return i}(i,n)),t.moveTo(i[0][0],i[0][1]);for(var s=1,c=i.length;s<c;s++)t.lineTo(i[s][0],i[s][1])}n&&t.closePath()}}var Sa=a.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){ba(t,e,!0)}}),Ma=a.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){ba(t,e,!1)}}),Ca=Math.round;function Ia(t,e,n){var i,o,r;e&&(i=e.x1,o=e.x2,r=e.y1,e=e.y2,t.x1=i,t.x2=o,t.y1=r,t.y2=e,n=n&&n.lineWidth)&&(Ca(2*i)===Ca(2*o)&&(t.x1=t.x2=Da(i,n,!0)),Ca(2*r)===Ca(2*e))&&(t.y1=t.y2=Da(r,n,!0))}function Ta(t,e,n){var i,o,r;e&&(i=e.x,o=e.y,r=e.width,e=e.height,t.x=i,t.y=o,t.width=r,t.height=e,n=n&&n.lineWidth)&&(t.x=Da(i,n,!0),t.y=Da(o,n,!0),t.width=Math.max(Da(i+r,n,!1)-t.x,0===r?0:1),t.height=Math.max(Da(o+e,n,!1)-t.y,0===e?0:1))}function Da(t,e,n){var i;return e?((i=Ca(2*t))+Ca(e))%2==0?i/2:(i+(n?1:-1))/2:t}var ka={},O=a.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var n,i,o,r;this.subPixelOptimize?(Ta(ka,e,this.style),n=ka.x,i=ka.y,o=ka.width,r=ka.height,ka.r=e.r,e=ka):(n=e.x,i=e.y,o=e.width,r=e.height),e.r?yi(t,e):t.rect(n,i,o,r),t.closePath()}}),Aa={},Pa=a.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n,i,o,r=(this.subPixelOptimize?(Ia(Aa,e,this.style),n=Aa.x1,i=Aa.y1,o=Aa.x2,Aa):(n=e.x1,i=e.y1,o=e.x2,e)).y2,e=e.percent;0!==e&&(t.moveTo(n,i),e<1&&(o=n*(1-e)+o*e,r=i*(1-e)+r*e),t.lineTo(o,r))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),Oa=[];function La(t,e,n){var i=t.cpx2,o=t.cpy2;return null===i||null===o?[(n?rr:or)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?rr:or)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?hr:lr)(t.x1,t.cpx1,t.x2,e),(n?hr:lr)(t.y1,t.cpy1,t.y2,e)]}function Ea(t){this.colorStops=t||[]}function za(t,e,n,i,o,r){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==i?0:i,this.type="linear",this.global=r||!1,Ea.call(this,o)}function Ba(t,e,n,i,o){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=o||!1,Ea.call(this,i)}var r=a.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,o=e.x2,r=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,h=e.cpy2,e=e.percent;0!==e&&(t.moveTo(n,i),null==l||null==h?(e<1&&(cr(n,a,o,e,Oa),a=Oa[1],o=Oa[2],cr(i,s,r,e,Oa),s=Oa[1],r=Oa[2]),t.quadraticCurveTo(a,s,o,r)):(e<1&&(sr(n,a,l,o,e,Oa),a=Oa[1],l=Oa[2],o=Oa[3],sr(i,s,h,r,e,Oa),s=Oa[1],h=Oa[2],r=Oa[3]),t.bezierCurveTo(a,s,l,h,o,r)))},pointAt:function(t){return La(this.shape,t,!1)},tangentAt:function(t){t=La(this.shape,t,!0);return At(t,t)}}),Ra=a.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.cx,i=e.cy,o=Math.max(e.r,0),r=e.startAngle,a=e.endAngle,e=e.clockwise,s=Math.cos(r),l=Math.sin(r);t.moveTo(s*o+n,l*o+i),t.arc(n,i,o,r,a,!e)}}),s=a.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,n=0;n<e.length;n++)t=t||e[n].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},buildPath:function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),a.prototype.getBoundingRect.call(this)}});Ea.prototype={constructor:Ea,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}},za.prototype={constructor:za},Q(za,Ea);function Na(t){Hi.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}Ba.prototype={constructor:Ba},Q(Ba,Ea),Na.prototype.incremental=!0,Na.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},Na.prototype.addDisplayable=function(t,e){(e?this._temporaryDisplayables:this._displayables).push(t),this.dirty()},Na.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},Na.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},Na.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var e,t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},Na.prototype.brush=function(t,e){for(var n=this._cursor;n<this._displayables.length;n++)(i=this._displayables[n]).beforeBrush&&i.beforeBrush(t),i.brush(t,n===this._cursor?null:this._displayables[n-1]),i.afterBrush&&i.afterBrush(t);this._cursor=n;for(var i,n=0;n<this._temporaryDisplayables.length;n++)(i=this._temporaryDisplayables[n]).beforeBrush&&i.beforeBrush(t),i.brush(t,0===n?null:this._temporaryDisplayables[n-1]),i.afterBrush&&i.afterBrush(t);this._temporaryDisplayables=[],this.notClear=!0};var Fa=[],Ha=(Na.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new X(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(Fa)),t.union(i)}this._rect=t}return this._rect},Na.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},Q(Na,Hi),Math.max),Va=Math.min,Ga={},Wa=1,Xa="emphasis",Ya="normal",Ua=1,qa={},ja={};function Za(t){return a.extend(t)}function Ka(t,e){ja[t]=e}function $a(t,e,n,i){t=new a(pa(t,e));return n&&("center"===i&&(n=Ja(n,t.getBoundingRect())),es(t,n)),t}function Qa(t,e,n){var i=new Vi({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){"center"===n&&(t={width:t.width,height:t.height},i.setStyle(Ja(e,t)))}});return i}function Ja(t,e){var e=e.width/e.height,n=t.height*e,e=n<=t.width?t.height:(n=t.width)/e;return{x:t.x+t.width/2-n/2,y:t.y+t.height/2-e/2,width:n,height:e}}function ts(t,e){for(var n=[],i=t.length,o=0;o<i;o++){var r=t[o];r.path||r.createPathProxy(),r.__dirtyPath&&r.buildPath(r.path,r.shape,!0),n.push(r.path)}return(e=new a(e)).createPathProxy(),e.buildPath=function(t){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e)},e}function es(t,e){t.applyTransform&&(e=t.getBoundingRect().calculateTransform(e),t.applyTransform(e))}var u=Da;function ns(t){return null!=t&&"none"!==t}var is=P(),os=0;function rs(t){var e=t.__hoverStl;if(e&&!t.__highlighted){var n=t.__zr,i=t.useHoverLayer&&n&&"canvas"===n.painter.type;if(t.__highlighted=i?"layer":"plain",!(t.isGroup||!n&&t.useHoverLayer)){var o=t,r=t.style;if(Cs(r=i?(o=n.addHover(t)).style:r),!i){n=o;if(n.__hoverStlDirty){n.__hoverStlDirty=!1;var a=n.__hoverStl;if(a){var s,l=n.__cachedNormalStl={},h=(n.__cachedNormalZ2=n.z2,n.style);for(s in a)null!=a[s]&&(l[s]=h[s]);l.fill=h.fill,l.stroke=h.stroke}else n.__cachedNormalStl=n.__cachedNormalZ2=null}}r.extendFrom(e),as(r,e,"fill"),as(r,e,"stroke"),Ms(r),i||(t.dirty(!1),t.z2+=Wa)}}}function as(t,e,n){!ns(e[n])&&ns(t[n])&&(t[n]="string"!=typeof(e=t[n])?e:((t=is.get(e))||(t=qe(e,-.1),os<1e4&&(is.set(e,t),os++)),t))}function ss(t){var e,n=t.__highlighted;n&&(t.__highlighted=!1,t.isGroup||("layer"===n?t.__zr&&t.__zr.removeHover(t):(n=t.style,(e=t.__cachedNormalStl)&&(Cs(n),t.setStyle(e),Ms(n)),null!=(e=t.__cachedNormalZ2)&&t.z2-e===Wa&&(t.z2=e))))}function ls(t,e,n){var i,o=Ya,r=Ya;t.__highlighted&&(o=Xa,i=!0),e(t,n),t.__highlighted&&(r=Xa,i=!0),t.isGroup&&t.traverse(function(t){t.isGroup||e(t,n)}),i&&t.__highDownOnUpdate&&t.__highDownOnUpdate(o,r)}function hs(t,e){e=t.__hoverStl=!1!==e&&(t.hoverStyle||e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,ss(t),rs(t))}function us(t){ps(this,t)||this.__highByOuter||ls(this,rs)}function cs(t){ps(this,t)||this.__highByOuter||ls(this,ss)}function ds(t){this.__highByOuter|=1<<(t||0),ls(this,rs)}function fs(t){(this.__highByOuter&=~(1<<(t||0)))||ls(this,ss)}function ps(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function gs(t,e){ms(t,!0),ls(t,hs,e)}function ms(t,e){var n,e=!1===e;t.__highDownSilentOnTouch=t.highDownSilentOnTouch,t.__highDownOnUpdate=t.highDownOnUpdate,e&&!t.__highDownDispatcher||(t[n=e?"off":"on"]("mouseover",us)[n]("mouseout",cs),t[n]("emphasis",ds)[n]("normal",fs),t.__highByOuter=t.__highByOuter||0,t.__highDownDispatcher=!e)}function ys(t){return!(!t||!t.__highDownDispatcher)}function vs(t){var e=qa[t];return e=null==e&&Ua<=32?qa[t]=Ua++:e}function _s(t,e,n,i,o,r,a){var s,l=(o=o||Ga).labelFetcher,h=o.labelDataIndex,u=o.labelDimIndex,c=o.labelProp,d=n.getShallow("show"),f=i.getShallow("show"),d=((d||f)&&null==(s=l?l.getFormattedLabel(h,"normal",null,u,c):s)&&(s=it(o.defaultText)?o.defaultText(h,o):o.defaultText),d?s:null),f=f?ht(l?l.getFormattedLabel(h,"emphasis",null,u,c):null,s):null;null==d&&null==f||(xs(t,n,r,o),xs(e,i,a,o,!0)),t.text=d,e.text=f}function xs(t,e,n,i,o){return ws(t,e,i,o),n&&C(t,n),t}function ws(t,e,n,i){(n=n||Ga).isRectText&&(n.getTextPosition?a=n.getTextPosition(e,i):"outside"===(a=e.getShallow("position")||(i?null:"inside"))&&(a="top"),t.textPosition=a,t.textOffset=e.getShallow("offset"),null!=(a=e.getShallow("rotate"))&&(a*=Math.PI/180),t.textRotation=a,t.textDistance=ht(e.getShallow("distance"),i?null:5));var o,r,a=e.ecModel,s=a&&a.option.textStyle,l=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||Ga).rich;if(n)for(var i in e=e||{},n)n.hasOwnProperty(i)&&(e[i]=1);t=t.parentModel}return e}(e);if(l)for(var h in o={},l)l.hasOwnProperty(h)&&(r=e.getModel(["rich",h]),bs(o[h]={},r,s,n,i));t.rich=o,bs(t,e,s,n,i,!0),n.forceRich&&!n.textStyle&&(n.textStyle={})}function bs(t,e,n,i,o,r){n=!o&&n||Ga,t.textFill=Ss(e.getShallow("color"),i)||n.color,t.textStroke=Ss(e.getShallow("textBorderColor"),i)||n.textBorderColor,t.textStrokeWidth=ht(e.getShallow("textBorderWidth"),n.textBorderWidth),o||(r&&(t.insideRollbackOpt=i,Ms(t)),null==t.textFill&&(t.textFill=i.autoColor)),t.fontStyle=e.getShallow("fontStyle")||n.fontStyle,t.fontWeight=e.getShallow("fontWeight")||n.fontWeight,t.fontSize=e.getShallow("fontSize")||n.fontSize,t.fontFamily=e.getShallow("fontFamily")||n.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),r&&i.disableBox||(t.textBackgroundColor=Ss(e.getShallow("backgroundColor"),i),t.textPadding=e.getShallow("padding"),t.textBorderColor=Ss(e.getShallow("borderColor"),i),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||n.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||n.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||n.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||n.textShadowOffsetY}function Ss(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function Ms(t){var e,n,i,o=t.textPosition,r=t.insideRollbackOpt;r&&null==t.textFill&&(n=r.autoColor,i=r.isRectText,i=!(r=!1!==(r=r.useInsideStyle)&&(!0===r||i&&o&&"string"==typeof o&&0<=o.indexOf("inside")))&&null!=n,(r||i)&&(e={textFill:t.textFill,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth}),r&&(t.textFill="#fff",null==t.textStroke)&&(t.textStroke=n,null==t.textStrokeWidth)&&(t.textStrokeWidth=2),i)&&(t.textFill=n),t.insideRollback=e}function Cs(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function Is(t,e){e=e&&e.getModel("textStyle");return ft([t.fontStyle||e&&e.getShallow("fontStyle")||"",t.fontWeight||e&&e.getShallow("fontWeight")||"",(t.fontSize||e&&e.getShallow("fontSize")||12)+"px",t.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "))}function Ts(t,e,n,i,o,r){var a,s;"function"==typeof o&&(r=o,o=null),i&&i.isAnimationEnabled()&&(a=i.getShallow("animationDuration"+(t=t?"Update":"")),s=i.getShallow("animationEasing"+t),"function"==typeof(t=i.getShallow("animationDelay"+t))&&(t=t(o,i.getAnimationDelayParams?i.getAnimationDelayParams(e,o):null)),0<(a="function"==typeof a?a(o):a))?e.animateTo(n,a,t||0,s,r,!!r):(e.stopAnimation(),e.attr(n),r&&r())}function Ds(t,e,n,i,o){Ts(!0,t,e,n,i,o)}function ks(t,e,n,i,o){Ts(!1,t,e,n,i,o)}function As(t,e,n){return e&&!J(e)&&(e=Me.getLocalTransform(e)),zt([],t,e=n?xe([],e):e)}function Ps(t,e,i,n){var o,r;function a(t){var e={position:St(t.position),rotation:t.rotation};return t.shape&&(e.shape=C({},t.shape)),e}t&&e&&(r={},t.traverse(function(t){!t.isGroup&&t.anid&&(r[t.anid]=t)}),o=r,e.traverse(function(t){var e,n;!t.isGroup&&t.anid&&(e=o[t.anid])&&(n=a(t),t.attr(a(e)),Ds(t,n,i,t.dataIndex))}))}function Os(t,e,n){var i=(e=C({rectHover:!0},e)).style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),T(i,n),new Vi(e)):$a(t.replace("path://",""),e,n,"center")}function Ls(t,e,n,i,o,r,a,s){var l,n=n-t,i=i-e,a=a-o,s=s-r,h=a*i-n*s;return!((l=h)<=1e-6&&-1e-6<=l||(o=((l=t-o)*i-n*(t=e-r))/h)<0||1<o||(i=(l*s-a*t)/h)<0||1<i)}Ka("circle",ma),Ka("sector",_a),Ka("ring",xa),Ka("polygon",Sa),Ka("polyline",Ma),Ka("rect",O),Ka("line",Pa),Ka("bezierCurve",r),Ka("arc",Ra);var Es=Object.freeze({__proto__:null,Arc:Ra,BezierCurve:r,BoundingRect:X,CACHED_LABEL_STYLE_PROPERTIES:{color:"textFill",textBorderColor:"textStroke",textBorderWidth:"textStrokeWidth"},Circle:ma,CompoundPath:s,Group:h,Image:Vi,IncrementalDisplayable:Na,Line:Pa,LinearGradient:za,Polygon:Sa,Polyline:Ma,RadialGradient:Ba,Rect:O,Ring:xa,Sector:_a,Text:ga,Z2_EMPHASIS_LIFT:Wa,applyTransform:As,clipPointsByRect:function(t,n){return S(t,function(t){var e=t[0],e=Ha(e,n.x),t=(e=Va(e,n.x+n.width),t[1]),t=Ha(t,n.y);return[e,Va(t,n.y+n.height)]})},clipRectByRect:function(t,e){var n=Ha(t.x,e.x),i=Va(t.x+t.width,e.x+e.width),o=Ha(t.y,e.y),t=Va(t.y+t.height,e.y+e.height);if(n<=i&&o<=t)return{x:n,y:o,width:i-n,height:t-o}},createIcon:Os,extendPath:function(t,e){return a.extend(pa(t,e))},extendShape:Za,getFont:Is,getHighlightDigit:vs,getShapeClass:function(t){if(ja.hasOwnProperty(t))return ja[t]},getTransform:function(t,e){for(var n=pe([]);t&&t!==e;)me(n,t.getLocalTransform(),n),t=t.parent;return n},groupTransition:Ps,initProps:ks,isHighDownDispatcher:ys,lineLineIntersect:Ls,linePolygonIntersect:function(t,e,n,i,o){for(var r=0,a=o[o.length-1];r<o.length;r++){var s=o[r];if(Ls(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}},makeImage:Qa,makePath:$a,mergePath:ts,modifyLabelStyle:function(t,e,n){var i=t.style;e&&(Cs(i),t.setStyle(e),Ms(i)),i=t.__hoverStl,n&&i&&(Cs(i),C(i,n),Ms(i))},registerShape:Ka,resizePath:es,setAsHighDownDispatcher:ms,setElementHoverStyle:hs,setHoverStyle:gs,setLabelStyle:_s,setText:function(t,e,n){var i,o={isRectText:!0};!1===n?i=!0:o.autoColor=n,ws(t,e,o,i)},setTextStyle:xs,subPixelOptimize:u,subPixelOptimizeLine:function(t){return Ia(t.shape,t.shape,t.style),t},subPixelOptimizeRect:function(t){return Ta(t.shape,t.shape,t.style),t},transformDirection:function(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),o=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),i=As(["left"===t?-i:"right"===t?i:0,"top"===t?-o:"bottom"===t?o:0],e,n);return Math.abs(i[0])>Math.abs(i[1])?0<i[0]?"right":"left":0<i[1]?"bottom":"top"},updateProps:Ds}),zs=["textStyle","color"],r={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(zs):null)},getFont:function(){return Is({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return ai(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}},Bs=Uo([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),s={getItemStyle:function(t,e){t=Bs(this,t,e),e=this.getBorderLineDash();return e&&(t.lineDash=e),t},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},Sa=i,Rs=Po();function _(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}function Ns(t,e,n){for(var i=0;i<e.length&&(!e[i]||null!=(t=t&&"object"==typeof t?t[e[i]]:null));i++);return t=null==t&&n?n.get(e):t}function Fs(t,e){var n=Rs(t).getParent;return n?n.call(t,e):t.parentModel}_.prototype={constructor:_,init:null,mergeOption:function(t){g(this.option,t,!0)},get:function(t,e){return null==t?this.option:Ns(this.option,this.parsePath(t),!e&&Fs(this,t))},getShallow:function(t,e){var n=this.option,n=null==n?n:n[t],e=!e&&Fs(this,t);return n=null==n&&e?e.getShallow(t):n},getModel:function(t,e){var n;return new _(null==t?this.option:Ns(this.option,t=this.parsePath(t)),e=e||(n=Fs(this,t))&&n.getModel(t),this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){return new this.constructor(M(this.option))},setReadOnly:function(t){},parsePath:function(t){return t="string"==typeof t?t.split("."):t},customizeGetParent:function(t){Rs(this).getParent=t},isAnimationEnabled:function(){if(!y.node)return null!=this.option.animation?!!this.option.animation:this.parentModel?this.parentModel.isAnimationEnabled():void 0}},Ho(_),Go(_),Sa(_,n),Sa(_,o),Sa(_,r),Sa(_,s);var Hs=0;function Vs(t){return[t||"",Hs++,Math.random().toFixed(5)].join("_")}function Gs(t,e,n,i){var o=e[1]-e[0],r=n[1]-n[0];if(0==o)return 0==r?n[0]:(n[0]+n[1])/2;if(i)if(0<o){if(t<=e[0])return n[0];if(t>=e[1])return n[1]}else{if(t>=e[0])return n[0];if(t<=e[1])return n[1]}else{if(t===e[0])return n[0];if(t===e[1])return n[1]}return(t-e[0])/o*r+n[0]}function v(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function Ws(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function Xs(t){var t=t.toString(),e=t.indexOf("e");return 0<e?(e=+t.slice(e+1))<0?-e:0:(e=t.indexOf("."))<0?0:t.length-1-e}function Ys(t,e){var n=Math.log,i=Math.LN10,t=Math.floor(n(t[1]-t[0])/i),n=Math.round(n(Math.abs(e[1]-e[0]))/i),e=Math.min(Math.max(-t+n,0),20);return isFinite(e)?e:20}function Us(t){var e=2*Math.PI;return(t%e+e)%e}function qs(t){return-1e-4<t&&t<1e-4}var js=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Zs(t){var e,n;return t instanceof Date?t:"string"==typeof t?(e=js.exec(t))?e[8]?(n=+e[4]||0,"Z"!==e[8].toUpperCase()&&(n-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,+e[7]||0))):new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0):new Date(NaN):null==t?new Date(NaN):new Date(Math.round(t))}function Ks(t){return Math.pow(10,$s(t))}function $s(t){var e;return 0===t?0:(e=Math.floor(Math.log(t)/Math.LN10),10<=t/Math.pow(10,e)&&e++,e)}function Qs(t,e){var n=$s(t),i=Math.pow(10,n),o=t/i,e=e?o<1.5?1:o<2.5?2:o<4?3:o<7?5:10:o<1?1:o<2?2:o<3?3:o<5?5:10;return t=e*i,-20<=n?+t.toFixed(n<0?-n:0):t}Ma=Object.freeze({__proto__:null,MAX_SAFE_INTEGER:9007199254740991,asc:function(t){return t.sort(function(t,e){return t-e}),t},getPercentWithPrecision:function(t,e,n){if(!t[e])return 0;var i=tt(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===i)return 0;for(var o=Math.pow(10,n),n=S(t,function(t){return(isNaN(t)?0:t)/i*o*100}),r=100*o,a=S(n,function(t){return Math.floor(t)}),s=tt(a,function(t,e){return t+e},0),l=S(n,function(t,e){return t-a[e]});s<r;){for(var h=Number.NEGATIVE_INFINITY,u=null,c=0,d=l.length;c<d;++c)l[c]>h&&(h=l[c],u=c);++a[u],l[u]=0,++s}return a[e]/o},getPixelPrecision:Ys,getPrecision:function(t){if(t=+t,isNaN(t))return 0;for(var e=1,n=0;Math.round(t*e)/e!==t;)e*=10,n++;return n},getPrecisionSafe:Xs,isNumeric:function(t){return 0<=t-parseFloat(t)},isRadianAroundZero:qs,linearMap:Gs,nice:Qs,parseDate:Zs,parsePercent:v,quantile:function(t,e){var e=(t.length-1)*e+1,n=Math.floor(e),i=+t[n-1];return(e=e-n)?i+e*(t[n]-i):i},quantity:Ks,quantityExponent:$s,reformIntervals:function(t){t.sort(function(t,e){return function t(e,n,i){return e.interval[i]<n.interval[i]||e.interval[i]===n.interval[i]&&(e.close[i]-n.close[i]==(i?-1:1)||!i&&t(e,n,1))}(t,e,0)?-1:1});for(var e=-1/0,n=1,i=0;i<t.length;){for(var o=t[i].interval,r=t[i].close,a=0;a<2;a++)o[a]<=e&&(o[a]=e,r[a]=a?1:1-n),e=o[a],n=r[a];o[0]===o[1]&&r[0]*r[1]!=1?t.splice(i,1):i++}return t},remRadian:Us,round:Ws});function Js(t){return isNaN(t)?"-":(t=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<t.length?"."+t[1]:"")}function tl(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),t=e?t&&t.charAt(0).toUpperCase()+t.slice(1):t}var el=dt,nl=/([&<>"'])/g,il={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function ol(t){return null==t?"":(t+"").replace(nl,function(t,e){return il[e]})}function rl(t,e){return"{"+t+(null==e?"":e)+"}"}var al=["a","b","c","d","e","f","g"];function sl(t,e,n){var i=(e=b(e)?e:[e]).length;if(!i)return"";for(var o=e[0].$vars||[],r=0;r<o.length;r++){var a=al[r];t=t.replace(rl(a),rl(a,0))}for(var s=0;s<i;s++)for(var l=0;l<o.length;l++){var h=e[s][o[l]];t=t.replace(rl(al[l],s),n?ol(h):h)}return t}function ll(t,e){var n=(t=k(t)?{color:t,extraCssText:e}:t||{}).color,i=t.type,e=t.extraCssText,o=t.renderMode||"html",t=t.markerId||"X";return n?"html"===o?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+ol(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+ol(n)+";"+(e||"")+'"></span>':{renderMode:o,content:"{marker"+t+"|}  ",style:{color:n}}:""}function hl(t,e){return"0000".substr(0,e-(t+="").length)+t}function ul(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var e=Zs(e),n=n?"UTC":"",i=e["get"+n+"FullYear"](),o=e["get"+n+"Month"]()+1,r=e["get"+n+"Date"](),a=e["get"+n+"Hours"](),s=e["get"+n+"Minutes"](),l=e["get"+n+"Seconds"](),e=e["get"+n+"Milliseconds"]();return t=t.replace("MM",hl(o,2)).replace("M",o).replace("yyyy",i).replace("yy",i%100).replace("dd",hl(r,2)).replace("d",r).replace("hh",hl(a,2)).replace("h",a).replace("mm",hl(s,2)).replace("m",s).replace("ss",hl(l,2)).replace("s",l).replace("SSS",hl(e,3))}var cl=ui;function dl(t,e){var n;"_blank"===e||"blank"===e?((n=window.open()).opener=null,n.location=t):window.open(t,e)}var xa=Object.freeze({__proto__:null,addCommas:Js,capitalFirst:function(t){return t&&t.charAt(0).toUpperCase()+t.substr(1)},encodeHTML:ol,formatTime:ul,formatTpl:sl,formatTplSimple:function(n,t,i){return D(t,function(t,e){n=n.replace("{"+e+"}",i?ol(t):t)}),n},getTextBoundingRect:function(t){return ai(t.text,t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich,t.truncate)},getTextRect:function(t,e,n,i,o,r,a,s){return ai(t,e,n,i,o,s,r,a)},getTooltipMarker:ll,normalizeCssArray:el,toCamelCase:tl,truncateText:cl,windowOpen:dl}),fl=D,pl=["left","right","top","bottom","width","height"],gl=[["width","left","right"],["height","top","bottom"]];function ml(s,l,h,u,c){var d=0,f=0,p=(null==u&&(u=1/0),null==c&&(c=1/0),0);l.eachChild(function(t,e){var n,i,o,r=t.position,a=t.getBoundingRect(),e=l.childAt(e+1),e=e&&e.getBoundingRect();p="horizontal"===s?(i=a.width+(e?-e.x+a.x:0),u<(n=d+i)||t.newline?(d=0,n=i,f+=p+h,a.height):Math.max(p,a.height)):(i=a.height+(e?-e.y+a.y:0),c<(o=f+i)||t.newline?(d+=p+h,f=0,o=i,a.width):Math.max(p,a.width)),t.newline||(r[0]=d,r[1]=f,"horizontal"===s?d=n+h:f=o+h)})}var yl=ml;function vl(t,e,n){n=el(n||0);var i=e.width,o=e.height,r=v(t.left,i),a=v(t.top,o),e=v(t.right,i),s=v(t.bottom,o),l=v(t.width,i),h=v(t.height,o),u=n[2]+n[0],c=n[1]+n[3],d=t.aspect;switch(isNaN(l)&&(l=i-e-c-r),isNaN(h)&&(h=o-s-u-a),null!=d&&(isNaN(l)&&isNaN(h)&&(i/o<d?l=.8*i:h=.8*o),isNaN(l)&&(l=d*h),isNaN(h))&&(h=l/d),isNaN(r)&&(r=i-e-l-c),isNaN(a)&&(a=o-s-h-u),t.left||t.right){case"center":r=i/2-l/2-n[3];break;case"right":r=i-l-c}switch(t.top||t.bottom){case"middle":case"center":a=o/2-h/2-n[0];break;case"bottom":a=o-h-u}r=r||0,a=a||0,isNaN(l)&&(l=i-c-r-(e||0)),isNaN(h)&&(h=o-u-a-(s||0));d=new X(r+n[3],a+n[0],l,h);return d.margin=n,d}function _l(l,h,t){var u=(t=A(t)?t:{}).ignoreSize,t=(b(u)||(u=[u,u]),n(gl[0],0)),e=n(gl[1],1);function n(t,e){var n={},i=0,o={},r=0;if(fl(t,function(t){o[t]=l[t]}),fl(t,function(t){c(h,t)&&(n[t]=o[t]=h[t]),d(n,t)&&i++,d(o,t)&&r++}),u[e])d(h,t[1])?o[t[2]]=null:d(h,t[2])&&(o[t[1]]=null);else if(2!==r&&i){if(!(2<=i))for(var a=0;a<t.length;a++){var s=t[a];if(!c(n,s)&&c(l,s)){n[s]=l[s];break}}return n}return o}function c(t,e){return t.hasOwnProperty(e)}function d(t,e){return null!=t[e]&&"auto"!==t[e]}function i(t,e,n){fl(t,function(t){e[t]=n[t]})}i(gl[0],l,t),i(gl[1],l,e)}function xl(t){return e={},(n=t)&&e&&fl(pl,function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e;var e,n}l(ml,"vertical"),l(ml,"horizontal");var wl,bl,Sl,Ml=Po(),c=_.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,i){_.call(this,t,e,n,i),this.uid=Vs("ec_cpt_model")},init:function(t,e,n,i){this.mergeDefaultAndTheme(t,n)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?xl(t):{};g(t,e.getTheme().get(this.mainType)),g(t,this.getDefaultOption()),n&&_l(t,i,n)},mergeOption:function(t,e){g(this.option,t,!0);var n=this.layoutMode;n&&_l(this.option,t,n)},optionUpdated:function(t,e){},getDefaultOption:function(){var t=Ml(this);if(!t.defaultOption){for(var e=[],n=this.constructor;n;){var i=n.prototype.defaultOption;i&&e.push(i),n=n.superClass}for(var o={},r=e.length-1;0<=r;r--)o=g(o,e[r],!0);t.defaultOption=o}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});function Cl(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}Yo(c,{registerWhenExtend:!0}),bl={},(wl=c).registerSubTypeDefaulter=function(t,e){t=Fo(t),bl[t.main]=e},wl.determineSubType=function(t,e){var n,i=e.type;return i||(n=Fo(t).main,wl.hasSubTypes(t)&&bl[n]&&(i=bl[n](e))),i},Sl=function(t){var e=[];D(c.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=S(e,function(t){return Fo(t).main}),"dataset"!==t&&m(e,"dataset")<=0&&e.unshift("dataset");return e},c.topologicalTravel=function(t,e,n,i){if(t.length){a={},s=[],D(r=e,function(n){var e,i,o=Cl(a,n),t=o.originalDeps=Sl(n),t=(e=r,i=[],D(t,function(t){0<=m(e,t)&&i.push(t)}),i);o.entryCount=t.length,0===o.entryCount&&s.push(n),D(t,function(t){m(o.predecessor,t)<0&&o.predecessor.push(t);var e=Cl(a,t);m(e.successor,t)<0&&e.successor.push(n)})});var r,a,s,e={graph:a,noEntryList:s},o=e.graph,l=e.noEntryList,h={};for(D(t,function(t){h[t]=!0});l.length;){var u=l.pop(),c=o[u],d=!!h[u];d&&(n.call(i,u,c.originalDeps.slice()),delete h[u]),D(c.successor,d?p:f)}D(h,function(){throw new Error("Circle dependency may exists")})}function f(t){o[t].entryCount--,0===o[t].entryCount&&l.push(t)}function p(t){h[t]=!0,f(t)}},i(c,{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}});var u="",Il={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:(u="undefined"!=typeof navigator?navigator.platform||"":u).match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Tl=Po();var Dl={clearColorPalette:function(){Tl(this).colorIdx=0,Tl(this).colorNameMap={}},getColorFromPalette:function(t,e,n){var i,o,e=Tl(e=e||this),r=e.colorIdx||0,a=e.colorNameMap=e.colorNameMap||{};return a.hasOwnProperty(t)?a[t]:(i=So(this.get("color",!0)),o=this.get("colorLayer",!0),(o=(o=null!=n&&o?function(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}(o,n):i)||i)&&o.length?(n=o[r],t&&(a[t]=n),e.colorIdx=(r+1)%o.length,n):void 0)}},kl="original",Al="arrayRows",Pl="objectRows",Ol="keyedColumns",Ll="unknown",El="typedArray",zl="column",Bl="row";function Rl(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===Ol?{}:[]),this.sourceFormat=t.sourceFormat||Ll,this.seriesLayoutBy=t.seriesLayoutBy||zl,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&P(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}Rl.seriesDataToSource=function(t){return new Rl({data:t,sourceFormat:rt(t)?El:kl,fromDataset:!1})},Go(Rl);var Nl={Must:1,Might:2,Not:3},Fl=Po();function Hl(t){var e,n=t.option,i=n.data,o=rt(i)?El:kl,r=!1,a=n.seriesLayoutBy,s=n.sourceHeader,l=n.dimensions,h=Xl(t),h=(h&&(i=(e=h.option).source,o=Fl(h).sourceFormat,r=!0,a=a||e.seriesLayoutBy,null==s&&(s=e.sourceHeader),l=l||e.dimensions),function(t,e,n,i,o){if(!t)return{dimensionsDefine:Vl(o)};var r,a;e===Al?("auto"===i||null==i?Gl(function(t){null!=t&&"-"!==t&&(k(t)?null==a&&(a=1):a=0)},n,t,10):a=i?1:0,o||1!==a||(o=[],Gl(function(t,e){o[e]=null!=t?t:""},n,t)),r=o?o.length:n===Bl?t.length:t[0]?t[0].length:null):e===Pl?o=o||function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););{var i;if(e)return i=[],D(e,function(t,e){i.push(e)}),i}}(t):e===Ol?o||(o=[],D(t,function(t,e){o.push(e)})):e===kl?(i=Io(t[0]),r=b(i)&&i.length||1):e===El&&I&&p(!!o,"dimensions must be given if data is TypedArray.");return{startIndex:a,dimensionsDefine:Vl(o),dimensionsDetectCount:r}}(i,o,a,s,l));Fl(t).source=new Rl({data:i,fromDataset:r,seriesLayoutBy:a,sourceFormat:o,dimensionsDefine:h.dimensionsDefine,startIndex:h.startIndex,dimensionsDetectCount:h.dimensionsDetectCount,encodeDefine:n.encode})}function Vl(t){var i;if(t)return i=P(),S(t,function(t,e){var n;return null!=(t=C({},A(t)?t:{name:t})).name&&(t.name+="",null==t.displayName&&(t.displayName=t.name),(n=i.get(t.name))?t.name+="-"+n.count++:i.set(t.name,{count:1})),t})}function Gl(t,e,n,i){if(null==i&&(i=1/0),e===Bl)for(var o=0;o<n.length&&o<i;o++)t(n[o]?n[o][0]:null,o);else for(var r=n[0]||[],o=0;o<r.length&&o<i;o++)t(r[o],o)}function Wl(n,t,e){var o,r,a,i,s,l={},h=Xl(t);return h&&n&&(o=[],r=[],t=t.ecModel,t=Fl(t).datasetMap,h=h.uid+"_"+e.seriesLayoutBy,D(n=n.slice(),function(t,e){A(t)||(n[e]={name:t}),"ordinal"===t.type&&null==a&&(i=c(n[a=e])),l[t.name]=[]}),s=t.get(h)||t.set(h,{categoryWayDim:i,valueWayDim:0}),D(n,function(t,e){var n,i=t.name,t=c(t);null==a?(n=s.valueWayDim,u(l[i],n,t),u(r,n,t),s.valueWayDim+=t):a===e?(u(l[i],0,t),u(o,0,t)):(n=s.categoryWayDim,u(l[i],n,t),u(r,n,t),s.categoryWayDim+=t)}),o.length&&(l.itemName=o),r.length)&&(l.seriesName=r),l;function u(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function c(t){t=t.dimsDef;return t?t.length:1}}function Xl(t){var e=t.option;if(!e.data)return t.ecModel.getComponent("dataset",e.datasetIndex||0)}function Yl(t,e){var n,i,o,r=t.data,a=t.sourceFormat,s=t.seriesLayoutBy,l=t.dimensionsDefine,h=t.startIndex,u=e;if(!rt(r)){if(l&&(A(l=l[u])?(i=l.name,o=l.type):k(l)&&(i=l)),null!=o)return"ordinal"===o?Nl.Must:Nl.Not;if(a===Al)if(s===Bl){for(var c=r[u],d=0;d<(c||[]).length&&d<5;d++)if(null!=(n=m(c[h+d])))return n}else for(d=0;d<r.length&&d<5;d++){var f=r[h+d];if(f&&null!=(n=m(f[u])))return n}else if(a===Pl){if(!i)return Nl.Not;for(d=0;d<r.length&&d<5;d++)if((p=r[d])&&null!=(n=m(p[i])))return n}else if(a===Ol){if(!i)return Nl.Not;if(!(c=r[i])||rt(c))return Nl.Not;for(d=0;d<c.length&&d<5;d++)if(null!=(n=m(c[d])))return n}else if(a===kl)for(d=0;d<r.length&&d<5;d++){var p,g=Io(p=r[d]);if(!b(g))return Nl.Not;if(null!=(n=m(g[u])))return n}}return Nl.Not;function m(t){var e=k(t);return null!=t&&isFinite(t)&&""!==t?e?Nl.Might:Nl.Not:e&&"-"!==t?Nl.Must:void 0}}var Ul="\0_ec_inner",ql=_.extend({init:function(t,e,n,i){n=n||{},this.option=null,this._theme=new _(n),this._optionManager=i},setOption:function(t,e){p(!(Ul in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e,n=!1,i=this._optionManager;return t&&"recreate"!==t||(e=i.mountOption("recreate"===t),this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(e)):function(t){this.option={},this.option[Ul]=1,this._componentsMap=P({series:[]}),this._seriesIndices,this._seriesIndicesMap,function(n,t){var i=n.color&&!n.colorLayer;D(t,function(t,e){"colorLayer"===e&&i||c.hasClass(e)||("object"==typeof t?n[e]=n[e]?g(n[e],t,!1):M(t):null==n[e]&&(n[e]=t))})}(t,this._theme.option),g(t,Il,!1),this.mergeOption(t)}.call(this,e),n=!0),"timeline"!==t&&"media"!==t||this.restoreData(),t&&"recreate"!==t&&"timeline"!==t||(e=i.getTimelineOption(this))&&(this.mergeOption(e),n=!0),t&&"recreate"!==t&&"media"!==t||(e=i.getMediaOption(this,this._api)).length&&D(e,function(t){this.mergeOption(t,n=!0)},this),n},mergeOption:function(n){var s=this.option,l=this._componentsMap,i=[];Fl(this).datasetMap=P(),D(n,function(t,e){null!=t&&(c.hasClass(e)?e&&i.push(e):s[e]=null==s[e]?M(t):g(s[e],t,!0))}),c.topologicalTravel(i,c.getAllClassMainTypes(),function(r,t){var e=So(n[r]),e=To(l.get(r),e),a=(function(t){var a=P();_o(t,function(t,e){var n=t.exist;n&&a.set(n.id,t)}),_o(t,function(t,e){var n=t.option;p(!n||null==n.id||!a.get(n.id)||a.get(n.id)===t,"id duplicates: "+(n&&n.id)),n&&null!=n.id&&a.set(n.id,t),t.keyInfo||(t.keyInfo={})}),_o(t,function(t,e){var n=t.exist,i=t.option,o=t.keyInfo;if(xo(i)){if(o.name=null!=i.name?i.name+"":n?n.name:bo+e,n)o.id=n.id;else if(null!=i.id)o.id=i.id+"";else for(var r=0;o.id="\0"+o.name+"\0"+r++,a.get(o.id););a.set(o.id,t)}})}(e),D(e,function(t,e){var n,i=t.option;A(i)&&(t.keyInfo.mainType=r,t.keyInfo.subType=(n=r,i=i,t=t.exist,i.type||(t?t.subType:c.determineSubType(n,i))))}),function(e,t){b(t)||(t=t?[t]:[]);var n={};return D(t,function(t){n[t]=(e.get(t)||[]).slice()}),n}(l,t));s[r]=[],l.set(r,[]),D(e,function(t,e){var n,i=t.exist,o=t.option;p(A(o)||i,"Empty component definition"),o?(n=c.getClass(r,t.keyInfo.subType,!0),i&&i.constructor===n?(i.name=t.keyInfo.name,i.mergeOption(o,this),i.optionUpdated(o,!1)):(t=C({dependentModels:a,componentIndex:e},t.keyInfo),C(i=new n(o,this,this,t),t),i.init(o,this,this,t),i.optionUpdated(null,!0))):(i.mergeOption({},this),i.optionUpdated({},!1)),l.get(r)[e]=i,s[r][e]=i.option},this),"series"===r&&jl(this,l.get("series"))},this),this._seriesIndicesMap=P(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var i=M(this.option);return D(i,function(t,e){if(c.hasClass(e)){for(var n=(t=So(t)).length-1;0<=n;n--)ko(t[n])&&t.splice(n,1);i[e]=t}}),delete i[Ul],i},getTheme:function(){return this._theme},getComponent:function(t,e){t=this._componentsMap.get(t);if(t)return t[e||0]},queryComponents:function(t){var e,n,i,o,r,a,s=t.mainType;return s&&(e=t.index,n=t.id,i=t.name,o=this._componentsMap.get(s))&&o.length?Zl(null!=e?et(S(e=b(e)?e:[e],function(t){return o[t]}),function(t){return!!t}):null!=n?(r=b(n),et(o,function(t){return r&&0<=m(n,t.id)||!r&&t.id===n})):null!=i?(a=b(i),et(o,function(t){return a&&0<=m(i,t.name)||!a&&t.name===i})):o.slice(),t):[]},findComponents:function(t){var e,n=t.query,i=t.mainType,o=(o=i+"Index",r=i+"Id",e=i+"Name",!(n=n)||null==n[o]&&null==n[r]&&null==n[e]?null:{mainType:i,index:n[o],id:n[r],name:n[e]}),r=o?this.queryComponents(o):this._componentsMap.get(i);return n=Zl(r,t),t.filter?et(n,t.filter):n},eachComponent:function(t,i,o){var e=this._componentsMap;"function"==typeof t?(o=i,i=t,e.each(function(t,n){D(t,function(t,e){i.call(o,n,t,e)})})):k(t)?D(e.get(t),i,o):A(t)&&D(this.findComponents(t),i,o)},getSeriesByName:function(e){return et(this._componentsMap.get("series"),function(t){return t.name===e})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(e){return et(this._componentsMap.get("series"),function(t){return t.subType===e})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(n,i){Kl(this),D(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)},this)},eachRawSeries:function(t,e){D(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(n,i,o){Kl(this),D(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];e.subType===n&&i.call(o,e,t)},this)},eachRawSeriesByType:function(t,e,n){return D(this.getSeriesByType(t),e,n)},isSeriesFiltered:function(t){return Kl(this),null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){Kl(this);t=et(this._componentsMap.get("series"),t,e);jl(this,t)},restoreData:function(n){var i=this._componentsMap,o=(jl(this,i.get("series")),[]);i.each(function(t,e){o.push(e)}),c.topologicalTravel(o,c.getAllClassMainTypes(),function(e,t){D(i.get(e),function(t){"series"===e&&function(t,e){{var n,i;if(e)return n=e.seiresIndex,i=e.seriesId,e=e.seriesName,null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=e&&t.name!==e}}(t,n)||t.restoreData()})})}});function jl(t,e){t._seriesIndicesMap=P(t._seriesIndices=S(e,function(t){return t.componentIndex})||[])}function Zl(t,e){return e.hasOwnProperty("subType")?et(t,function(t){return t.subType===e.subType}):t}function Kl(t){if(I&&!t._seriesIndices)throw new Error("Option should contains series.")}i(ql,Dl);var $l=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"];function Ql(e){D($l,function(t){this[t]=nt(e[t],e)},this)}var Jl={};function th(){this._coordinateSystems=[]}th.prototype={constructor:th,create:function(n,i){var o=[];D(Jl,function(t,e){t=t.create(n,i);o=o.concat(t||[])}),this._coordinateSystems=o},update:function(e,n){D(this._coordinateSystems,function(t){t.update&&t.update(e,n)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},th.register=function(t,e){Jl[t]=e},th.get=function(t){return Jl[t]};var eh=D,nh=M,ih=S,oh=g,rh=/^(min|max)?(.+)$/;function ah(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}var sh=D,lh=A,hh=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function uh(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=hh.length;n<i;n++){var o=hh[n],r=e.normal,a=e.emphasis;r&&r[o]&&(t[o]=t[o]||{},t[o].normal?g(t[o].normal,r[o]):t[o].normal=r[o],r[o]=null),a&&a[o]&&(t[o]=t[o]||{},t[o].emphasis?g(t[o].emphasis,a[o]):t[o].emphasis=a[o],a[o]=null)}}function ch(t,e,n){var i,o;t&&t[e]&&(t[e].normal||t[e].emphasis)&&(i=t[e].normal,o=t[e].emphasis,i&&(n?(t[e].normal=t[e].emphasis=null,T(t[e],i)):t[e]=i),o)&&(t.emphasis=t.emphasis||{},t.emphasis[e]=o)}function dh(t){ch(t,"itemStyle"),ch(t,"lineStyle"),ch(t,"areaStyle"),ch(t,"label"),ch(t,"labelLine"),ch(t,"upperLabel"),ch(t,"edgeLabel")}function d(t,e){var n=lh(t)&&t[e],i=lh(n)&&n.textStyle;if(i)for(var o=0,r=Co.length;o<r;o++){e=Co[o];i.hasOwnProperty(e)&&(n[e]=i[e])}}function fh(t){t&&(dh(t),d(t,"label"),t.emphasis)&&d(t.emphasis,"label")}function ph(t){return b(t)?t:t?[t]:[]}function gh(t){return(b(t)?t[0]:t)||{}}function mh(e,t){sh(ph(e.series),function(t){if(lh(t))if(lh(t)){uh(t),dh(t),d(t,"label"),d(t,"upperLabel"),d(t,"edgeLabel"),t.emphasis&&(d(t.emphasis,"label"),d(t.emphasis,"upperLabel"),d(t.emphasis,"edgeLabel"));var e=t.markPoint,n=(e&&(uh(e),fh(e)),t.markLine),i=(n&&(uh(n),fh(n)),t.markArea),o=(i&&fh(i),t.data);if("graph"===t.type){var o=o||t.nodes,r=t.links||t.edges;if(r&&!rt(r))for(var a=0;a<r.length;a++)fh(r[a]);D(t.categories,function(t){dh(t)})}if(o&&!rt(o))for(a=0;a<o.length;a++)fh(o[a]);if((e=t.markPoint)&&e.data)for(var s=e.data,a=0;a<s.length;a++)fh(s[a]);if((n=t.markLine)&&n.data)for(var l=n.data,a=0;a<l.length;a++)b(l[a])?(fh(l[a][0]),fh(l[a][1])):fh(l[a]);"gauge"===t.type?(d(t,"axisLabel"),d(t,"title"),d(t,"detail")):"treemap"===t.type?(ch(t.breadcrumb,"itemStyle"),D(t.levels,function(t){dh(t)})):"tree"===t.type&&dh(t.leaves)}});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),sh(n,function(t){sh(ph(e[t]),function(t){t&&(d(t,"axisLabel"),d(t.axisPointer,"label"))})}),sh(ph(e.parallel),function(t){t=t&&t.parallelAxisDefault;d(t,"axisLabel"),d(t&&t.axisPointer,"label")}),sh(ph(e.calendar),function(t){ch(t,"itemStyle"),d(t,"dayLabel"),d(t,"monthLabel"),d(t,"yearLabel")}),sh(ph(e.radar),function(t){d(t,"name")}),sh(ph(e.geo),function(t){lh(t)&&(fh(t),sh(ph(t.regions),function(t){fh(t)}))}),sh(ph(e.timeline),function(t){fh(t),ch(t,"label"),ch(t,"itemStyle"),ch(t,"controlStyle",!0);t=t.data;b(t)&&D(t,function(t){A(t)&&(ch(t,"label"),ch(t,"itemStyle"))})}),sh(ph(e.toolbox),function(t){ch(t,"iconStyle"),sh(t.feature,function(t){ch(t,"iconStyle")})}),d(gh(e.axisPointer),"label"),d(gh(e.tooltip).axisPointer,"label")}function yh(e){D(vh,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var vh=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],_h=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"];function xh(e,t){mh(e,t),e.series=So(e.series),D(e.series,function(t){if(A(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e)null!=t.clockWise&&(t.clockwise=t.clockWise);else if("gauge"===e){e=function(t,e){e=e.split(",");for(var n=t,i=0;i<e.length&&null!=(n=n&&n[e[i]]);i++);return n}(t,"pointer.color");if(null!=e){var n=t;var i="itemStyle.color";var o=void 0;i=i.split(",");for(var r,a=n,s=0;s<i.length-1;s++)null==a[r=i[s]]&&(a[r]={}),a=a[r];!o&&null!=a[i[s]]||(a[i[s]]=e)}}yh(t)}}),e.dataRange&&(e.visualMap=e.dataRange),D(_h,function(t){t=e[t];t&&D(t=b(t)?t:[t],function(t){yh(t)})})}function wh(g){D(g,function(h,u){var c=[],d=[NaN,NaN],t=[h.stackResultDimension,h.stackedOverDimension],f=h.data,p=h.isStackedByIndex,t=f.map(t,function(t,e,n){var i,o,r=f.get(h.stackedDimension,n);if(isNaN(r))return d;p?o=f.getRawIndex(n):i=f.get(h.stackedByDimension,n);for(var a=NaN,s=u-1;0<=s;s--){var l=g[s];if(0<=(o=p?o:l.data.rawIndexOf(l.stackedByDimension,i))){l=l.data.getByRawIndex(l.stackResultDimension,o);if(0<=r&&0<l||r<=0&&l<0){r+=l,a=l;break}}}return c[0]=r,c[1]=a,c});f.hostModel.setData(t),h.data=t})}function bh(t,e){Rl.isInstance(t)||(t=Rl.seriesDataToSource(t)),this._source=t;var n=this._data=t.data,i=t.sourceFormat;if(i===El){if(I&&null==e)throw new Error("Typed array data must specify dimension size");this._offset=0,this._dimSize=e,this._data=n}e=Sh[i===Al?i+"_"+t.seriesLayoutBy:i];I&&p(e,"Invalide sourceFormat: "+i),C(this,e)}var n=bh.prototype,Sh=(n.pure=!(ah.prototype={constructor:ah,setOption:function(t,e){t&&D(So(t.series),function(t){t&&t.data&&rt(t.data)&&gt(t.data)}),t=nh(t);var o,n=this._optionBackup,t=function(t,n,i){var e,o,r=[],a=[],s=t.timeline;t.baseOption&&(o=t.baseOption);(s||t.options)&&(o=o||{},r=(t.options||[]).slice());{var l;t.media&&(o=o||{},l=t.media,eh(l,function(t){t&&t.option&&(t.query?a.push(t):e=e||t)}))}o=o||t;o.timeline||(o.timeline=s);return eh([o].concat(r).concat(S(a,function(t){return t.option})),function(e){eh(n,function(t){t(e,i)})}),{baseOption:o,timelineOptions:r,mediaDefault:e,mediaList:a}}.call(this,t,e,!n);this._newBaseOption=t.baseOption,n?(o=n.baseOption,e=t.baseOption,eh(e=e||{},function(t,e){var n,i;null!=t&&(n=o[e],c.hasClass(e)?(t=So(t),i=To(n=So(n),t),o[e]=ih(i,function(t){return t.option&&t.exist?oh(t.exist,t.option,!0):t.exist||t.option})):o[e]=oh(n,t,!0))}),t.timelineOptions.length&&(n.timelineOptions=t.timelineOptions),t.mediaList.length&&(n.mediaList=t.mediaList),t.mediaDefault&&(n.mediaDefault=t.mediaDefault)):this._optionBackup=t},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=ih(e.timelineOptions,nh),this._mediaList=ih(e.mediaList,nh),this._mediaDefault=nh(e.mediaDefault),this._currentMediaIndices=[],nh(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,n=this._timelineOptions;return e=n.length&&(t=t.getComponent("timeline"))?nh(n[t.getCurrentIndex()]):e},getMediaOption:function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,o=this._mediaDefault,r=[],a=[];if(i.length||o){for(var s,l,h=0,u=i.length;h<u;h++)!function(t,e,n){var i={width:e,height:n,aspectratio:e/n},o=!0;return D(t,function(t,e){var n,e=e.match(rh);e&&e[1]&&e[2]&&(n=e[1],e=e[2].toLowerCase(),e=i[e],t=t,("min"===(n=n)?t<=e:"max"===n?e<=t:e===t)||(o=!1))}),o}(i[h].query,e,n)||r.push(h);(r=!r.length&&o?[-1]:r).length&&(s=r,l=this._currentMediaIndices,s.join(",")!==l.join(","))&&(a=ih(r,function(t){return nh((-1===t?o:i[t]).option)})),this._currentMediaIndices=r}return a}}),{arrayRows_column:{pure:n.persistent=!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:Ih},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],n=this._data,i=0;i<n.length;i++){var o=n[i];e.push(o?o[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:Mh,getItem:Ch,appendData:Ih},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,t=this._data[t];return t?t.length:0},getItem:function(t){for(var e=[],n=this._source.dimensionsDefine,i=0;i<n.length;i++){var o=this._data[n[i].name];e.push(o?o[t]:null)}return e},appendData:function(t){var o=this._data;D(t,function(t,e){for(var n=o[e]||(o[e]=[]),i=0;i<(t||[]).length;i++)n.push(t[i])})}},original:{count:Mh,getItem:Ch,appendData:Ih},typedArray:{persistent:!(n.getSource=function(){return this._source}),pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var n=this._dimSize*t,i=0;i<this._dimSize;i++)e[i]=this._data[n+i];return e},appendData:function(t){I&&p(rt(t),"Added data must be TypedArray if data in initialization is TypedArray"),this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}});function Mh(){return this._data.length}function Ch(t){return this._data[t]}function Ih(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}var Th={arrayRows:Dh,objectRows:function(t,e,n,i){return null!=n?t[i]:t},keyedColumns:Dh,original:function(t,e,n,i){t=Io(t);return null!=n&&t instanceof Array?t[n]:t},typedArray:Dh};function Dh(t,e,n,i){return null!=n?t[n]:t}var kh={arrayRows:Ah,objectRows:function(t,e,n,i){return Ph(t[e],this._dimensionInfos[e])},keyedColumns:Ah,original:function(t,e,n,i){var o=t&&(null==t.value?t:t.value);return this._rawData.pure||!xo(t=t)||t instanceof Array||(this.hasItemOption=!0),Ph(o instanceof Array?o[i]:o,this._dimensionInfos[e])},typedArray:function(t,e,n,i){return t[i]}};function Ah(t,e,n,i){return Ph(t[i],this._dimensionInfos[e])}function Ph(t,e){var n=e&&e.type;return"ordinal"===n?(e=e&&e.ordinalMeta)?e.parseAndCollect(t):t:null==(t="time"===n&&"number"!=typeof t&&null!=t&&"-"!==t?+Zs(t):t)||""===t?NaN:+t}function Oh(t,e,n){if(t){var i,o,r,a=t.getRawDataItem(e);if(null!=a)return i=t.getProvider().getSource().sourceFormat,(t=t.getDimensionInfo(n))&&(o=t.name,r=t.index),Th[i](a,e,r,o)}}var Lh=/\{@(.+?)\}/g,o={getDataParams:function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),o=n.getRawIndex(t),r=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"color"),t=n.getItemVisual(t,"borderColor"),l=this.ecModel.getComponent("tooltip"),l=Bo(l&&l.get("renderMode")),h=this.mainType,u="series"===h,n=n.userOutput;return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:u?this.subType:null,seriesIndex:this.seriesIndex,seriesId:u?this.id:null,seriesName:u?this.name:null,name:r,dataIndex:o,data:a,dataType:e,value:i,color:s,borderColor:t,dimensionNames:n?n.dimensionNames:null,encode:n?n.encode:null,marker:ll({color:s,renderMode:l}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(i,t,e,n,o){t=t||"normal";var r=this.getData(e),a=r.getItemModel(i),e=this.getDataParams(i,e),a=(null!=n&&e.value instanceof Array&&(e.value=e.value[n]),a.get("normal"===t?[o||"label","formatter"]:[t,o||"label","formatter"]));return"function"==typeof a?(e.status=t,e.dimensionIndex=n,a(e)):"string"==typeof a?sl(a,e).replace(Lh,function(t,e){var n=e.length;return"["===e.charAt(0)&&"]"===e.charAt(n-1)&&(e=+e.slice(1,n-1)),Oh(r,i,e)}):void 0},getRawValue:function(t,e){return Oh(this.getData(e),t)},formatTooltip:function(){}};function Eh(t){return new zh(t)}function zh(t){this._reset=(t=t||{}).reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}var Bh,Rh,Nh,Fh,Hh,Vh,r=zh.prototype,Gh=(r.perform=function(t){var e,n,i=this._upstream,o=t&&t.skip,r=(this._dirty&&i&&((r=this.context).data=r.outputData=i.context.outputData),this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!o&&(e=this._plan(this.context)),h(this._modBy)),a=this._modDataCount||0,s=h(t&&t.modBy),l=t&&t.modDataCount||0;function h(t){return t=1<=t?t:1}r===s&&a===l||(e="reset"),!this._dirty&&"reset"!==e||(this._dirty=!1,n=function(t,e){var n,i;t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null,!e&&t._reset&&((n=t._reset(t.context))&&n.progress&&(i=n.forceFirstProgress,n=n.progress),b(n))&&!n.length&&(n=null);t._progress=n,t._modBy=t._modDataCount=null;e=t._downstream;return e&&e.dirty(),i}(this,o)),this._modBy=s,this._modDataCount=l;r=t&&t.step;if(i?(I&&p(null!=i._outputDueEnd),this._dueEnd=i._outputDueEnd):(I&&p(!this._progress||this._count),this._dueEnd=this._count?this._count(this.context):1/0),this._progress){var u=this._dueIndex,c=Math.min(null!=r?this._dueIndex+r:1/0,this._dueEnd);if(!o&&(n||u<c)){var d=this._progress;if(b(d))for(var f=0;f<d.length;f++)Yh(this,d[f],u,c,s,l);else Yh(this,d,u,c,s,l)}this._dueIndex=c;a=null!=this._settedOutputEnd?this._settedOutputEnd:c;I&&p(a>=this._outputDueEnd),this._outputDueEnd=a}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},Vh={reset:function(t,e,n,i){Rh=t,Bh=e,Nh=n,Fh=i,Hh=Math.ceil(Fh/Nh),Vh.next=1<Nh&&0<Fh?Xh:Wh}});function Wh(){return Rh<Bh?Rh++:null}function Xh(){var t=Rh%Hh*Nh+Math.ceil(Rh/Hh),t=Bh<=Rh?null:t<Fh?t:Rh;return Rh++,t}function Yh(t,e,n,i,o,r){Gh.reset(n,i,o,r),t._callingProgress=e,t._callingProgress({start:n,end:i,count:i-n,next:Gh.next},t.context)}r.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},r.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},r.pipe=function(t){I&&p(t&&!t._disposed&&t!==this),this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},r.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},r.getUpstream=function(){return this._upstream},r.getDownstream=function(){return this._downstream},r.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var Uh=Po(),qh=c.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendVisualProvider:null,visualColorAccessPath:"itemStyle.color",visualBorderColorAccessPath:"itemStyle.borderColor",layoutMode:null,init:function(t,e,n,i){this.seriesIndex=this.componentIndex,this.dataTask=Eh({count:Zh,reset:Kh}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),Hl(this);t=this.getInitialData(t,n);Qh(t,this),this.dataTask.context.data=t,I&&p(t,"getInitialData returned invalid data."),Uh(this).dataBeforeProcessed=t,jh(this)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?xl(t):{},o=this.subType;c.hasClass(o),g(t,e.getTheme().get(this.subType)),g(t,this.getDefaultOption()),Mo(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&_l(t,i,n)},mergeOption:function(t,e){t=g(this.option,t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode,n=(n&&_l(this.option,t,n),Hl(this),this.getInitialData(t,e));Qh(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,Uh(this).dataBeforeProcessed=n,jh(this)},fillDataTextStyle:function(t){if(t&&!rt(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&Mo(t[n],"label",e)},getInitialData:function(){},appendData:function(t){this.getRawData().appendData(t.data)},getData:function(t){var e=tu(this);return e?(e=e.context.data,null==t?e:e.getLinkedData(t)):Uh(this).data},setData:function(t){var e,n=tu(this);n&&((e=n.context).data!==t&&n.modifyOutputEnd&&n.setOutputEnd(t.count()),e.outputData=t,n!==this.dataTask)&&(e.data=t),Uh(this).data=t},getSource:function(){return Fl(this).source},getRawData:function(){return Uh(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(e,r,t,a){var s=this,n="html"===(a=a||"html")?"<br/>":"\n",l="richText"===a,h={},u=0;function i(t){return{renderMode:a,content:ol(Js(t)),style:h}}var c,d,f=this.getData(),o=f.mapDimension("defaultedTooltip",!0),p=o.length,g=this.getRawValue(e),m=b(g);function y(t,e){var n,i,o,e=f.getDimensionInfo(e);e&&!1!==e.otherDims.tooltip&&(n=e.type,i="sub"+s.seriesIndex+"at"+u,o="string"==typeof(o=ll({color:v,type:"subItem",renderMode:a,markerId:i}))?o:o.content,(o=(c?o+ol(e.displayName||"-")+": ":"")+ol("ordinal"===n?t+"":"time"===n?r?"":ul("yyyy/MM/dd hh:mm:ss",t):Js(t)))&&d.push(o),l)&&(h[i]=v,++u)}var v=(v=A(v=f.getItemVisual(e,"color"))&&v.colorStops?(v.colorStops[0]||{}).color:v)||"transparent",_=(1<p||m&&!p?(c=tt(_=g,function(t,e,n){n=f.getDimensionInfo(n);return t|(n&&!1!==n.tooltip&&null!=n.displayName)},0),d=[],o.length?D(o,function(t){y(Oh(f,e,t),t)}):D(_,y),_=c?l?"\n":"<br/>":"",_+=d.join(_||", "),{renderMode:a,content:_,style:h}):i(p?Oh(f,e,o[0]):m?g[0]:g)).content,p=s.seriesIndex+"at"+u,o=ll({color:v,type:"item",renderMode:a,markerId:p}),m=(h[p]=v,++u,f.getName(e)),g=this.name,p=(g=(g=Do(this)?g:"")?ol(g)+(r?": ":n):"","string"==typeof o?o:o.content);return{html:r?p+g+_:g+p+(m?ol(m)+": "+_:_),markers:h}},isAnimationEnabled:function(){var t;return!(y.node||(t=this.getShallow("animation"))&&this.getData().count()>this.getShallow("animationThreshold"))&&t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,n){var i=this.ecModel;return Dl.getColorFromPalette.call(this,t,e,n)||i.getColorFromPalette(t,e,n)},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});function jh(t){var e,n,i=t.name;Do(t)||(t.name=(t=(e=(t=t).getRawData()).mapDimension("seriesName",!0),n=[],D(t,function(t){t=e.getDimensionInfo(t);t.displayName&&n.push(t.displayName)}),n.join(" ")||i))}function Zh(t){return t.model.getRawData().count()}function Kh(t){t=t.model;return t.setData(t.getRawData().cloneShallow()),$h}function $h(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function Qh(e,n){D(e.CHANGABLE_METHODS,function(t){e.wrapMethod(t,l(Jh,n))})}function Jh(t){t=tu(t);t&&t.setOutputEnd(this.count())}function tu(t){var e,n=(t.ecModel||{}).scheduler,n=n&&n.getPipeline(t.uid);if(n)return(n=n.currentTask)&&(e=n.agentStubMap)?e.get(t.uid):n}i(qh,o),i(qh,Dl);var eu=function(){this.group=new h,this.uid=Vs("viewComponent")},Sa=(eu.prototype={constructor:eu,init:function(t,e){},render:function(t,e,n,i){},dispose:function(){},filterForExposedEvent:null},eu.prototype);function nu(){var r=Po();return function(t){var e=r(t),t=t.pipelineContext,n=e.large,i=e.progressiveRender,o=e.large=t&&t.large,e=e.progressiveRender=t&&t.progressiveRender;return!!(n^o||i^e)&&"reset"}}Sa.updateView=Sa.updateLayout=Sa.updateVisual=function(t,e,n,i){},Ho(eu),Yo(eu,{registerWhenExtend:!0});var iu=Po(),ou=nu();function ru(){this.group=new h,this.uid=Vs("viewChart"),this.renderTask=Eh({plan:lu,reset:hu}),this.renderTask.context={view:this}}s=ru.prototype={type:"chart",init:function(t,e){},render:function(t,e,n,i){},highlight:function(t,e,n,i){su(t.getData(),i,"emphasis")},downplay:function(t,e,n,i){su(t.getData(),i,"normal")},remove:function(t,e){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};function au(t,e,n){if(t&&(t.trigger(e,n),t.isGroup)&&!ys(t))for(var i=0,o=t.childCount();i<o;i++)au(t.childAt(i),e,n)}function su(e,t,n){var i=Ao(e,t),o=t&&null!=t.highlightKey?vs(t.highlightKey):null;null!=i?D(So(i),function(t){au(e.getItemGraphicEl(t),n,o)}):e.eachItemGraphicEl(function(t){au(t,n,o)})}function lu(t){return ou(t.model)}function hu(t){var e=t.model,n=t.ecModel,i=t.api,o=t.payload,r=e.pipelineContext.progressiveRender,t=t.view,a=o&&iu(o).updateMethod,r=r?"incrementalPrepareRender":a&&t[a]?a:"render";return"render"!==r&&t[r](e,n,i,o),uu[r]}s.updateView=s.updateLayout=s.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},Ho(ru,["dispose"]),Yo(ru,{registerWhenExtend:!0}),ru.markUpdateMethod=function(t,e){iu(t).updateMethod=e};var uu={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},cu="\0__throttleOriginMethod",du="\0__throttleRate",fu="\0__throttleType";function pu(t,n,i){var o,r,a,s,l,h=0,u=0,c=null;function d(){u=(new Date).getTime(),c=null,t.apply(a,s||[])}n=n||0;function e(){o=(new Date).getTime(),a=this,s=arguments;var t=l||n,e=l||i;l=null,r=o-(e?h:u)-t,clearTimeout(c),e?c=setTimeout(d,t):0<=r?d():c=setTimeout(d,-r),h=o}return e.clear=function(){c&&(clearTimeout(c),c=null)},e.debounceNextCall=function(t){l=t},e}var u={createOnAllSeries:!0,performRawSeries:!0,reset:function(e,t){var n=e.getData(),o=(e.visualColorAccessPath||"itemStyle.color").split("."),i=e.get(o),r=!it(i)||i instanceof Ea?null:i,a=(i&&!r||(i=e.getColorFromPalette(e.name,null,t.getSeriesCount())),n.setVisual("color",i),(e.visualBorderColorAccessPath||"itemStyle.borderColor").split(".")),i=e.get(a);if(n.setVisual("borderColor",i),!t.isSeriesFiltered(e))return r&&n.each(function(t){n.setItemVisual(t,"color",r(e.getDataParams(t)))}),{dataEach:n.hasItemOption?function(t,e){var n=t.getItemModel(e),i=n.get(o,!0),n=n.get(a,!0);null!=i&&t.setItemVisual(e,"color",i),null!=n&&t.setItemVisual(e,"borderColor",n)}:null}}},gu={legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};function mu(t,e){var l,h,u,n,c,r=e.getModel("aria");function d(t,e){var n;return"string"!=typeof t?t:(n=t,D(e,function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),n)}function f(t){var e=r.get(t);if(null!=e)return e;for(var n=t.split("."),i=gu.aria,o=0;o<n.length;++o)i=i[n[o]];return i}r.get("show")&&(r.get("description")?t.setAttribute("aria-label",r.get("description")):(l=0,e.eachSeries(function(t,e){++l},this),h=r.get("data.maxCount")||10,n=r.get("series.maxCount")||10,u=Math.min(l,n),l<1||(n=(n=function(){var t=e.getModel("title").option;t&&t.length&&(t=t[0]);return t&&t.text}())?d(f("general.withTitle"),{title:n}):f("general.withoutTitle"),c=[],n+=d(f(1<l?"series.multiple.prefix":"series.single.prefix"),{seriesCount:l}),e.eachSeries(function(t,e){if(e<u){var e=t.get("name"),n="series."+(1<l?"multiple":"single")+".",e=f(e?n+"withName":n+"withoutName");e=d(e,{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:(n=t.subType,gu.series.typeNames[n]||"自定义图")});for(var i,o,r=t.getData(),a=((window.data=r).count()>h?e+=d(f("data.partialData"),{displayCnt:h}):e+=f("data.allData"),[]),s=0;s<r.count();s++)s<h&&(i=r.getName(s),o=Oh(r,s),a.push(d(f(i?"data.withName":"data.withoutName"),{name:i,value:o})));e+=a.join(f("data.separator.middle"))+f("data.separator.end"),c.push(e)}}),n+=c.join(f("series.multiple.separator.middle"))+f("series.multiple.separator.end"),t.setAttribute("aria-label",n))))}var yu=Math.PI;function vu(t,e,n,i){this.ecInstance=t,this.api=e,this.unfinished;n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice();this._allHandlers=n.concat(i),this._stageTaskMap=P()}n=vu.prototype;function _u(s,t,l,h,u){var c;function d(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}u=u||{},D(t,function(i,t){var e,n,o,r,a;u.visualType&&u.visualType!==i.visualType||(e=(n=s._stageTaskMap.get(i.uid)).seriesTaskMap,(n=n.overallTask)?((r=n.agentStubMap).each(function(t){d(u,t)&&(t.dirty(),o=!0)}),o&&n.dirty(),xu(n,h),a=s.getPerformArgs(n,u.block),r.each(function(t){t.perform(a)}),c|=n.perform(a)):e&&e.each(function(t,e){d(u,t)&&t.dirty();var n=s.getPerformArgs(t,u.block);n.skip=!i.performRawSeries&&l.isSeriesFiltered(t.context.model),xu(t,h),c|=t.perform(n)}))}),s.unfinished|=c}n.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){t=t.overallTask;t&&t.dirty()})},n.getPerformArgs=function(t,e){var n,i;if(t.__pipeline)return i=(n=this._pipelineMap.get(t.__pipeline.id)).context,{step:e=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,modBy:null!=(t=i&&i.modDataCount)?Math.ceil(t/e):null,modDataCount:t}},n.getPipeline=function(t){return this._pipelineMap.get(t)},n.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),e=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,o=t.get("large")&&i>=t.get("largeThreshold"),i="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:e,modDataCount:i,large:o}},n.restorePipelines=function(t){var i=this,o=i._pipelineMap=P();t.eachSeries(function(t){var e=t.getProgressive(),n=t.uid;o.set(n,{id:n,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:e&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(e||700),count:0}),Au(i,t,t.dataTask)})},n.prepareStageTasks=function(){var _=this._stageTaskMap,x=this.ecInstance.getModel(),w=this.api;D(this._allHandlers,function(t){var n,i,o,r,a,s,l,e,h,u,c,d,f,p,g,m=_.get(t.uid)||_.set(t.uid,[]);function y(t){var e=t.uid,e=a.get(e)||a.set(e,Eh({plan:Cu,reset:Iu,count:ku}));e.context={model:t,ecModel:o,api:r,useClearVisual:i.isVisual&&!i.isLayout,plan:i.plan,reset:i.reset,scheduler:n},Au(n,t,e)}function v(t){var e=t.uid,n=c.get(e);n||(n=c.set(e,Eh({reset:bu,onDirty:Mu})),u.dirty()),n.context={model:t,overallProgress:f,modifyOutputEnd:p},n.agent=u,n.__block=f,Au(l,t,n)}t.reset&&(n=this,i=t,o=x,r=w,a=(e=m).seriesTaskMap||(e.seriesTaskMap=P()),e=i.seriesType,h=i.getTargetSeries,i.createOnAllSeries?o.eachRawSeries(y):e?o.eachRawSeriesByType(e,y):h&&h(o,r).each(y),s=n._pipelineMap,a.each(function(t,e){s.get(e)||(t.dispose(),a.removeKey(e))})),t.overallReset&&(l=this,e=t,h=x,t=w,(u=(m=m).overallTask=m.overallTask||Eh({reset:wu})).context={ecModel:h,api:t,overallReset:e.overallReset,scheduler:l},c=u.agentStubMap=u.agentStubMap||P(),m=e.seriesType,d=e.getTargetSeries,f=!0,p=e.modifyOutputEnd,m?h.eachRawSeriesByType(m,v):d?d(h,t).each(v):(f=!1,D(h.getSeries(),v)),g=l._pipelineMap,c.each(function(t,e){g.get(e)||(t.dispose(),u.dirty(),c.removeKey(e))}))},this)},n.prepareView=function(t,e,n,i){var o=t.renderTask,r=o.context;r.model=e,r.ecModel=n,r.api=i,o.__block=!t.incrementalPrepareRender,Au(this,e,o)},n.performDataProcessorTasks=function(t,e){_u(this,this._dataProcessorHandlers,t,e,{block:!0})},n.performVisualTasks=function(t,e,n){_u(this,this._visualHandlers,t,e,n)},n.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},n.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}}while(e=e.getUpstream())})};var xu=n.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)};function wu(t){t.overallReset(t.ecModel,t.api,t.payload)}function bu(t,e){return t.overallProgress&&Su}function Su(){this.agent.dirty(),this.getDownstream().dirty()}function Mu(){this.agent&&this.agent.dirty()}function Cu(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function Iu(t){t.useClearVisual&&t.data.clearAllVisual();t=t.resetDefines=So(t.reset(t.model,t.ecModel,t.api,t.payload));return 1<t.length?S(t,function(t,e){return Du(e)}):Tu}var Tu=Du(0);function Du(r){return function(t,e){var n=e.data,i=e.resetDefines[r];if(i&&i.dataEach)for(var o=t.start;o<t.end;o++)i.dataEach(n,o);else i&&i.progress&&i.progress(t,n)}}function ku(t){return t.data.count()}function Au(t,e,n){e=e.uid,t=t._pipelineMap.get(e);t.head||(t.head=n),t.tail&&t.tail.pipe(n),(t.tail=n).__idxInPipeline=t.count++,n.__pipeline=t}vu.wrapStageHandler=function(t,e){return(t=it(t)?{overallReset:t,seriesType:function(t){Pu=null;try{t(Ou,Lu)}catch(t){}return Pu}(t)}:t).uid=Vs("stageHandler"),e&&(t.visualType=e),t};var Pu,Ou={},Lu={};function Eu(t,e){for(var n in e.prototype)t[n]=vt}Eu(Ou,ql),Eu(Lu,Ql),Ou.eachSeriesByType=Ou.eachRawSeriesByType=function(t){Pu=t},Ou.eachComponent=function(t){"series"===t.mainType&&t.subType&&(Pu=t.subType)};function zu(){return{axisLine:{lineStyle:{color:Bu}},axisTick:{lineStyle:{color:Bu}},axisLabel:{textStyle:{color:Bu}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:Bu}}}}var r=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],o={color:r,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],r]},Bu="#eee",Sa=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],s={color:Sa,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:Bu},crossStyle:{color:Bu},label:{color:"#000"}}},legend:{textStyle:{color:Bu}},textStyle:{color:Bu},title:{textStyle:{color:Bu}},toolbox:{iconStyle:{normal:{borderColor:Bu}}},dataZoom:{textStyle:{color:Bu}},visualMap:{textStyle:{color:Bu}},timeline:{lineStyle:{color:Bu},itemStyle:{normal:{color:Sa[1]}},label:{normal:{textStyle:{color:Bu}}},controlStyle:{normal:{color:Bu,borderColor:Bu}}},timeAxis:zu(),logAxis:zu(),valueAxis:zu(),categoryAxis:zu(),line:{symbol:"circle"},graph:{color:Sa},gauge:{title:{textStyle:{color:Bu}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};s.categoryAxis.splitLine.show=!1,c.extend({type:"dataset",defaultOption:{seriesLayoutBy:zl,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){var t=this,e=t.option.source,n=Ll;if(rt(e))n=El;else if(b(e)){0===e.length&&(n=Al);for(var i=0,o=e.length;i<o;i++){var r=e[i];if(null!=r){if(b(r)){n=Al;break}if(A(r)){n=Pl;break}}}}else if(A(e)){for(var a in e)if(e.hasOwnProperty(a)&&J(e[a])){n=Ol;break}}else if(null!=e)throw new Error("Invalid data");Fl(t).sourceFormat=n}}),eu.extend({type:"dataset"}),a.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var n=e.cx,i=e.cy,o=e.rx,e=e.ry,r=.5522848*o,a=.5522848*e;t.moveTo(n-o,i),t.bezierCurveTo(n-o,i-a,n-r,i-e,n,i-e),t.bezierCurveTo(n+r,i-e,n+o,i-a,n+o,i),t.bezierCurveTo(n+o,i+a,n+r,i+e,n,i+e),t.bezierCurveTo(n-r,i+e,n-o,i+a,n-o,i),t.closePath()}});var Ru=P(),Nu=function(t,e,n){e=b(e)?e:e.svg?[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(n=e.specialAreas,e=e.geoJson),[{type:"geoJSON",source:e,specialAreas:n}]);return D(e,function(t){var e=t.type,n=("geoJson"===e&&(e=t.type="geoJSON"),Hu[e]);I&&p(n,"Illegal map type: "+e),n(t)}),Ru.set(t,e)},Fu=function(t){return Ru.get(t)},Hu={geoJSON:function(t){var e=t.source;t.geoJSON=k(e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=function(t){for(9===(t=k(t)?(new DOMParser).parseFromString(t,"text/xml"):t).nodeType&&(t=t.firstChild);"svg"!==t.nodeName.toLowerCase()||1!==t.nodeType;)t=t.nextSibling;return t}(t.source)}},Vu=p,x=D,Gu=it,Wu=A,Xu=c.parseClassType,Yu={zrender:"4.3.2"},n={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:3500,COMPONENT:4e3,BRUSH:5e3}},Uu="__flagInMainProcess",qu="__optionUpdated",ju=/^[a-zA-Z0-9_]+$/;function Zu(i,o){return function(t,e,n){!o&&this._disposed?dc(this.id):(t=t&&t.toLowerCase(),Ht.prototype[i].call(this,t,e,n))}}function Ku(){Ht.call(this)}function $u(t,e,n){n=n||{},"string"==typeof e&&(e=wc[e]),this.id,this.group,this._dom=t;var i="canvas",t=(I&&(i=("undefined"==typeof window?global:window).__ECHARTS__DEFAULT__RENDERER__||i),this._zr=yo(t,{renderer:n.renderer||i,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height}));this._throttledZrFlush=pu(nt(t.flush,t),17);(e=M(e))&&xh(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new th;var o,r,a,n=this._api=(i=(o=this)._coordSysMgr,C(new Ql(o),{getCoordinateSystems:nt(i.getCoordinateSystems,i),getComponentByElement:function(t){for(;t;){var e=t.__ecComponentInfo;if(null!=e)return o._model.getComponent(e.mainType,e.index);t=t.parent}}}));function s(t,e){return t.__prio-e.__prio}An(xc,s),An(yc,s),this._scheduler=new vu(this,n,yc,xc),Ht.call(this,this._ecEventProcessor=new pc),this._messageCenter=new Ku,this._initEvents(),this.resize=nt(this.resize,this),this._pendingActions=[],t.animation.on("frame",this._onframe,this),a=this,(r=t).on("rendered",function(){a.trigger("rendered"),!r.animation.isFinished()||a[qu]||a._scheduler.unfinished||a._pendingActions.length||a.trigger("finished")}),gt(this)}Ku.prototype.on=Zu("on",!0),Ku.prototype.off=Zu("off",!0),Ku.prototype.one=Zu("one",!0),i(Ku,Ht);r=$u.prototype;function Qu(t,e,n){if(this._disposed)dc(this.id);else{var i=this._model,o=this._coordSysMgr.getCoordinateSystems();e=Lo(i,e);for(var r=0;r<o.length;r++){var a=o[r];if(a[t]&&null!=(a=a[t](i,e,n)))return a}I&&console.warn("No coordinate system that supports "+t+" found by the given finder.")}}r._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[qu]){var e=this[qu].silent;this[Uu]=!0,tc(this),Ju.update.call(this),this[Uu]=!1,this[qu]=!1,oc.call(this,e),rc.call(this,e)}else if(t.unfinished){var n=1,i=this._model,o=this._api;t.unfinished=!1;do{var r=+new Date}while(t.performSeriesTasks(i),t.performDataProcessorTasks(i),nc(this,i),t.performVisualTasks(i),hc(this,this._model,0,"remain"),0<(n-=+new Date-r)&&t.unfinished);t.unfinished||this._zr.flush()}}},r.getDom=function(){return this._dom},r.getZr=function(){return this._zr},r.setOption=function(t,e,n){var i,o,r;I&&Vu(!this[Uu],"`setOption` should not be called during main process."),this._disposed?dc(this.id):(Wu(e)&&(n=e.lazyUpdate,i=e.silent,e=e.notMerge),this[Uu]=!0,this._model&&!e||(e=new ah(this._api),o=this._theme,(r=this._model=new ql).scheduler=this._scheduler,r.init(null,null,o,e)),this._model.setOption(t,vc),n?(this[qu]={silent:i},this[Uu]=!1):(tc(this),Ju.update.call(this),this._zr.flush(),this[qu]=!1,this[Uu]=!1,oc.call(this,i),rc.call(this,i)))},r.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},r.getModel=function(){return this._model},r.getOption=function(){return this._model&&this._model.getOption()},r.getWidth=function(){return this._zr.getWidth()},r.getHeight=function(){return this._zr.getHeight()},r.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},r.getRenderedCanvas=function(t){if(y.canvasSupported)return(t=t||{}).pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor"),this._zr.painter.getRenderedCanvas(t)},r.getSvgDataURL=function(){var t;if(y.svgSupported)return D((t=this._zr).storage.getDisplayList(),function(t){t.stopAnimation(!0)}),t.painter.toDataURL()},r.getDataURL=function(t){var e,n,i,o;if(!this._disposed)return o=(t=t||{}).excludeComponents,e=this._model,n=[],i=this,x(o,function(t){e.eachComponent({mainType:t},function(t){t=i._componentsMap[t.__viewId];t.group.ignore||(n.push(t),t.group.ignore=!0)})}),o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png")),x(n,function(t){t.group.ignore=!1}),o;dc(this.id)},r.getConnectedDataURL=function(i){var o,r,a,s,l,h,u,c,d,e,t,n,f,p,g;if(this._disposed)dc(this.id);else if(y.canvasSupported)return o="svg"===i.type,r=this.group,a=Math.min,s=Math.max,Mc[r]?(h=l=1/0,c=u=-1/0,d=[],e=i&&i.pixelRatio||1,D(Sc,function(t,e){var n;t.group===r&&(n=o?t.getZr().painter.getSvgDom().innerHTML:t.getRenderedCanvas(M(i)),t=t.getDom().getBoundingClientRect(),l=a(t.left,l),h=a(t.top,h),u=s(t.right,u),c=s(t.bottom,c),d.push({dom:n,left:t.left,top:t.top}))}),t=(u*=e)-(l*=e),n=(c*=e)-(h*=e),f=K(),(p=yo(f,{renderer:o?"svg":"canvas"})).resize({width:t,height:n}),o?(g="",x(d,function(t){var e=t.left-l,n=t.top-h;g+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),p.painter.getSvgRoot().innerHTML=g,i.connectedBackgroundColor&&p.painter.setBackgroundColor(i.connectedBackgroundColor),p.refreshImmediately(),p.painter.toDataURL()):(i.connectedBackgroundColor&&p.add(new O({shape:{x:0,y:0,width:t,height:n},style:{fill:i.connectedBackgroundColor}})),x(d,function(t){t=new Vi({style:{x:t.left*e-l,y:t.top*e-h,image:t.dom}});p.add(t)}),p.refreshImmediately(),f.toDataURL("image/"+(i&&i.type||"png")))):this.getDataURL(i)},r.convertToPixel=l(Qu,"convertToPixel"),r.convertFromPixel=l(Qu,"convertFromPixel"),r.containPixel=function(t,i){var o;if(!this._disposed)return D(t=Lo(this._model,t),function(t,n){0<=n.indexOf("Models")&&D(t,function(t){var e=t.coordinateSystem;e&&e.containPoint?o|=!!e.containPoint(i):"seriesModels"===n?(e=this._chartsMap[t.__viewId])&&e.containPoint?o|=e.containPoint(i,t):I&&console.warn(n+": "+(e?"The found component do not support containPoint.":"No view mapping to the found component.")):I&&console.warn(n+": containPoint is not supported")},this)},this),!!o;dc(this.id)},r.getVisual=function(t,e){var n=(t=Lo(this._model,t,{defaultMainType:"series"})).seriesModel,n=(!I||n||console.warn("There is no specified seires model"),n.getData()),t=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?n.indexOfRawIndex(t.dataIndex):null;return null!=t?n.getItemVisual(t,e):n.getVisual(e)},r.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},r.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var Ju={prepareAndUpdate:function(t){tc(this),Ju.update.call(this,t)},update:function(t){var e=this._model,n=this._api,i=this._zr,o=this._coordSysMgr,r=this._scheduler;e&&(r.restoreData(e,t),r.performSeriesTasks(e),o.create(e,n),r.performDataProcessorTasks(e,t),nc(this,e),o.update(e,n),sc(e),r.performVisualTasks(e,t),lc(this,e,n,t),o=e.get("backgroundColor")||"transparent",y.canvasSupported?i.setBackgroundColor(o):(o=$e(r=Ye(o),"rgb"),0===r[3]&&(o="transparent")),uc(e,n))},updateTransform:function(i){var o,n,r=this._model,a=this,s=this._api;r&&(o=[],r.eachComponent(function(t,e){var n=a.getViewOfComponentModel(e);n&&n.__alive&&(!n.updateTransform||(e=n.updateTransform(e,r,s,i))&&e.update)&&o.push(n)}),n=P(),r.eachSeries(function(t){var e=a._chartsMap[t.__viewId];(!e.updateTransform||(e=e.updateTransform(t,r,s,i))&&e.update)&&n.set(t.uid,1)}),sc(r),this._scheduler.performVisualTasks(r,i,{setDirty:!0,dirtyMap:n}),hc(a,r,0,i,n),uc(r,this._api))},updateView:function(t){var e=this._model;e&&(ru.markUpdateMethod(t,"updateView"),sc(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),lc(this,this._model,this._api,t),uc(e,this._api))},updateVisual:function(t){Ju.update.call(this,t)},updateLayout:function(t){Ju.update.call(this,t)}};function tc(t){var e=t._model,n=t._scheduler;n.restorePipelines(e),n.prepareStageTasks(),ac(t,"component",e,n),ac(t,"chart",e,n),n.plan()}function ec(e,n,i,o,t){var r,a,s=e._model;function l(t){t&&t.__alive&&t[n]&&t[n](t.__model,s,e._api,i)}o?((r={})[o+"Id"]=i[o+"Id"],r[o+"Index"]=i[o+"Index"],r[o+"Name"]=i[o+"Name"],r={mainType:o,query:r},t&&(r.subType=t),null!=(a=i.excludeSeriesId)&&(a=P(So(a))),s&&s.eachComponent(r,function(t){a&&null!=a.get(t.id)||l(e["series"===o?"_chartsMap":"_componentsMap"][t.__viewId])},e)):x(e._componentsViews.concat(e._chartsViews),l)}function nc(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})}function ic(e,t){var n,i=e.type,o=e.escapeConnect,r=gc[i],a=r.actionInfo,s=(l=(a.update||"update").split(":")).pop(),l=null!=l[0]&&Xu(l[0]),h=(this[Uu]=!0,[e]),u=!1,c=(e.batch&&(u=!0,h=S(e.batch,function(t){return(t=T(C({},t),e)).batch=null,t})),[]),d="highlight"===i||"downplay"===i;x(h,function(t){(n=(n=r.action(t,this._model,this._api))||C({},t)).type=a.event||n.type,c.push(n),d?ec(this,s,t,"series"):l&&ec(this,s,t,l.main,l.sub)},this),"none"===s||d||l||(this[qu]?(tc(this),Ju.update.call(this,e),this[qu]=!1):Ju[s].call(this,e)),n=u?{type:a.event||i,escapeConnect:o,batch:c}:c[0],this[Uu]=!1,t||this._messageCenter.trigger(n.type,n)}function oc(t){for(var e=this._pendingActions;e.length;){var n=e.shift();ic.call(this,n,t)}}function rc(t){t||this.trigger("updated")}function ac(t,e,r,a){for(var s="component"===e,l=s?t._componentsViews:t._chartsViews,h=s?t._componentsMap:t._chartsMap,u=t._zr,c=t._api,n=0;n<l.length;n++)l[n].__alive=!1;function i(t){var e,n,i="_ec_"+t.id+"_"+t.type,o=h[i];o||(e=Xu(t.type),n=s?eu.getClass(e.main,e.sub):ru.getClass(e.sub),I&&Vu(n,e.sub+" does not exist."),(o=new n).init(r,c),h[i]=o,l.push(o),u.add(o.group)),t.__viewId=o.__id=i,o.__alive=!0,o.__model=t,o.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},s||a.prepareView(o,t,r,c)}s?r.eachComponent(function(t,e){"series"!==t&&i(e)}):r.eachSeries(i);for(n=0;n<l.length;){var o=l[n];o.__alive?n++:(s||o.renderTask.dispose(),u.remove(o.group),o.dispose(r,c),l.splice(n,1),delete h[o.__id],o.__id=o.group.__ecComponentInfo=null)}}function sc(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function lc(t,e,n,i){var o,r,a,s,l;o=t,r=e,a=n,s=i,x(l||o._componentsViews,function(t){var e=t.__model;t.render(e,r,a,s),fc(e,t)}),x(t._chartsViews,function(t){t.__alive=!1}),hc(t,e,0,i),x(t._chartsViews,function(t){t.__alive||t.remove(e,n)})}function hc(o,t,e,r,a){var s,n,i,l,h,u=o._scheduler;t.eachSeries(function(t){var e=o._chartsMap[t.__viewId],n=(e.__alive=!0,e.renderTask),n=(u.updatePayload(n,r),a&&a.get(t.uid)&&n.dirty(),s|=n.perform(u.getPerformArgs(n)),e.group.silent=!!t.get("silent"),fc(t,e),t),t=e,i=n.get("blendMode")||null;I&&!y.canvasSupported&&i&&"source-over"!==i&&console.warn("Only canvas support blendMode"),t.group.traverse(function(t){t.isGroup||t.style.blend!==i&&t.setStyle("blend",i),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",i)})})}),u.unfinished|=s,i=t,l=(n=o)._zr.storage,h=0,l.traverse(function(t){h++}),h>i.get("hoverLayerThreshold")&&!y.node&&i.eachSeries(function(t){t.preventUsingHoverLayer||(t=n._chartsMap[t.__viewId]).__alive&&t.group.traverse(function(t){t.useHoverLayer=!0})}),mu(o._zr.dom,t)}function uc(e,n){x(_c,function(t){t(e,n)})}r.resize=function(t){var e;I&&Vu(!this[Uu],"`resize` should not be called during main process."),this._disposed?dc(this.id):(this._zr.resize(t),e=this._model,this._loadingFX&&this._loadingFX.resize(),e&&(e=e.resetOption("media"),t=t&&t.silent,this[Uu]=!0,e&&tc(this),Ju.update.call(this),this[Uu]=!1,oc.call(this,t),rc.call(this,t)))},r.showLoading=function(t,e){var n;this._disposed?dc(this.id):(Wu(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),bc[t]?(e=bc[t](this._api,e),n=this._zr,this._loadingFX=e,n.add(e)):I&&console.warn("Loading effects "+t+" not exists."))},r.hideLoading=function(){this._disposed?dc(this.id):(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},r.makeActionFromEvent=function(t){var e=C({},t);return e.type=mc[t.type],e},r.dispatchAction=function(t,e){this._disposed?dc(this.id):(Wu(e)||(e={silent:!!e}),gc[t.type]&&this._model&&(this[Uu]?this._pendingActions.push(t):(ic.call(this,t,e.silent),e.flush?this._zr.flush(!0):!1!==e.flush&&y.browser.weChat&&this._throttledZrFlush(),oc.call(this,e.silent),rc.call(this,e.silent))))},r.appendData=function(t){var e;this._disposed?dc(this.id):(e=t.seriesIndex,e=this.getModel().getSeriesByIndex(e),I&&Vu(t.data&&e),e.appendData(t),this._scheduler.unfinished=!0)},r.on=Zu("on",!1),r.off=Zu("off",!1),r.one=Zu("one",!1);var cc=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function dc(t){I&&console.warn("Instance "+t+" has been disposed")}function fc(t,e){var n=t.get("z"),i=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=i)&&(t.zlevel=i)})}function pc(){this.eventInfo}r._initEvents=function(){x(cc,function(s){function t(t){var e,n,i,o=this.getModel(),r=t.target,a="globalout"===s;a?e={}:r&&null!=r.dataIndex?e=(i=r.dataModel||o.getSeriesByIndex(r.seriesIndex))&&i.getDataParams(r.dataIndex,r.dataType,r)||{}:r&&r.eventData&&(e=C({},r.eventData)),e&&(i=e.componentType,n=e.componentIndex,"markLine"!==i&&"markPoint"!==i&&"markArea"!==i||(i="series",n=e.seriesIndex),i=(o=i&&null!=n&&o.getComponent(i,n))&&this["series"===o.mainType?"_chartsMap":"_componentsMap"][o.__viewId],!I||a||o&&i||console.warn("model or view can not be found by params"),e.event=t,e.type=s,this._ecEventProcessor.eventInfo={targetEl:r,packedEvent:e,model:o,view:i},this.trigger(s,e))}t.zrEventfulCallAtLast=!0,this._zr.on(s,t,this)},this),x(mc,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},r.isDisposed=function(){return this._disposed},r.clear=function(){this._disposed?dc(this.id):this.setOption({series:[]},!0)},r.dispose=function(){var e,n;this._disposed?dc(this.id):(this._disposed=!0,zo(this.getDom(),Tc,""),e=this._api,n=this._model,x(this._componentsViews,function(t){t.dispose(n,e)}),x(this._chartsViews,function(t){t.dispose(n,e)}),this._zr.dispose(),delete Sc[this.id])},i($u,Ht),pc.prototype={constructor:pc,normalizeQuery:function(t){var e,a,s,l={},h={},u={};return k(t)?(e=Xu(t),l.mainType=e.main||null,l.subType=e.sub||null):(a=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1},D(t,function(t,e){for(var n=!1,i=0;i<a.length;i++){var o=a[i],r=e.lastIndexOf(o);0<r&&r===e.length-o.length&&"data"!==(r=e.slice(0,r))&&(l.mainType=r,l[o.toLowerCase()]=t,n=!0)}s.hasOwnProperty(e)&&(h[e]=t,n=!0),n||(u[e]=t)})),{cptQuery:l,dataQuery:h,otherQuery:u}},filter:function(t,e,n){var i,o,r,a,s,l=this.eventInfo;return!l||(i=l.targetEl,o=l.packedEvent,r=l.model,l=l.view,!r)||!l||(a=e.cptQuery,s=e.dataQuery,h(a,r,"mainType")&&h(a,r,"subType")&&h(a,r,"index","componentIndex")&&h(a,r,"name")&&h(a,r,"id")&&h(s,o,"name")&&h(s,o,"dataIndex")&&h(s,o,"dataType")&&(!l.filterForExposedEvent||l.filterForExposedEvent(t,e.otherQuery,i,o)));function h(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},afterTrigger:function(){this.eventInfo=null}};var gc={},mc={},yc=[],vc=[],_c=[],xc=[],wc={},bc={},Sc={},Mc={},Cc=+new Date,Ic=+new Date,Tc="_echarts_instance_";function Dc(t){Mc[t]=!1}Sa=Dc;function kc(t){return Sc[e=Tc,(t=t).getAttribute?t.getAttribute(e):t[e]];var e}function Ac(t,e){wc[t]=e}function Pc(t){vc.push(t)}function Oc(t,e){Bc(yc,t,e,1e3)}function Lc(t,e,n){"function"==typeof e&&(n=e,e="");var i=Wu(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,Vu(ju.test(i)&&ju.test(e)),gc[i]||(gc[i]={action:n,actionInfo:t}),mc[e]=i}function Ec(t,e){Bc(xc,t,e,1e3,"layout")}function zc(t,e){Bc(xc,t,e,3e3,"visual")}function Bc(t,e,n,i,o){if((Gu(e)||Wu(e))&&(n=e,e=i),I){if(isNaN(e)||null==e)throw new Error("Illegal priority");x(t,function(t){Vu(t.__raw!==n)})}i=vu.wrapStageHandler(n,o);i.__prio=e,i.__raw=n,t.push(i)}function Rc(t,e){bc[t]=e}function Nc(t){return c.extend(t)}function Fc(t){return eu.extend(t)}zc(2e3,u),Pc(xh),Oc(900,function(t){var i=P();t.eachSeries(function(t){var e,n=t.get("stack");n&&(n=i.get(n)||i.set(n,[]),(t={stackResultDimension:(e=t.getData()).getCalculationInfo("stackResultDimension"),stackedOverDimension:e.getCalculationInfo("stackedOverDimension"),stackedDimension:e.getCalculationInfo("stackedDimension"),stackedByDimension:e.getCalculationInfo("stackedByDimension"),isStackedByIndex:e.getCalculationInfo("isStackedByIndex"),data:e,seriesModel:t}).stackedDimension)&&(t.isStackedByIndex||t.stackedByDimension)&&(n.length&&e.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(t))}),i.each(wh)}),Rc("default",function(i,o){T(o=o||{},{text:"loading",textColor:"#000",fontSize:"12px",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#c23531",spinnerRadius:10,lineWidth:5,zlevel:0});var r,t=new h,a=new O({style:{fill:o.maskColor},zlevel:o.zlevel,z:1e4}),s=(t.add(a),o.fontSize+" sans-serif"),l=new O({style:{fill:"none",text:o.text,font:s,textPosition:"right",textDistance:10,textFill:o.textColor},zlevel:o.zlevel,z:10001});return t.add(l),o.showSpinner&&((r=new Ra({shape:{startAngle:-yu/2,endAngle:-yu/2+.1,r:o.spinnerRadius},style:{stroke:o.color,lineCap:"round",lineWidth:o.lineWidth},zlevel:o.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*yu/2}).start("circularInOut"),r.animateShape(!0).when(1e3,{startAngle:3*yu/2}).delay(300).start("circularInOut"),t.add(r)),t.resize=function(){var t=ri(o.text,s),e=o.showSpinner?o.spinnerRadius:0,t=(i.getWidth()-2*e-(o.showSpinner&&t?10:0)-t)/2-(o.showSpinner?0:t/2),n=i.getHeight()/2;o.showSpinner&&r.setShape({cx:t,cy:n}),l.setShape({x:t-e,y:n-e,width:2*e,height:2*e}),a.setShape({x:0,y:0,width:i.getWidth(),height:i.getHeight()})},t.resize(),t}),Lc({type:"highlight",event:"highlight",update:"highlight"},vt),Lc({type:"downplay",event:"downplay",update:"downplay"},vt),Ac("light",o),Ac("dark",s);function Hc(t){return t}function Vc(t,e,n,i,o){this._old=t,this._new=e,this._oldKeyGetter=n||Hc,this._newKeyGetter=i||Hc,this.context=o}function Gc(t,e,n,i,o){for(var r=0;r<t.length;r++){var a="_ec_"+o[i](t[r],r),s=e[a];null==s?(n.push(a),e[a]=r):(s.length||(e[a]=s=[s]),s.push(r))}}Vc.prototype={constructor:Vc,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t=this._old,e=this._new,n={},i=[],o=[];for(Gc(t,{},i,"_oldKeyGetter",this),Gc(e,n,o,"_newKeyGetter",this),r=0;r<t.length;r++)null!=(s=n[a=i[r]])?((h=s.length)?(1===h&&(n[a]=null),s=s.shift()):n[a]=null,this._update&&this._update(s,r)):this._remove&&this._remove(r);for(var r=0;r<o.length;r++){var a=o[r];if(n.hasOwnProperty(a)){var s=n[a];if(null!=s)if(s.length)for(var l=0,h=s.length;l<h;l++)this._add&&this._add(s[l]);else this._add&&this._add(s)}}}};var Wc=P(["tooltip","label","itemName","itemId","seriesName"]);function Xc(r){var t={},a=t.encode={},s=P(),l=[],h=[],u=t.userOutput={dimensionNames:r.dimensions.slice(),encode:{}},i=(D(r.dimensions,function(t){var e,n,i=r.getDimensionInfo(t),o=i.coordDim;o&&(I&&p(null==Wc.get(o)),e=i.coordDimIndex,Yc(a,o)[e]=t,i.isExtraCoord||(s.set(o,1),"ordinal"!==(n=i.type)&&"time"!==n&&(l[0]=t),Yc(u.encode,o)[e]=i.index),i.defaultTooltip)&&h.push(t),Wc.each(function(t,e){var n=Yc(a,e),e=i.otherDims[e];null!=e&&!1!==e&&(n[e]=i.name)})}),[]),o={},e=(s.each(function(t,e){var n=a[e];o[e]=n[0],i=i.concat(n)}),t.dataDimsOnCoord=i,t.encodeFirstDimNotExtra=o,a.label),e=(e&&e.length&&(l=e.slice()),a.tooltip);return e&&e.length?h=e.slice():h.length||(h=l.slice()),a.defaultedLabel=l,a.defaultedTooltip=h,t}function Yc(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function Uc(t){null!=t&&C(this,t),this.otherDims={}}var qc=A,r="undefined",jc={float:typeof Float64Array==r?Array:Float64Array,int:typeof Int32Array==r?Array:Int32Array,ordinal:Array,number:Array,time:Array},Zc=typeof Uint32Array==r?Array:Uint32Array,Kc=typeof Int32Array==r?Array:Int32Array,$c=typeof Uint16Array==r?Array:Uint16Array;function Qc(t){return 65535<t._rawCount?Zc:$c}var Jc=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],td=["_extent","_approximateExtent","_rawExtent"];function ed(e,n){D(Jc.concat(n.__wrappedMethods||[]),function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e.__wrappedMethods=n.__wrappedMethods,D(td,function(t){e[t]=M(n[t])}),e._calculationInfo=C(n._calculationInfo)}var nd=function(t,e){t=t||["x","y"];for(var n={},i=[],o={},r=0;r<t.length;r++){var a=t[r],s=(k(a)?a=new Uc({name:a}):a instanceof Uc||(a=new Uc(a)),a.name);a.type=a.type||"float",a.coordDim||(a.coordDim=s,a.coordDimIndex=0),a.otherDims=a.otherDims||{},i.push(s),(n[s]=a).index=r,a.createInvertedIndices&&(o[s]=[])}this.dimensions=i,this._dimensionInfos=n,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=Xc(this),this._invertedIndicesMap=o,this._calculationInfo={},this.userOutput=this._dimensionsSummary.userOutput},u=nd.prototype;function id(t,e,n,i,o){var r=jc[e.type],a=i-1,s=e.name,l=t[s][a];if(l&&l.length<n){for(var h=new r(Math.min(o-a*n,n)),u=0;u<l.length;u++)h[u]=l[u];t[s][a]=h}for(var c=i*n;c<o;c+=n)t[s].push(new r(Math.min(o-c,n)))}function od(o){var r=o._invertedIndicesMap;D(r,function(t,e){var n=o._dimensionInfos[e].ordinalMeta;if(n){t=r[e]=new Kc(n.categories.length);for(var i=0;i<t.length;i++)t[i]=-1;for(i=0;i<o._count;i++)t[o.get(e,i)]=i}})}function rd(t,e,n){var i,o,r;return o=null!=e&&(i=t._chunkSize,r=Math.floor(n/i),e=t.dimensions[e],r=t._storage[e][r])&&(o=r[n%i],r=t._dimensionInfos[e].ordinalMeta)&&r.categories.length?r.categories[o]:o}function ad(t){return t}function sd(t){return t<this._count&&0<=t?this._indices[t]:-1}function ld(t,e){var n=t._idList[e];return n=null==(n=null==n?rd(t,t._idDimIdx,e):n)?"e\0\0"+e:n}function hd(t){return t=b(t)?t:[t]}function ud(t,e){for(var n=0;n<e.length;n++)t._dimensionInfos[e[n]]||console.error("Unkown dimension "+e[n])}function cd(t,e){for(var n=t.dimensions,i=new nd(S(n,t.getDimensionInfo,t),t.hostModel),o=(ed(i,t),i._storage={}),r=t._storage,a=0;a<n.length;a++){var s=n[a];r[s]&&(0<=m(e,s)?(o[s]=function(t){for(var e=new Array(t.length),n=0;n<t.length;n++)e[n]=function(t){var e=t.constructor;return e===Array?t.slice():new e(t)}(t[n]);return e}(r[s]),i._rawExtent[s]=dd(),i._extent[s]=null):o[s]=r[s])}return i}function dd(){return[1/0,-1/0]}u.type="list",u.hasItemOption=!0,u.getDimension=function(t){return t="number"!=typeof t&&(isNaN(t)||this._dimensionInfos.hasOwnProperty(t))?t:this.dimensions[t]},u.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},u.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},u.mapDimension=function(t,e){var n=this._dimensionsSummary;return null==e?n.encodeFirstDimNotExtra[t]:(n=n.encode[t],!0===e?(n||[]).slice():n&&n[e])},u.initData=function(t,e,n){var i=Rl.isInstance(t)||J(t);if(i&&(t=new bh(t,this.dimensions.length)),I&&!i&&("function"!=typeof t.getItem||"function"!=typeof t.count))throw new Error("Inavlid data provider.");this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},n||(this.hasItemOption=!1),this.defaultDimValueGetter=kh[this._rawData.getSource().sourceFormat],this._dimValueGetter=n=n||this.defaultDimValueGetter,this._dimValueGetterArrayRows=kh.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},u.getProvider=function(){return this._rawData},u.appendData=function(t){I&&p(!this._indices,"appendData can only be called on raw data.");var e=this._rawData,n=this.count(),t=(e.appendData(t),e.count());e.persistent||(t+=n),this._initDataFromProvider(n,t)},u.appendValues=function(t,e){for(var n=this._chunkSize,i=this._storage,o=this.dimensions,r=o.length,a=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),h=this._chunkCount,u=0;u<r;u++)a[y=o[u]]||(a[y]=dd()),i[y]||(i[y]=[]),id(i,this._dimensionInfos[y],n,h,l),this._chunkCount=i[y].length;for(var c=new Array(r),d=s;d<l;d++){for(var f=d-s,p=Math.floor(d/n),g=d%n,m=0;m<r;m++){var y=o[m],v=this._dimValueGetterArrayRows(t[f]||c,y,f,m),_=(i[y][p][g]=v,a[y]);v<_[0]&&(_[0]=v),v>_[1]&&(_[1]=v)}e&&(this._nameList[d]=e[f])}this._rawCount=this._count=l,this._extent={},od(this)},u._initDataFromProvider=function(t,e){if(!(e<=t)){for(var n,i=this._chunkSize,o=this._rawData,r=this._storage,a=this.dimensions,s=a.length,l=this._dimensionInfos,h=this._nameList,u=this._idList,c=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;p<s;p++){c[M=a[p]]||(c[M]=dd());var g=l[M];0===g.otherDims.itemName&&(n=this._nameDimIdx=p),0===g.otherDims.itemId&&(this._idDimIdx=p),r[M]||(r[M]=[]),id(r,g,i,f,e),this._chunkCount=r[M].length}for(var m=new Array(s),y=t;y<e;y++){for(var v,_,x,m=o.getItem(y,m),w=Math.floor(y/i),b=y%i,S=0;S<s;S++){var M,C=r[M=a[S]][w],I=this._dimValueGetter(m,M,y,S),C=(C[b]=I,c[M]);I<C[0]&&(C[0]=I),I>C[1]&&(C[1]=I)}o.pure||(v=h[y],m&&null==v&&(null!=m.name?h[y]=v=m.name:null!=n&&(_=r[x=a[n]][w])&&(v=_[b],_=l[x].ordinalMeta)&&_.categories.length&&(v=_.categories[v])),null==(x=null==m?null:m.id)&&null!=v&&(d[v]=d[v]||0,0<d[x=v]&&(x+="__ec__"+d[v]),d[v]++),null!=x&&(u[y]=x))}!o.persistent&&o.clean&&o.clean(),this._rawCount=this._count=e,this._extent={},od(this)}},u.count=function(){return this._count},u.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array){o=new e(n);for(var i=0;i<n;i++)o[i]=t[i]}else o=new e(t.buffer,0,n)}else for(var o=new(e=Qc(this))(this.count()),i=0;i<o.length;i++)o[i]=i;return o},u.get=function(t,e){if(!(0<=e&&e<this._count))return NaN;var n=this._storage;if(!n[t])return NaN;e=this.getRawIndex(e);var i=Math.floor(e/this._chunkSize),e=e%this._chunkSize;return n[t][i][e]},u.getByRawIndex=function(t,e){var n;return 0<=e&&e<this._rawCount&&(t=this._storage[t])?(n=Math.floor(e/this._chunkSize),e=e%this._chunkSize,t[n][e]):NaN},u._getFast=function(t,e){var n=Math.floor(e/this._chunkSize),e=e%this._chunkSize;return this._storage[t][n][e]},u.getValues=function(t,e){var n=[];b(t)||(e=t,t=this.dimensions);for(var i=0,o=t.length;i<o;i++)n.push(this.get(t[i],e));return n},u.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this.get(e[n],t)))return!1;return!0},u.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],n=dd();if(!e)return n;var i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(e=this._extent[t])return e.slice();for(var o=(e=n)[0],r=e[1],a=0;a<i;a++){var s=this._getFast(t,this.getRawIndex(a));s<o&&(o=s),r<s&&(r=s)}return this._extent[t]=e=[o,r]},u.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},u.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},u.getCalculationInfo=function(t){return this._calculationInfo[t]},u.setCalculationInfo=function(t,e){qc(t)?C(this._calculationInfo,t):this._calculationInfo[t]=e},u.getSum=function(t){var e=0;if(this._storage[t])for(var n=0,i=this.count();n<i;n++){var o=this.get(t,n);isNaN(o)||(e+=o)}return e},u.getMedian=function(t){var n=[],t=(this.each(t,function(t,e){isNaN(t)||n.push(t)}),[].concat(n).sort(function(t,e){return t-e})),e=this.count();return 0===e?0:e%2==1?t[(e-1)/2]:(t[e/2]+t[e/2-1])/2},u.rawIndexOf=function(t,e){t=t&&this._invertedIndicesMap[t];if(I&&!t)throw new Error("Do not supported yet");t=t[e];return null==t||isNaN(t)?-1:t},u.indexOfName=function(t){for(var e=0,n=this.count();e<n;e++)if(this.getName(e)===t)return e;return-1},u.indexOfRawIndex=function(t){if(!(t>=this._rawCount||t<0)){if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,o=this._count-1;i<=o;){var r=(i+o)/2|0;if(e[r]<t)i=1+r;else{if(!(e[r]>t))return r;o=r-1}}}return-1},u.indicesOfNearest=function(t,e,n){var i=[];if(this._storage[t]){null==n&&(n=1/0);for(var o=1/0,r=-1,a=0,s=0,l=this.count();s<l;s++){var h=e-this.get(t,s),u=Math.abs(h);u<=n&&((u<o||u===o&&0<=h&&r<0)&&(o=u,r=h,a=0),h===r)&&(i[a++]=s)}i.length=a}return i},u.getRawIndex=ad,u.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],n=0;n<this.dimensions.length;n++){var i=this.dimensions[n];e.push(this.get(i,t))}return e},u.getName=function(t){t=this.getRawIndex(t);return this._nameList[t]||rd(this,this._nameDimIdx,t)||""},u.getId=function(t){return ld(this,this.getRawIndex(t))},u.each=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=S(hd(t),this.getDimension,this),I&&ud(this,t);for(var o=t.length,r=0;r<this.count();r++)switch(o){case 0:e.call(n,r);break;case 1:e.call(n,this.get(t[0],r),r);break;case 2:e.call(n,this.get(t[0],r),this.get(t[1],r),r);break;default:for(var a=0,s=[];a<o;a++)s[a]=this.get(t[a],r);s[a]=r,e.apply(n,s)}}},u.filterSelf=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=S(hd(t),this.getDimension,this),I&&ud(this,t);for(var o=this.count(),r=new(Qc(this))(o),a=[],s=t.length,l=0,h=t[0],u=0;u<o;u++){var c=this.getRawIndex(u);if(0===s)f=e.call(n,u);else if(1===s)var d=this._getFast(h,c),f=e.call(n,d,u);else{for(var p=0;p<s;p++)a[p]=this._getFast(h,c);a[p]=u,f=e.apply(n,a)}f&&(r[l++]=c)}return l<o&&(this._indices=r),this._count=l,this._extent={},this.getRawIndex=this._indices?sd:ad,this}},u.selectRange=function(t){if(this._count){var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);I&&ud(this,n);var i=n.length;if(i){var o=this.count(),r=new(Qc(this))(o),a=0,s=n[0],l=t[s][0],h=t[s][1],u=!1;if(!this._indices){var c=0;if(1===i){for(var d=this._storage[n[0]],f=0;f<this._chunkCount;f++)for(var p=d[f],g=Math.min(this._count-f*this._chunkSize,this._chunkSize),m=0;m<g;m++)(l<=(w=p[m])&&w<=h||isNaN(w))&&(r[a++]=c),c++;u=!0}else if(2===i){for(var d=this._storage[s],y=this._storage[n[1]],v=t[n[1]][0],_=t[n[1]][1],f=0;f<this._chunkCount;f++)for(var p=d[f],x=y[f],g=Math.min(this._count-f*this._chunkSize,this._chunkSize),m=0;m<g;m++){var w=p[m],b=x[m];(l<=w&&w<=h||isNaN(w))&&(v<=b&&b<=_||isNaN(b))&&(r[a++]=c),c++}u=!0}}if(!u)if(1===i)for(m=0;m<o;m++){var S=this.getRawIndex(m);(l<=(w=this._getFast(s,S))&&w<=h||isNaN(w))&&(r[a++]=S)}else for(m=0;m<o;m++){for(var M=!0,S=this.getRawIndex(m),f=0;f<i;f++){var C=n[f];((w=this._getFast(e,S))<t[C][0]||w>t[C][1])&&(M=!1)}M&&(r[a++]=this.getRawIndex(m))}return a<o&&(this._indices=r),this._count=a,this._extent={},this.getRawIndex=this._indices?sd:ad,this}}},u.mapArray=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]);var o=[];return this.each(t,function(){o.push(e&&e.apply(this,arguments))},n=n||i||this),o},u.map=function(t,e,n,i){n=n||i||this,t=S(hd(t),this.getDimension,this),I&&ud(this,t);for(var i=cd(this,t),o=(i._indices=this._indices,i.getRawIndex=i._indices?sd:ad,i._storage),r=[],a=this._chunkSize,s=t.length,l=this.count(),h=[],u=i._rawExtent,c=0;c<l;c++){for(var d=0;d<s;d++)h[d]=this.get(t[d],c);h[s]=c;var f=e&&e.apply(n,h);if(null!=f){"object"!=typeof f&&(r[0]=f,f=r);for(var p=this.getRawIndex(c),g=Math.floor(p/a),m=p%a,y=0;y<f.length;y++){var v=t[y],_=f[y],x=u[v],v=o[v];v&&(v[g][m]=_),_<x[0]&&(x[0]=_),_>x[1]&&(x[1]=_)}}}return i},u.downSample=function(t,e,n,i){for(var o=cd(this,[t]),r=o._storage,a=[],s=Math.floor(1/e),l=r[t],h=this.count(),u=this._chunkSize,c=o._rawExtent[t],d=new(Qc(this))(h),f=0,p=0;p<h;p+=s){h-p<s&&(a.length=s=h-p);for(var g=0;g<s;g++){var m=this.getRawIndex(p+g),y=Math.floor(m/u);a[g]=l[y][m%u]}var v=n(a),_=this.getRawIndex(Math.min(p+i(a,v)||0,h-1));(l[Math.floor(_/u)][_%u]=v)<c[0]&&(c[0]=v),v>c[1]&&(c[1]=v),d[f++]=_}return o._count=f,o._indices=d,o.getRawIndex=sd,o},u.getItemModel=function(t){var e=this.hostModel;return new _(this.getRawDataItem(t),e,e&&e.ecModel)},u.diff=function(e){var n=this;return new Vc(e?e.getIndices():[],this.getIndices(),function(t){return ld(e,t)},function(t){return ld(n,t)})},u.getVisual=function(t){var e=this._visual;return e&&e[t]},u.setVisual=function(t,e){if(qc(t))for(var n in t)t.hasOwnProperty(n)&&this.setVisual(n,t[n]);else this._visual=this._visual||{},this._visual[t]=e},u.setLayout=function(t,e){if(qc(t))for(var n in t)t.hasOwnProperty(n)&&this.setLayout(n,t[n]);else this._layout[t]=e},u.getLayout=function(t){return this._layout[t]},u.getItemLayout=function(t){return this._itemLayouts[t]},u.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?C(this._itemLayouts[t]||{},e):e},u.clearItemLayouts=function(){this._itemLayouts.length=0},u.getItemVisual=function(t,e,n){t=this._itemVisuals[t],t=t&&t[e];return null!=t||n?t:this.getVisual(e)},u.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{},o=this.hasItemVisual;if(this._itemVisuals[t]=i,qc(e))for(var r in e)e.hasOwnProperty(r)&&(i[r]=e[r],o[r]=!0);else i[e]=n,o[e]=!0},u.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};function fd(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType}function pd(t,e,n){Rl.isInstance(e)||(e=Rl.seriesDataToSource(e)),n=n||{},t=(t||[]).slice();for(var i,o,r,a,s=(n.dimsDef||[]).slice(),l=P(),h=P(),u=[],c=(i=e,o=t,r=n.dimCount,a=Math.max(i.dimensionsDetectCount||1,o.length,s.length,r||0),D(o,function(t){t=t.dimsDef;t&&(a=Math.max(a,t.length))}),a),d=0;d<c;d++){var f=s[d]=C({},A(s[d])?s[d]:{name:s[d]}),p=f.name,g=u[d]=new Uc;null!=p&&null==l.get(p)&&(g.name=g.displayName=p,l.set(p,d)),null!=f.type&&(g.type=f.type),null!=f.displayName&&(g.displayName=f.displayName)}var m=n.encodeDef,y=((m=P(m=!m&&n.encodeDefaulter?n.encodeDefaulter(e,c):m)).each(function(t,n){var i;1===(t=So(t).slice()).length&&!k(t[0])&&t[0]<0?m.set(n,!1):(i=m.set(n,[]),D(t,function(t,e){null!=(t=k(t)?l.get(t):t)&&t<c&&(i[e]=t,v(u[t],n,e))}))}),0);function v(t,e,n){null!=Wc.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,h.set(e,!0))}D(t,function(n,t){k(n)?(r=n,n={}):(r=n.name,e=n.ordinalMeta,n.ordinalMeta=null,(n=M(n)).ordinalMeta=e,i=n.dimsDef,o=n.otherDims,n.name=n.coordDim=n.coordDimIndex=n.dimsDef=n.otherDims=null);var i,o,r,e,a=m.get(r);if(!1!==a){if(!(a=So(a)).length)for(var s=0;s<(i&&i.length||1);s++){for(;y<u.length&&null!=u[y].coordDim;)y++;y<u.length&&a.push(y++)}D(a,function(t,e){t=u[t];v(T(t,n),r,e),null==t.name&&i&&(A(e=i[e])||(e={name:e}),t.name=t.displayName=e.name,t.defaultTooltip=e.defaultTooltip),o&&T(t.otherDims,o)})}});for(var _=n.generateCoord,x=null!=(w=n.generateCoordCount),w=_?w||1:0,b=_||"value",S=0;S<c;S++)null==(g=u[S]=u[S]||new Uc).coordDim&&(g.coordDim=gd(b,h,x),g.coordDimIndex=0,(!_||w<=0)&&(g.isExtraCoord=!0),w--),null==g.name&&(g.name=gd(g.coordDim,l)),null!=g.type||Yl(e,S,g.name)!==Nl.Must&&(!g.isExtraCoord||null==g.otherDims.itemName&&null==g.otherDims.seriesName)||(g.type="ordinal");return u}function gd(t,e,n){if(n||null!=e.get(t)){for(var i=0;null!=e.get(t+i);)i++;t+=i}return e.set(t,!0),t}function md(t,e){return pd((e=e||{}).coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,encodeDefaulter:e.encodeDefaulter,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})}function yd(t){this.coordSysName=t,this.coordSysDims=[],this.axisMap=P(),this.categoryAxisMap=P(),this.firstCategoryDimIndex=null}u.setItemGraphicEl=function(t,e){var n=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=n&&n.seriesIndex,"group"===e.type)&&e.traverse(fd,e),this._graphicEls[t]=e},u.getItemGraphicEl=function(t){return this._graphicEls[t]},u.eachItemGraphicEl=function(n,i){D(this._graphicEls,function(t,e){t&&n&&n.call(i,t,e)})},u.cloneShallow=function(t){var e;return t||(e=S(this.dimensions,this.getDimensionInfo,this),t=new nd(e,this.hostModel)),t._storage=this._storage,ed(t,this),this._indices?(e=this._indices.constructor,t._indices=new e(this._indices)):t._indices=null,t.getRawIndex=t._indices?sd:ad,t},u.wrapMethod=function(t,e){var n=this[t];"function"==typeof n&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(ct(arguments)))})},u.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],u.CHANGABLE_METHODS=["filterSelf","selectRange"];var vd={cartesian2d:function(t,e,n,i){var o=t.getReferringComponents("xAxis")[0],r=t.getReferringComponents("yAxis")[0];if(I){if(!o)throw new Error('xAxis "'+lt(t.get("xAxisIndex"),t.get("xAxisId"),0)+'" not found');if(!r)throw new Error('yAxis "'+lt(t.get("xAxisIndex"),t.get("yAxisId"),0)+'" not found')}e.coordSysDims=["x","y"],n.set("x",o),n.set("y",r),_d(o)&&(i.set("x",o),e.firstCategoryDimIndex=0),_d(r)&&(i.set("y",r),e.firstCategoryDimIndex,e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){t=t.getReferringComponents("singleAxis")[0];if(I&&!t)throw new Error("singleAxis should be specified.");e.coordSysDims=["single"],n.set("single",t),_d(t)&&(i.set("single",t),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var t=t.getReferringComponents("polar")[0],o=t.findAxisModel("radiusAxis"),t=t.findAxisModel("angleAxis");if(I){if(!t)throw new Error("angleAxis option not found");if(!o)throw new Error("radiusAxis option not found")}e.coordSysDims=["radius","angle"],n.set("radius",o),n.set("angle",t),_d(o)&&(i.set("radius",o),e.firstCategoryDimIndex=0),_d(t)&&(i.set("angle",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,i,o,r){var a=t.ecModel,t=a.getComponent("parallel",t.get("parallelIndex")),s=i.coordSysDims=t.dimensions.slice();D(t.parallelAxisIndex,function(t,e){var t=a.getComponent("parallelAxis",t),n=s[e];o.set(n,t),_d(t)&&null==i.firstCategoryDimIndex&&(r.set(n,t),i.firstCategoryDimIndex=e)})}};function _d(t){return"category"===t.get("type")}function xd(t,n,e){var i,o,r,a,s,l,h=(e=e||{}).byIndex,u=e.stackedCoordDimension,c=!(!t||!t.get("stack"));return D(n,function(t,e){k(t)&&(n[e]=t={name:t}),c&&!t.isExtraCoord&&(h||i||!t.ordinalMeta||(i=t),o||"ordinal"===t.type||"time"===t.type||u&&u!==t.coordDim||(o=t))}),!o||h||i||(h=!0),o&&(r="__\0ecstackresult",a="__\0ecstackedover",i&&(i.createInvertedIndices=!0),s=o.coordDim,e=o.type,l=0,D(n,function(t){t.coordDim===s&&l++}),n.push({name:r,coordDim:s,coordDimIndex:l,type:e,isExtraCoord:!0,isCalculationCoord:!0}),l++,n.push({name:a,coordDim:a,coordDimIndex:l,type:e,isExtraCoord:!0,isCalculationCoord:!0})),{stackedDimension:o&&o.name,stackedByDimension:i&&i.name,isStackedByIndex:h,stackedOverDimension:a,stackResultDimension:r}}function wd(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function bd(t,e){return wd(t,e)?t.getCalculationInfo("stackResultDimension"):e}function Sd(t,e,n){n=n||{},Rl.isInstance(t)||(t=Rl.seriesDataToSource(t));var o,i,r=e.get("coordinateSystem"),r=th.get(r),a=function(t){var e=t.get("coordinateSystem"),n=new yd(e);if(e=vd[e])return e(t,n,n.axisMap,n.categoryAxisMap),n}(e),r=md(t,{coordDimensions:s=(s=a?S(a.coordSysDims,function(t){var e={name:t},t=a.axisMap.get(t);return t&&(t=t.get("type"),e.type="category"===(t=t)?"ordinal":"time"===t?"time":"float"),e}):s)||r&&(r.getDimensionsInfo?r.getDimensionsInfo():r.dimensions.slice())||["x","y"],generateCoord:n.generateCoord,encodeDefaulter:n.useEncodeDefaulter?l(Wl,s,e):null}),n=(a&&D(r,function(t,e){var n=t.coordDim,n=a.categoryAxisMap.get(n);n&&(null==o&&(o=e),t.ordinalMeta=n.getOrdinalMeta()),null!=t.otherDims.itemName&&(i=!0)}),i||null==o||(r[o].otherDims.itemName=0),xd(e,r)),s=new nd(r,e),r=(s.setCalculationInfo(n),null!=o&&function(t){if(t.sourceFormat===kl)return null!=(t=function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(t.data||[]))&&!b(Io(t))}(t)?function(t,e,n,i){return i===o?n:this.defaultDimValueGetter(t,e,n,i)}:null);return s.hasItemOption=!1,s.initData(t,null,r),s}function w(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function Md(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}w.prototype.parse=function(t){return t},w.prototype.getSetting=function(t){return this._setting[t]},w.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},w.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},w.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},w.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},w.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},w.prototype.getExtent=function(){return this._extent.slice()},w.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},w.prototype.isBlank=function(){return this._isBlank},w.prototype.setBlank=function(t){this._isBlank=t},w.prototype.getLabel=null,Ho(w),Yo(w,{registerWhenExtend:!0}),Md.createByAxisModel=function(t){var t=t.option,e=t.data,e=e&&S(e,Id);return new Md({categories:e,needCollect:!e,deduplication:!1!==t.dedplication})};o=Md.prototype;function Cd(t){return t._map||(t._map=P(t.categories))}function Id(t){return A(t)&&null!=t.value?t.value:t+""}o.getOrdinal=function(t){return Cd(this).get(t)},o.parseAndCollect=function(t){var e,n,i=this._needCollect;return"string"==typeof t||i?(i&&!this._deduplication?(n=this.categories.length,this.categories[n]=t):null==(n=(e=Cd(this)).get(t))&&(i?(n=this.categories.length,this.categories[n]=t,e.set(t,n)):n=NaN),n):t};var Td=w.prototype,Dd=w.extend({type:"ordinal",init:function(t,e){t&&!b(t)||(t=new Md({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),Td.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return Td.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(Td.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push(n),n++;return t},getLabel:function(t){if(!this.isBlank())return this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:vt,niceExtent:vt}),kd=(Dd.create=function(){return new Dd},Ws);function Ad(t){return Xs(t)+2}function Pd(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function Od(t,e){isFinite(t[0])||(t[0]=e[0]),isFinite(t[1])||(t[1]=e[1]),Pd(t,0,e),Pd(t,1,e),t[0]>t[1]&&(t[0]=t[1])}var Ld=Ws,Ed=w.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),Ed.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Ad(t)},getTicks:function(t){var e=this._interval,n=this._extent,i=this._niceExtent,o=this._intervalPrecision,r=[];if(e){n[0]<i[0]&&r.push(t?Ld(i[0]-e,o):n[0]);for(var a=i[0];a<=i[1]&&(r.push(a),(a=Ld(a+e,o))!==r[r.length-1]);)if(1e4<r.length)return[];var s=r.length?r[r.length-1]:i[1];n[1]>s&&r.push(t?Ld(s+e,o):n[1])}return r},getMinorTicks:function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),o=1;o<e.length;o++){for(var r=e[o],a=e[o-1],s=0,l=[],h=(r-a)/t;s<t-1;){var u=Ws(a+(s+1)*h);u>i[0]&&u<i[1]&&l.push(u),s++}n.push(l)}return n},getLabel:function(t,e){return null==t?"":(null==(e=e&&e.precision)?e=Xs(t)||0:"auto"===e&&(e=this._intervalPrecision),Js(t=Ld(t,e,!0)))},niceTicks:function(t,e,n){t=t||5;var i,o=this._extent,r=o[1]-o[0];isFinite(r)&&(r<0&&o.reverse(),r=t,t=e,e=n,n={},i=(o=o)[1]-o[0],i=n.interval=Qs(i/r,!0),null!=t&&i<t&&(i=n.interval=t),null!=e&&e<i&&(i=n.interval=e),r=n.intervalPrecision=Ad(i),Od(n.niceTickExtent=[kd(Math.ceil(o[0]/i)*i,r),kd(Math.floor(o[1]/i)*i,r)],o),t=n,this._intervalPrecision=t.intervalPrecision,this._interval=t.interval,this._niceExtent=t.niceTickExtent)},niceExtent:function(t){var e=this._extent,n=(e[0]===e[1]&&(0!==e[0]?(n=e[0],t.fixMax||(e[1]+=n/2),e[0]-=n/2):e[1]=1),e[1]-e[0]),n=(isFinite(n)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval),this._interval);t.fixMin||(e[0]=Ld(Math.floor(e[0]/n)*n)),t.fixMax||(e[1]=Ld(Math.ceil(e[1]/n)*n))}}),zd=(Ed.create=function(){return new Ed},"__ec_stack_");"undefined"==typeof Float32Array&&Array;function Bd(t){return t.get("stack")||zd+t.seriesIndex}function Rd(t){return t.dim+t.index}function Nd(t){var a,d,l=function(t){var e,l={},n=(D(t,function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type)for(var n=t.getData(),i=e.dim+"_"+e.index,o=n.mapDimension(e.dim),r=0,a=n.count();r<a;++r){var s=n.get(o,r);l[i]?l[i].push(s):l[i]=[s]}}),[]);for(e in l)if(l.hasOwnProperty(e)){var i=l[e];if(i){i.sort(function(t,e){return t-e});for(var o=null,r=1;r<i.length;++r){var a=i[r]-i[r-1];0<a&&(o=null===o?a:Math.min(o,a))}n[e]=o}}return n}(t),h=[];return D(t,function(t){var e,n=t.coordinateSystem.getBaseAxis(),i=n.getExtent(),o=(e="category"===n.type?n.getBandWidth():"value"===n.type||"time"===n.type?(e=n.dim+"_"+n.index,e=l[e],o=Math.abs(i[1]-i[0]),r=n.scale.getExtent(),r=Math.abs(r[1]-r[0]),e?o/r*e:o):(r=t.getData(),Math.abs(i[1]-i[0])/r.count()),v(t.get("barWidth"),e)),i=v(t.get("barMaxWidth"),e),r=v(t.get("barMinWidth")||1,e),a=t.get("barGap"),s=t.get("barCategoryGap");h.push({bandWidth:e,barWidth:o,barMaxWidth:i,barMinWidth:r,barGap:a,barCategoryGap:s,axisKey:Rd(n),stackId:Bd(t)})}),a={},D(h,function(t,e){var n=t.axisKey,i=t.bandWidth,i=a[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},o=i.stacks,n=(a[n]=i,t.stackId),r=(o[n]||i.autoWidthCount++,o[n]=o[n]||{width:0,maxWidth:0},t.barWidth),r=(r&&!o[n].width&&(o[n].width=r,r=Math.min(i.remainedWidth,r),i.remainedWidth-=r),t.barMaxWidth),r=(r&&(o[n].maxWidth=r),t.barMinWidth),o=(r&&(o[n].minWidth=r),t.barGap),n=(null!=o&&(i.gap=o),t.barCategoryGap);null!=n&&(i.categoryGap=n)}),d={},D(a,function(t,n){d[n]={};var i,e=t.stacks,o=t.bandWidth,r=v(t.categoryGap,o),a=v(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,h=(s-r)/(l+(l-1)*a),h=Math.max(h,0),u=(D(e,function(t){var e,n=t.maxWidth,i=t.minWidth;t.width?(e=t.width,n&&(e=Math.min(e,n)),i&&(e=Math.max(e,i)),t.width=e,s-=e+a*e,l--):(e=h,n&&n<e&&(e=Math.min(n,s)),(e=i&&e<i?i:e)!==h&&(t.width=e,s-=e+a*e,l--))}),h=(s-r)/(l+(l-1)*a),h=Math.max(h,0),0),c=(D(e,function(t,e){t.width||(t.width=h),u+=(i=t).width*(1+a)}),i&&(u-=i.width*a),-u/2);D(e,function(t,e){d[n][e]=d[n][e]||{bandWidth:o,offset:c,width:t.width},c+=t.width*(1+a)})}),d}function Fd(t,e,n){if(t&&e)return null!=(t=t[Rd(e)])&&null!=n?t[Bd(n)]:t}function Hd(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function Vd(t){return t.pipelineContext&&t.pipelineContext.large}nu();var Gd=Ed.prototype,Wd=Math.ceil,Xd=Math.floor,s=36e5,L=864e5,Yd=Ed.extend({type:"time",getLabel:function(t){var e=this._stepLvl,t=new Date(t);return ul(e[0],t,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent,n=(e[0]===e[1]&&(e[0]-=L,e[1]+=L),e[1]===-1/0&&e[0]===1/0&&(n=new Date,e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-L),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval),this._interval);t.fixMin||(e[0]=Ws(Xd(e[0]/n)*n)),t.fixMax||(e[1]=Ws(Wd(e[1]/n)*n))},niceTicks:function(t,e,n){var i=this._extent,o=i[1]-i[0],r=o/(t=t||10),e=(null!=e&&r<e&&(r=e),Ud.length),n=function(t,e,n,i){for(;n<i;){var o=n+i>>>1;t[o][1]<e?n=1+o:i=o}return n}(Ud,r=null!=n&&n<r?n:r,0,e),r=Ud[Math.min(n,e-1)],n=r[1],e=("year"===r[0]&&(n*=Qs(o/n/t,!0)),this.getSetting("useUTC")?0:60*new Date(+i[0]||+i[1]).getTimezoneOffset()*1e3),o=[Math.round(Wd((i[0]-e)/n)*n+e),Math.round(Xd((i[1]-e)/n)*n+e)];Od(o,i),this._stepLvl=r,this._interval=n,this._niceExtent=o},parse:function(t){return+Zs(t)}}),Ud=(D(["contain","normalize"],function(e){Yd.prototype[e]=function(t){return Gd[e].call(this,this.parse(t))}}),[["hh:mm:ss",1e3],["hh:mm:ss",5e3],["hh:mm:ss",1e4],["hh:mm:ss",15e3],["hh:mm:ss",3e4],["hh:mm\nMM-dd",6e4],["hh:mm\nMM-dd",3e5],["hh:mm\nMM-dd",6e5],["hh:mm\nMM-dd",9e5],["hh:mm\nMM-dd",18e5],["hh:mm\nMM-dd",s],["hh:mm\nMM-dd",72e5],["hh:mm\nMM-dd",6*s],["hh:mm\nMM-dd",432e5],["MM-dd\nyyyy",L],["MM-dd\nyyyy",2*L],["MM-dd\nyyyy",3*L],["MM-dd\nyyyy",4*L],["MM-dd\nyyyy",5*L],["MM-dd\nyyyy",6*L],["week",7*L],["MM-dd\nyyyy",864e6],["week",14*L],["week",21*L],["month",31*L],["week",42*L],["month",62*L],["week",70*L],["quarter",95*L],["month",31*L*4],["month",13392e6],["half-year",16416e6],["month",31*L*8],["month",26784e6],["year",380*L]]),qd=(Yd.create=function(t){return new Yd({useUTC:t.ecModel.get("useUTC")})},w.prototype),jd=Ed.prototype,Zd=Xs,Kd=Ws,$d=Math.floor,Qd=Math.ceil,Jd=Math.pow,tf=Math.log,ef=w.extend({type:"log",base:10,$constructor:function(){w.apply(this,arguments),this._originalScale=new Ed},getTicks:function(t){var n=this._originalScale,i=this._extent,o=n.getExtent();return S(jd.getTicks.call(this,t),function(t){var e=Ws(Jd(this.base,t)),e=t===i[0]&&n.__fixMin?nf(e,o[0]):e;return t===i[1]&&n.__fixMax?nf(e,o[1]):e},this)},getMinorTicks:jd.getMinorTicks,getLabel:jd.getLabel,scale:function(t){return t=qd.scale.call(this,t),Jd(this.base,t)},setExtent:function(t,e){var n=this.base;t=tf(t)/tf(n),e=tf(e)/tf(n),jd.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=qd.getExtent.call(this),t=(e[0]=Jd(t,e[0]),e[1]=Jd(t,e[1]),this._originalScale),n=t.getExtent();return t.__fixMin&&(e[0]=nf(e[0],n[0])),t.__fixMax&&(e[1]=nf(e[1],n[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=tf(t[0])/tf(e),t[1]=tf(t[1])/tf(e),qd.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n==1/0||n<=0)){var i=Ks(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&0<Math.abs(i);)i*=10;t=[Ws(Qd(e[0]/i)*i),Ws($d(e[1]/i)*i)];this._interval=i,this._niceExtent=t}},niceExtent:function(t){jd.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});function nf(t,e){return Kd(t,Zd(e))}function of(t,e){var n,i,o,r,a,s,l=t.type,h=e.getMin(),u=e.getMax(),c=t.getExtent(),d=("ordinal"===l?p=e.getCategories().length:("boolean"==typeof(i=b(i=e.get("boundaryGap"))?i:[i||0,i||0])[0]&&(I&&console.warn('Boolean type for boundaryGap is only allowed for ordinal axis. Please use string in percentage instead, e.g., "20%". Currently, boundaryGap is set to be 0.'),i=[0,0]),i[0]=v(i[0],1),i[1]=v(i[1],1),a=c[1]-c[0]||Math.abs(c[0])),"dataMin"===h?h=c[0]:"function"==typeof h&&(h=h({min:c[0],max:c[1]})),"dataMax"===u?u=c[1]:"function"==typeof u&&(u=u({min:c[0],max:c[1]})),null!=h),f=null!=u,p=(null==h&&(h="ordinal"===l?p?0:NaN:c[0]-i[0]*a),null==u&&(u="ordinal"===l?p?p-1:NaN:c[1]+i[1]*a),null!=h&&isFinite(h)||(h=NaN),null!=u&&isFinite(u)||(u=NaN),t.setBlank(st(h)||st(u)||"ordinal"===l&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(h=0<h&&0<u&&!d?0:h)<0&&u<0&&!f&&(u=0),e.ecModel);return p&&"time"===l&&(c="bar",s=[],p.eachSeriesByType(c,function(t){Hd(t)&&!Vd(t)&&s.push(t)}),D(i=s,function(t){n|=t.getBaseAxis()===e.axis}),n)&&(a=Nd(i),t=h,l=u,c=a,a=(a=(i=e).axis.getExtent())[1]-a[0],void 0!==(c=Fd(c,i.axis))&&(o=1/0,D(c,function(t){o=Math.min(t.offset,o)}),r=-1/0,D(c,function(t){r=Math.max(t.offset+t.width,r)}),o=Math.abs(o),r=Math.abs(r),l+=r/(i=o+r)*(a=(c=l-t)/(1-(o+r)/a)-c),t-=o/i*a),h=(c={min:t,max:l}).min,u=c.max),{extent:[h,u],fixMin:d,fixMax:f}}function rf(t,e){var n=of(t,e),i=n.extent,o=e.get("splitNumber"),r=("log"===t.type&&(t.base=e.get("logBase")),t.type),i=(t.setExtent(i[0],i[1]),t.niceExtent({splitNumber:o,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:"interval"===r||"time"===r?e.get("minInterval"):null,maxInterval:"interval"===r||"time"===r?e.get("maxInterval"):null}),e.get("interval"));null!=i&&t.setInterval&&t.setInterval(i)}function af(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Dd(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new Ed;default:return(w.getClass(e)||Ed).create(t)}}function sf(n){var e,i=n.getLabelModel().get("formatter"),o="category"===n.type?n.scale.getExtent()[0]:null;return"string"==typeof i?(e=i,i=function(t){return t=n.scale.getLabel(t),e.replace("{value}",null!=t?t:"")}):"function"==typeof i?function(t,e){return null!=o&&(e=t-o),i(lf(n,t),e)}:function(t){return n.scale.getLabel(t)}}function lf(t,e){return"category"===t.type?t.scale.getLabel(e):e}function hf(t){var e=t.model,n=t.scale;if(e.get("axisLabel.show")&&!n.isBlank()){var i,o,e="category"===t.type,r=n.getExtent(),a=e?n.count():(i=n.getTicks()).length,s=t.getLabelModel(),l=sf(t),h=1;40<a&&(h=Math.ceil(a/40));for(var u,c,d,f=0;f<a;f+=h){var p=l(i?i[f]:r[0]+f),p=s.getTextRect(p),g=(p=p,u=s.get("rotate")||0,c=d=g=c=void 0,u=u*Math.PI/180,p=p.plain(),c=p.width,g=p.height,d=c*Math.abs(Math.cos(u))+Math.abs(g*Math.sin(u)),c=c*Math.abs(Math.sin(u))+Math.abs(g*Math.cos(u)),new X(p.x,p.y,d,c));o?o.union(g):o=g}return o}}function uf(t){t=t.get("interval");return null==t?"auto":t}function cf(t){return"category"===t.type&&0===uf(t.getLabelModel())}D(["contain","normalize"],function(e){ef.prototype[e]=function(t){return t=tf(t)/tf(this.base),qd[e].call(this,t)}}),ef.create=function(){return new ef};var df={getMin:function(t){var e=this.option,t=t||null==e.rangeStart?e.min:e.rangeStart;return t=this.axis&&null!=t&&"dataMin"!==t&&"function"!=typeof t&&!st(t)?this.axis.scale.parse(t):t},getMax:function(t){var e=this.option,t=t||null==e.rangeEnd?e.max:e.rangeEnd;return t=this.axis&&null!=t&&"dataMax"!==t&&"function"!=typeof t&&!st(t)?this.axis.scale.parse(t):t},getNeedCrossZero:function(){var t=this.option;return null==t.rangeStart&&null==t.rangeEnd&&!t.scale},getCoordSysModel:vt,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}},r=Za({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,o=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+o,i+e),t.lineTo(n-o,i+e),t.closePath()}}),u=Za({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,o=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+o,i),t.lineTo(n,i+e),t.lineTo(n-o,i),t.closePath()}}),o=Za({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,o=e.width/5*3,e=Math.max(o,e.height),o=o/2,r=o*o/(e-o),e=i-e+o+r,a=Math.asin(r/o),s=Math.cos(a)*o,l=Math.sin(a),h=Math.cos(a),u=.6*o,c=.7*o;t.moveTo(n-s,e+r),t.arc(n,e,o,Math.PI-a,2*Math.PI+a),t.bezierCurveTo(n+s-l*u,e+r+h*u,n,i-c,n,i),t.bezierCurveTo(n,i-c,n-s+l*u,e+r+h*u,n-s,e+r),t.closePath()}}),s=Za({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,o=e.x,e=e.y,i=i/3*2;t.moveTo(o,e),t.lineTo(o+i,e+n),t.lineTo(o,e+n/4*3),t.lineTo(o-i,e+n),t.lineTo(o,e),t.closePath()}}),ff={line:function(t,e,n,i,o){o.x1=t,o.y1=e+i/2,o.x2=t+n,o.y2=e+i/2},rect:function(t,e,n,i,o){o.x=t,o.y=e,o.width=n,o.height=i},roundRect:function(t,e,n,i,o){o.x=t,o.y=e,o.width=n,o.height=i,o.r=Math.min(n,i)/4},square:function(t,e,n,i,o){n=Math.min(n,i);o.x=t,o.y=e,o.width=n,o.height=n},circle:function(t,e,n,i,o){o.cx=t+n/2,o.cy=e+i/2,o.r=Math.min(n,i)/2},diamond:function(t,e,n,i,o){o.cx=t+n/2,o.cy=e+i/2,o.width=n,o.height=i},pin:function(t,e,n,i,o){o.x=t+n/2,o.y=e+i/2,o.width=n,o.height=i},arrow:function(t,e,n,i,o){o.x=t+n/2,o.y=e+i/2,o.width=n,o.height=i},triangle:function(t,e,n,i,o){o.cx=t+n/2,o.cy=e+i/2,o.width=n,o.height=i}},pf={},gf=(D({line:Pa,rect:O,roundRect:O,square:O,circle:ma,diamond:u,pin:o,arrow:s,triangle:r},function(t,e){pf[e]=new t}),Za({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var t=hi(t,e,n),i=this.shape;return i&&"pin"===i.symbolType&&"inside"===e.textPosition&&(t.y=n.y+.4*n.height),t},buildPath:function(t,e,n){var i,o=e.symbolType;"none"!==o&&(i=(i=pf[o])||pf[o="rect"],ff[o](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n))}}));function mf(t,e){var n,i;"image"!==this.type&&(n=this.style,(i=this.shape)&&"line"===i.symbolType?n.stroke=t:this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff"):(n.fill&&(n.fill=t),n.stroke&&(n.stroke=t)),this.dirty(!1))}function yf(t,e,n,i,o,r,a){var s=0===t.indexOf("empty");return(a=0===(t=s?t.substr(5,1).toLowerCase()+t.substr(6):t).indexOf("image://")?Qa(t.slice(8),new X(e,n,i,o),a?"center":"cover"):0===t.indexOf("path://")?$a(t.slice(7),{},new X(e,n,i,o),a?"center":"cover"):new gf({shape:{symbolType:t,x:e,y:n,width:i,height:o}})).__isEmptyBrush=s,a.setColor=mf,a.setColor(r),a}var ma=Object.freeze({__proto__:null,completeDimensions:pd,createDimensions:md,createList:function(t){return Sd(t.getSource(),t)},createScale:function(t,e){var n=e;return _.isInstance(e)||i(n=new _(e),df),(e=af(n)).setExtent(t[0],t[1]),rf(e,n),e},createSymbol:yf,dataStack:{isDimensionStacked:wd,enableDataStack:xd,getStackedDimension:bd},getLayoutRect:vl,mixinAxisModelCommonMethods:function(t){i(t,df)}}),vf=1e-8;function _f(t,e){return Math.abs(t-e)<vf}function xf(t,e,n){var i=0,o=t[0];if(o){for(var r=1;r<t.length;r++){var a=t[r];i+=Gr(o[0],o[1],a[0],a[1],e,n),o=a}var s=t[0];return _f(o[0],s[0])&&_f(o[1],s[1])||(i+=Gr(o[0],o[1],s[0],s[1],e,n)),0!==i}}function wf(t,e,n){this.name=t,this.geometries=e,n=n?[n[0],n[1]]:[(t=this.getBoundingRect()).x+t.width/2,t.y+t.height/2],this.center=n}function bf(t,e,n){for(var i=[],o=e[0],r=e[1],a=0;a<t.length;a+=2){var s=(s=t.charCodeAt(a)-64)>>1^-(1&s),l=(l=t.charCodeAt(a+1)-64)>>1^-(1&l);i.push([(o=s+=o)/n,(r=l+=r)/n])}return i}function Sf(t,o){var e=t;if(e.UTF8Encoding){for(var n=e.UTF8Scale,i=(null==n&&(n=1024),e.features),r=0;r<i.length;r++)for(var a=i[r].geometry,s=a.coordinates,l=a.encodeOffsets,h=0;h<s.length;h++){var u=s[h];if("Polygon"===a.type)s[h]=bf(u,l[h],n);else if("MultiPolygon"===a.type)for(var c=0;c<u.length;c++){var d=u[c];u[c]=bf(d,l[h][c],n)}}e.UTF8Encoding=!1}return S(et(t.features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,t=t.geometry,n=t.coordinates,i=[],t=("Polygon"===t.type&&i.push({type:"polygon",exterior:n[0],interiors:n.slice(1)}),"MultiPolygon"===t.type&&D(n,function(t){t[0]&&i.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})}),new wf(e[o||"name"],i,e.cp));return t.properties=e,t})}wf.prototype={constructor:wf,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var t=Number.MAX_VALUE,e=[t,t],n=[-t,-t],i=[],o=[],r=this.geometries,a=0;a<r.length;a++)"polygon"===r[a].type&&(xr(r[a].exterior,i,o),Bt(e,e,i),Rt(n,n,o));return 0===a&&(e[0]=e[1]=n[0]=n[1]=0),this._rect=new X(e[0],e[1],n[0]-e[0],n[1]-e[1])},contain:function(t){var e=this.getBoundingRect(),n=this.geometries;if(e.contain(t[0],t[1]))t:for(var i=0,o=n.length;i<o;i++)if("polygon"===n[i].type){var r=n[i].exterior,a=n[i].interiors;if(xf(r,t[0],t[1])){for(var s=0;s<(a?a.length:0);s++)if(xf(a[s]))continue t;return!0}}return!1},transformTo:function(t,e,n,i){for(var o=this.getBoundingRect(),r=o.width/o.height,r=(n?i=i||n/r:n=r*i,new X(t,e,n,i)),a=o.calculateTransform(r),s=this.geometries,l=0;l<s.length;l++)if("polygon"===s[l].type){for(var h=s[l].exterior,u=s[l].interiors,c=0;c<h.length;c++)zt(h[c],h[c],a);for(var d=0;d<(u?u.length:0);d++)for(c=0;c<u[d].length;c++)zt(u[d][c],u[d][c],a)}(o=this._rect).copy(r),this.center=[o.x+o.width/2,o.y+o.height/2]},cloneShallow:function(t){t=new wf(t=null==t?this.name:t,this.geometries,this.center);return t._rect=this._rect,t.transformTo=null,t}};var Mf=Po();function Cf(t){return"category"===t.type?(o=(e=t).getLabelModel(),r=Tf(e,o),!o.get("show")||e.scale.isBlank()?{labels:[],labelCategoryInterval:r.labelCategoryInterval}:r):(o=(n=t).scale.getTicks(),i=sf(n),{labels:S(o,function(t,e){return{formattedLabel:i(t,e),rawLabel:n.scale.getLabel(t),tickValue:t}})});var n,i,e,o,r}function If(t,e){var n,i,o,r,a,s;return"category"===t.type?(e=e,r=Df(n=t,"ticks"),a=uf(e),(s=kf(r,a))||(e.get("show")&&!n.scale.isBlank()||(i=[]),i=it(a)?Of(n,a,!0):"auto"===a?(s=Tf(n,n.getLabelModel()),o=s.labelCategoryInterval,S(s.labels,function(t){return t.tickValue})):Pf(n,o=a,!0),Af(r,a,{ticks:i,tickCategoryInterval:o}))):{ticks:t.scale.getTicks()}}function Tf(t,e){var n,i=Df(t,"labels"),e=uf(e),o=kf(i,e);return o||Af(i,e,{labels:it(e)?Of(t,e):Pf(t,n="auto"===e?null!=(i=Mf(o=t).autoInterval)?i:Mf(o).autoInterval=o.calculateCategoryInterval():e),labelCategoryInterval:n})}function Df(t,e){return Mf(t)[e]||(Mf(t)[e]=[])}function kf(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function Af(t,e,n){return t.push({key:e,value:n}),n}function Pf(t,e,n){for(var i=sf(t),o=t.scale,r=o.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),e=r[0],h=o.count(),h=(0!==e&&1<l&&2<h/l&&(e=Math.round(Math.ceil(e/l)*l)),cf(t)),t=a.get("showMinLabel")||h,a=a.get("showMaxLabel")||h,u=(t&&e!==r[0]&&c(r[0]),e);u<=r[1];u+=l)c(u);function c(t){s.push(n?t:{formattedLabel:i(t),rawLabel:o.getLabel(t),tickValue:t})}return a&&u-l!==r[1]&&c(r[1]),s}function Of(t,n,i){var o=t.scale,r=sf(t),a=[];return D(o.getTicks(),function(t){var e=o.getLabel(t);n(t,e)&&a.push(i?t:{formattedLabel:r(t),rawLabel:e,tickValue:t})}),a}function Lf(t,e,n){this.dim=t,this.scale=e,this._extent=n||[0,0],this.inverse=!1,this.onBand=!1}var Ef=[0,1];function zf(t,e){e=(t[1]-t[0])/e/2;t[0]+=e,t[1]-=e}Lf.prototype={constructor:Lf,contain:function(t){var e=this._extent,n=Math.min(e[0],e[1]),e=Math.max(e[0],e[1]);return n<=t&&t<=e},containData:function(t){return this.scale.contain(t)},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return Ys(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var n=this._extent;n[0]=t,n[1]=e},dataToCoord:function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&zf(n=n.slice(),i.count()),Gs(t,Ef,n,e)},coordToData:function(t,e){var n=this._extent,i=this.scale,i=(this.onBand&&"ordinal"===i.type&&zf(n=n.slice(),i.count()),Gs(t,n,Ef,e));return this.scale.scale(i)},pointToData:function(t,e){},getTicksCoords:function(t){var e,n,i,o,r,a,s,l=(t=t||{}).tickModel||this.getTickModel(),h=S(If(this,l).ticks,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this),l=l.get("alignWithLabel");function u(t,e){return t=Ws(t),e=Ws(e),a?e<t:t<e}return e=this,n=h,l=l,t=t.clamp,s=n.length,e.onBand&&!l&&s&&(l=e.getExtent(),1===s?(n[0].coord=l[0],i=n[1]={coord:l[0]}):(r=n[s-1].tickValue-n[0].tickValue,o=(n[s-1].coord-n[0].coord)/r,D(n,function(t){t.coord-=o/2}),e=1+(r=e.scale.getExtent())[1]-n[s-1].tickValue,i={coord:n[s-1].coord+o*e},n.push(i)),a=l[0]>l[1],u(n[0].coord,l[0])&&(t?n[0].coord=l[0]:n.shift()),t&&u(l[0],n[0].coord)&&n.unshift({coord:l[0]}),u(l[1],i.coord)&&(t?i.coord=l[1]:n.pop()),t)&&u(i.coord,l[1])&&n.push({coord:l[1]}),h},getMinorTicksCoords:function(){var t;return"ordinal"===this.scale.type?[]:(t=this.model.getModel("minorTick").get("splitNumber"),S(this.scale.getMinorTicks(t=0<t&&t<100?t:5),function(t){return S(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this))},getViewLabels:function(){return Cf(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),e=e[1]-e[0]+(this.onBand?1:0),t=(0===e&&(e=1),Math.abs(t[1]-t[0]));return Math.abs(t)/e},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){o=(n=f=this).getLabelModel();var t={axisRotate:n.getRotate?n.getRotate():n.isHorizontal&&!n.isHorizontal()?90:0,labelRotate:o.get("rotate")||0,font:o.getFont()},e=sf(f),n=(t.axisRotate-t.labelRotate)/180*Math.PI,i=(o=f.scale).getExtent(),o=o.count();if(i[1]-i[0]<1)return 0;for(var r=1,a=(40<o&&(r=Math.max(1,Math.floor(o/40))),i[0]),s=f.dataToCoord(a+1)-f.dataToCoord(a),l=Math.abs(s*Math.cos(n)),s=Math.abs(s*Math.sin(n)),h=0,u=0;a<=i[1];a+=r)var c=1.3*(d=ai(e(a),t.font,"center","top")).width,d=1.3*d.height,h=Math.max(h,c,7),u=Math.max(u,d,7);var n=h/l,l=u/s,s=(isNaN(n)&&(n=1/0),isNaN(l)&&(l=1/0),Math.max(0,Math.floor(Math.min(n,l)))),n=Mf(f.model),l=f.getExtent(),f=n.lastAutoInterval,p=n.lastTickCount;return null!=f&&null!=p&&Math.abs(f-s)<=1&&Math.abs(p-o)<=1&&s<f&&n.axisExtend0===l[0]&&n.axisExtend1===l[1]?s=f:(n.lastTickCount=o,n.lastAutoInterval=s,n.axisExtend0=l[0],n.axisExtend1=l[1]),s}};var u=Sf,Bf={},Rf=(D(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){Bf[t]=_t[t]}),{});function Nf(t,e,n){h.call(this),this.updateData(t,e,n)}D(["extendShape","extendPath","makePath","makeImage","mergePath","resizePath","createIcon","setHoverStyle","setLabelStyle","setTextStyle","setText","getFont","updateProps","initProps","getTransform","clipPointsByRect","clipRectByRect","registerShape","getShapeClass","Group","Image","Text","Circle","Sector","Ring","Polygon","Polyline","Rect","Line","BezierCurve","Arc","IncrementalDisplayable","CompoundPath","LinearGradient","RadialGradient","BoundingRect"],function(t){Rf[t]=Es[t]}),qh.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t,e){if(I){t=t.coordinateSystem;if("polar"!==t&&"cartesian2d"!==t)throw new Error("Line not support coordinateSystem besides cartesian and polar")}return Sd(this.getSource(),this,{useEncodeDefaulter:!0})},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clip:!0,label:{position:"top"},lineStyle:{width:2,type:"solid"},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}});var o=Nf.prototype,Ff=Nf.getSymbolSize=function(t,e){t=t.getItemVisual(e,"symbolSize");return t instanceof Array?t.slice():[+t,+t]};function Hf(t){return[t[0]/2,t[1]/2]}function Vf(t,e){this.parent.drift(t,e)}o._createSymbol=function(t,e,n,i,o){this.removeAll();e=yf(t,-1,-1,2,2,e.getItemVisual(n,"color"),o);e.attr({z2:100,culling:!0,scale:Hf(i)}),e.drift=Vf,this._symbolType=t,this.add(e)},o.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},o.getSymbolPath=function(){return this.childAt(0)},o.getScale=function(){return this.childAt(0).scale},o.highlight=function(){this.childAt(0).trigger("emphasis")},o.downplay=function(){this.childAt(0).trigger("normal")},o.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},o.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":e.cursor},o.updateData=function(t,e,n){this.silent=!1;var i,o,r=t.getItemVisual(e,"symbol")||"circle",a=t.hostModel,s=Ff(t,e),l=r!==this._symbolType;l?(o=t.getItemVisual(e,"symbolKeepAspect"),this._createSymbol(r,t,e,s,o)):((i=this.childAt(0)).silent=!1,Ds(i,{scale:Hf(s)},a,e)),this._updateCommon(t,e,s,n),l&&(i=this.childAt(0),r=n&&n.fadeIn,o={scale:i.scale.slice()},r&&(o.style={opacity:i.style.opacity}),i.scale=[0,0],r&&(i.style.opacity=0),ks(i,o,a,e)),this._seriesModel=a};var Gf=["itemStyle"],Wf=["emphasis","itemStyle"],Xf=["label"],Yf=["emphasis","label"];function Uf(t,e){var n,i;this.incremental||this.useHoverLayer||("emphasis"===e?(n=(i=this.__symbolOriginalScale)[1]/i[0],i={scale:[Math.max(1.1*i[0],i[0]+3),Math.max(1.1*i[1],i[1]+3*n)]},this.animateTo(i,400,"elasticOut")):"normal"===e&&this.animateTo({scale:this.__symbolOriginalScale},400,"elasticOut"))}function qf(t){this.group=new h,this._symbolCtor=t||Nf}o._updateCommon=function(n,t,e,i){var o=this.childAt(0),r=n.hostModel,a=n.getItemVisual(t,"color"),s=("image"!==o.type?o.useStyle({strokeNoScale:!0}):o.setStyle({opacity:1,shadowBlur:null,shadowOffsetX:null,shadowOffsetY:null,shadowColor:null}),i&&i.itemStyle),l=i&&i.hoverItemStyle,h=i&&i.symbolOffset,u=i&&i.labelModel,c=i&&i.hoverLabelModel,d=i&&i.hoverAnimation,f=i&&i.cursorStyle,p=(!i||n.hasItemOption?(s=(p=i&&i.itemModel?i.itemModel:n.getItemModel(t)).getModel(Gf).getItemStyle(["color"]),l=p.getModel(Wf).getItemStyle(),h=p.getShallow("symbolOffset"),u=p.getModel(Xf),c=p.getModel(Yf),d=p.getShallow("hoverAnimation"),f=p.getShallow("cursor")):l=C({},l),o.style),g=n.getItemVisual(t,"symbolRotate"),g=(o.attr("rotation",(g||0)*Math.PI/180||0),h&&o.attr("position",[v(h[0],e[0]),v(h[1],e[1])]),f&&o.attr("cursor",f),o.setColor(a,i&&i.symbolInnerColor),o.setStyle(s),n.getItemVisual(t,"opacity")),h=(null!=g&&(p.opacity=g),n.getItemVisual(t,"liftZ")),f=o.__z2Origin,m=(null!=h?null==f&&(o.__z2Origin=o.z2,o.z2+=h):null!=f&&(o.z2=f,o.__z2Origin=null),i&&i.useNameLabel);_s(p,l,u,c,{labelFetcher:r,labelDataIndex:t,defaultText:function(t,e){return m?n.getName(t):function(t,e){var n=t.mapDimension("defaultedLabel",!0),i=n.length;if(1===i)return Oh(t,e,n[0]);if(i){for(var o=[],r=0;r<n.length;r++){var a=Oh(t,e,n[r]);o.push(a)}return o.join(" ")}}(n,t)},isRectText:!0,autoColor:a}),o.__symbolOriginalScale=Hf(e),o.hoverStyle=l,o.highDownOnUpdate=d&&r.isAnimationEnabled()?Uf:null,gs(o)},o.fadeOut=function(t,e){var n=this.childAt(0);this.silent=n.silent=!0,e&&e.keepLabel||(n.style.text=null),Ds(n,{style:{opacity:0},scale:[0,0]},this._seriesModel,this.dataIndex,t)},Q(Nf,h);s=qf.prototype;function jf(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&(!i.isIgnore||!i.isIgnore(n))&&(!i.clipShape||i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function Zf(t){return(t=null==t||A(t)?t:{isIgnore:t})||{}}function Kf(t){t=t.hostModel;return{itemStyle:t.getModel("itemStyle").getItemStyle(["color"]),hoverItemStyle:t.getModel("emphasis.itemStyle").getItemStyle(),symbolRotate:t.get("symbolRotate"),symbolOffset:t.get("symbolOffset"),hoverAnimation:t.get("hoverAnimation"),labelModel:t.getModel("label"),hoverLabelModel:t.getModel("emphasis.label"),cursorStyle:t.get("cursor")}}function $f(t,e,n){var i,o=t.getBaseAxis(),r=t.getOtherAxis(o),n=function(t,e){var n=0,t=t.scale.getExtent();"start"===e?n=t[0]:"end"===e?n=t[1]:0<t[0]?n=t[0]:t[1]<0&&(n=t[1]);return n}(r,n),o=o.dim,r=r.dim,a=e.mapDimension(r),s=e.mapDimension(o),l="x"===r||"radius"===r?1:0,t=S(t.dimensions,function(t){return e.mapDimension(t)}),h=e.getCalculationInfo("stackResultDimension");return(i|=wd(e,t[0]))&&(t[0]=h),(i|=wd(e,t[1]))&&(t[1]=h),{dataDimsForPoint:t,valueStart:n,valueAxisDim:r,baseAxisDim:o,stacked:!!i,valueDim:a,baseDim:s,baseDataOffset:l,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function Qf(t,e,n,i){var o=NaN,r=(t.stacked&&(o=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(o)&&(o=t.valueStart),t.baseDataOffset),a=[];return a[r]=n.get(t.baseDim,i),a[1-r]=o,e.dataToPoint(a)}s.updateData=function(i,o){o=Zf(o);var r=this.group,a=i.hostModel,s=this._data,l=this._symbolCtor,h=Kf(i);s||r.removeAll(),i.diff(s).add(function(t){var e,n=i.getItemLayout(t);jf(i,n,t,o)&&((e=new l(i,t,h)).attr("position",n),i.setItemGraphicEl(t,e),r.add(e))}).update(function(t,e){var e=s.getItemGraphicEl(e),n=i.getItemLayout(t);jf(i,n,t,o)?(e?(e.updateData(i,t,h),Ds(e,{position:n},a)):(e=new l(i,t)).attr("position",n),r.add(e),i.setItemGraphicEl(t,e)):r.remove(e)}).remove(function(t){var e=s.getItemGraphicEl(t);e&&e.fadeOut(function(){r.remove(e)})}).execute(),this._data=i},s.isPersistent=function(){return!0},s.updateLayout=function(){var n=this._data;n&&n.eachItemGraphicEl(function(t,e){e=n.getItemLayout(e);t.attr("position",e)})},s.incrementalPrepareUpdate=function(t){this._seriesScope=Kf(t),this._data=null,this.group.removeAll()},s.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}n=Zf(n);for(var o=t.start;o<t.end;o++){var r,a=e.getItemLayout(o);jf(e,a,o,n)&&((r=new this._symbolCtor(e,o,this._seriesScope)).traverse(i),r.attr("position",a),this.group.add(r),e.setItemGraphicEl(o,r))}},s.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll()};var Jf=Bt,tp=Rt,ep=Ct,np=bt,ip=[],op=[],rp=[];function ap(t){return isNaN(t[0])||isNaN(t[1])}function sp(t,e,n,i,o,r,a,s,l,h){return("none"!==h&&h?lp:hp).apply(this,arguments)}function lp(t,e,n,i,o,r,a,s,l,h,u){for(var c=0,d=n,f=0;f<i;f++){var p,g,m,y=e[d];if(o<=d||d<0)break;if(ap(y)){if(u){d+=r;continue}break}d===n?t[0<r?"moveTo":"lineTo"](y[0],y[1]):0<l?(p=e[c],m=(y[g="y"===h?1:0]-p[g])*l,np(op,p),op[g]=p[g]+m,np(rp,y),rp[g]=y[g]-m,t.bezierCurveTo(op[0],op[1],rp[0],rp[1],y[0],y[1])):t.lineTo(y[0],y[1]),c=d,d+=r}return f}function hp(t,e,n,i,o,r,a,s,l,h,u){for(var c=0,d=n,f=0;f<i;f++){var p=e[d];if(o<=d||d<0)break;if(ap(p)){if(u){d+=r;continue}break}if(d===n)t[0<r?"moveTo":"lineTo"](p[0],p[1]),np(op,p);else if(0<l){var g=d+r,m=e[g];if(u)for(;m&&ap(e[g]);)m=e[g+=r];var y,v,_=.5,x=e[c];!(m=e[g])||ap(m)?np(rp,p):(ap(m)&&!u&&(m=p),It(ip,m,x),v="x"===h||"y"===h?(v="x"===h?0:1,y=Math.abs(p[v]-x[v]),Math.abs(p[v]-m[v])):(y=Ot(p,x),Ot(p,m)),ep(rp,p,ip,-l*(1-(_=v/(v+y))))),Jf(op,op,s),tp(op,op,a),Jf(rp,rp,s),tp(rp,rp,a),t.bezierCurveTo(op[0],op[1],rp[0],rp[1],p[0],p[1]),ep(op,p,ip,l*_)}else t.lineTo(p[0],p[1]);c=d,d+=r}return f}function up(t,e){var n=[1/0,1/0],i=[-1/0,-1/0];if(e)for(var o=0;o<t.length;o++){var r=t[o];r[0]<n[0]&&(n[0]=r[0]),r[1]<n[1]&&(n[1]=r[1]),r[0]>i[0]&&(i[0]=r[0]),r[1]>i[1]&&(i[1]=r[1])}return{min:e?n:i,max:e?i:n}}var cp=a.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:va(a.prototype.brush),buildPath:function(t,e){var n=e.points,i=0,o=n.length,r=up(n,e.smoothConstraint);if(e.connectNulls){for(;0<o&&ap(n[o-1]);o--);for(;i<o&&ap(n[i]);i++);}for(;i<o;)i+=sp(t,n,i,o,o,1,r.min,r.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),dp=a.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:va(a.prototype.brush),buildPath:function(t,e){var n=e.points,i=e.stackedOnPoints,o=0,r=n.length,a=e.smoothMonotone,s=up(n,e.smoothConstraint),l=up(i,e.smoothConstraint);if(e.connectNulls){for(;0<r&&ap(n[r-1]);r--);for(;o<r&&ap(n[o]);o++);}for(;o<r;){var h=sp(t,n,o,r,r,1,s.min,s.max,e.smooth,a,e.connectNulls);sp(t,i,o+h-1,h,r,-1,l.min,l.max,e.stackedOnSmooth,a,e.connectNulls),o+=h+1,t.closePath()}}});function fp(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++){var i=t[n],o=e[n];if(i[0]!==o[0]||i[1]!==o[1])return}return 1}}function pp(t,e){var n=[],i=[],o=[],r=[];return xr(t,n,i),xr(e,o,r),Math.max(Math.abs(n[0]-o[0]),Math.abs(n[1]-o[1]),Math.abs(i[0]-r[0]),Math.abs(i[1]-r[1]))}function gp(t){return"number"==typeof t?t:t?.5:0}function mp(t,e,n){for(var e=e.getBaseAxis(),i="x"===e.dim||"radius"===e.dim?0:1,o=[],r=0;r<t.length-1;r++){var a=t[r+1],s=t[r],l=(o.push(s),[]);switch(n){case"end":l[i]=a[i],l[1-i]=s[1-i],o.push(l);break;case"middle":var h=(s[i]+a[i])/2,u=[];l[i]=u[i]=h,l[1-i]=s[1-i],u[1-i]=a[1-i],o.push(l),o.push(u);break;default:l[i]=s[i],l[1-i]=a[1-i],o.push(l)}}return t[r]&&o.push(t[r]),o}function yp(t,e,n){var t=t.get("showAllSymbol"),i="auto"===t;if(!t||i){var o,r,t=n.getAxesByScale("ordinal")[0];if(t)if(!i||!function(t,e){for(var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count(),o=(isNaN(i)&&(i=0),e.count()),r=Math.max(1,Math.round(o/5)),a=0;a<o;a+=r)if(1.5*Nf.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return;return 1}(t,e))return o=e.mapDimension(t.dim),r={},D(t.getViewLabels(),function(t){r[t.tickValue]=1}),function(t){return!r.hasOwnProperty(e.get(o,t))}}}function vp(t,e,n){var i,o,r,a,s,l,h,u,c;return"cartesian2d"===t.type?(i=t.getBaseAxis().isHorizontal(),r=e,a=n,s=(o=t).getArea(),o=o.getBaseAxis().isHorizontal(),l=s.x,h=s.y,u=s.width,s=s.height,c=a.get("lineStyle.width")||2,l-=c/2,h-=c/2,u+=c,s+=c,l=Math.floor(l),u=Math.round(u),c=new O({shape:{x:l,y:h,width:u,height:s}}),r&&(c.shape[o?"width":"height"]=0,ks(c,{shape:{width:u,height:s}},a)),l=c,n.get("clip",!0)||(h=l.shape,r=Math.max(h.width,h.height),i?(h.y-=r,h.height+=2*r):(h.x-=r,h.width+=2*r)),l):(o=e,u=n,a=(s=t).getArea(),s=new _a({shape:{cx:Ws(s.cx,1),cy:Ws(s.cy,1),r0:Ws(a.r0,1),r:Ws(a.r,1),startAngle:a.startAngle,endAngle:a.endAngle,clockwise:a.clockwise}}),o&&(s.shape.endAngle=a.startAngle,ks(s,{shape:{endAngle:a.endAngle}},u)),s)}ru.extend({type:"line",init:function(){var t=new h,e=new qf;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,n){var i,o=t.coordinateSystem,r=this.group,a=t.getData(),s=t.getModel("lineStyle"),l=t.getModel("areaStyle"),h=a.mapArray(a.getItemLayout),u="polar"===o.type,c=this._coordSys,d=this._symbolDraw,f=this._polyline,p=this._polygon,g=this._lineGroup,m=t.get("animation"),y=!l.isEmpty(),v=l.get("origin"),_=function(t,e,n){if(!n.valueDim)return[];for(var i=[],o=0,r=e.count();o<r;o++)i.push(Qf(n,t,e,o));return i}(o,a,$f(o,a,v)),x=t.get("showSymbol"),w=x&&!u&&yp(t,a,o),b=this._data,u=(b&&b.eachItemGraphicEl(function(t,e){t.__temp&&(r.remove(t),b.setItemGraphicEl(e,null))}),x||d.remove(),r.add(g),!u&&t.get("step")),c=(o&&o.getArea&&t.get("clip",!0)&&(null!=(i=o.getArea()).width?(i.x-=.1,i.y-=.1,i.width+=.2,i.height+=.2):i.r0&&(i.r0-=.5,i.r1+=.5)),this._clipShapeForSymbol=i,f&&c.type===o.type&&u===this._step?(y&&!p?p=this._newPolygon(h,_,o,m):p&&!y&&(g.remove(p),p=this._polygon=null),g.setClipPath(vp(o,!1,t)),x&&d.updateData(a,{isIgnore:w,clipShape:i}),a.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),fp(this._stackedOnPoints,_)&&fp(this._points,h)||(m?this._updateAnimation(a,_,o,n,u,v):(u&&(h=mp(h,o,u),_=mp(_,o,u)),f.setShape({points:h}),p&&p.setShape({points:h,stackedOnPoints:_})))):(x&&d.updateData(a,{isIgnore:w,clipShape:i}),u&&(h=mp(h,o,u),_=mp(_,o,u)),f=this._newPolyline(h,o,m),y&&(p=this._newPolygon(h,_,o,m)),g.setClipPath(vp(o,!0,t))),function(t,e){var n=t.getVisual("visualMeta");if(n&&n.length&&t.count())if("cartesian2d"!==e.type)I&&console.warn("Visual map on line style is only supported on cartesian2d.");else{for(var i,o=n.length-1;0<=o;o--){var r,a=n[o].dimension,a=t.dimensions[a],a=t.getDimensionInfo(a);if("x"===(r=a&&a.coordDim)||"y"===r){i=n[o];break}}if(i){var s=e.getAxis(r),e=S(i.stops,function(t){return{coord:s.toGlobalCoord(s.dataToCoord(t.value)),color:t.color}}),l=e.length,h=i.outerColors.slice(),u=(l&&e[0].coord>e[l-1].coord&&(e.reverse(),h.reverse()),e[0].coord-10),c=e[l-1].coord+10,d=c-u;if(d<.001)return"transparent";D(e,function(t){t.offset=(t.coord-u)/d}),e.push({offset:l?e[l-1].offset:.5,color:h[1]||"transparent"}),e.unshift({offset:l?e[0].offset:.5,color:h[0]||"transparent"});l=new za(0,0,0,0,e,!0);return l[r]=u,l[r+"2"]=c,l}I&&console.warn("Visual map on line style only support x or y dimension.")}}(a,o)||a.getVisual("color")),n=(f.useStyle(T(s.getLineStyle(),{fill:"none",stroke:c,lineJoin:"bevel"})),t.get("smooth"),gp(t.get("smooth")));f.setShape({smooth:n,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),p&&(x=a.getCalculationInfo("stackedOnSeries"),d=0,p.useStyle(T(l.getAreaStyle(),{fill:c,opacity:.7,lineJoin:"bevel"})),x&&(d=gp(x.get("smooth"))),p.setShape({smooth:n,stackedOnSmooth:d,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})),this._data=a,this._coordSys=o,this._stackedOnPoints=_,this._points=h,this._step=u,this._valueOrigin=v},dispose:function(){},highlight:function(t,e,n,i){var o=t.getData(),r=Ao(o,i);if(!(r instanceof Array)&&null!=r&&0<=r){var a=o.getItemGraphicEl(r);if(!a){var s=o.getItemLayout(r);if(!s)return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(s[0],s[1]))return;(a=new Nf(o,r)).position=s,a.setZ(t.get("zlevel"),t.get("z")),a.ignore=isNaN(s[0])||isNaN(s[1]),a.__temp=!0,o.setItemGraphicEl(r,a),a.stopSymbolAnimation(!0),this.group.add(a)}a.highlight()}else ru.prototype.highlight.call(this,t,e,n,i)},downplay:function(t,e,n,i){var o,r=t.getData(),a=Ao(r,i);null!=a&&0<=a?(o=r.getItemGraphicEl(a))&&(o.__temp?(r.setItemGraphicEl(a,null),this.group.remove(o)):o.downplay()):ru.prototype.downplay.call(this,t,e,n,i)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new cp({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e},_newPolygon:function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new dp({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(n),this._polygon=n},_updateAnimation:function(t,e,n,i,o,r){var a=this._polyline,s=this._polygon,l=t.hostModel,e=function(t,e,n,i,o,r,a,s){l=[],e.diff(t).add(function(t){l.push({cmd:"+",idx:t})}).update(function(t,e){l.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){l.push({cmd:"-",idx:t})}).execute();for(var l,h=l,u=[],c=[],d=[],f=[],p=[],g=[],m=[],y=$f(o,e,a),v=$f(r,t,s),_=0;_<h.length;_++){var x=h[_],w=!0;switch(x.cmd){case"=":var b=t.getItemLayout(x.idx),S=e.getItemLayout(x.idx1);(isNaN(b[0])||isNaN(b[1]))&&(b=S.slice()),u.push(b),c.push(S),d.push(n[x.idx]),f.push(i[x.idx1]),m.push(e.getRawIndex(x.idx1));break;case"+":var M=x.idx;u.push(o.dataToPoint([e.get(y.dataDimsForPoint[0],M),e.get(y.dataDimsForPoint[1],M)])),c.push(e.getItemLayout(M).slice()),d.push(Qf(y,o,e,M)),f.push(i[M]),m.push(e.getRawIndex(M));break;case"-":var M=x.idx,b=t.getRawIndex(M);b!==M?(u.push(t.getItemLayout(M)),c.push(r.dataToPoint([t.get(v.dataDimsForPoint[0],M),t.get(v.dataDimsForPoint[1],M)])),d.push(n[M]),f.push(Qf(v,r,t,M)),m.push(b)):w=!1}w&&(p.push(x),g.push(g.length))}g.sort(function(t,e){return m[t]-m[e]});for(var C=[],I=[],T=[],D=[],k=[],_=0;_<g.length;_++){M=g[_];C[_]=u[M],I[_]=c[M],T[_]=d[M],D[_]=f[M],k[_]=p[M]}return{current:C,next:I,stackedOnCurrent:T,stackedOnNext:D,status:k}}(this._data,t,this._stackedOnPoints,e,this._coordSys,n,this._valueOrigin,r),r=e.current,h=e.stackedOnCurrent,u=e.next,c=e.stackedOnNext;if(o&&(r=mp(e.current,n,o),h=mp(e.stackedOnCurrent,n,o),u=mp(e.next,n,o),c=mp(e.stackedOnNext,n,o)),3e3<pp(r,u)||s&&3e3<pp(h,c))a.setShape({points:u}),s&&s.setShape({points:u,stackedOnPoints:c});else{a.shape.__points=e.current,a.shape.points=r,Ds(a,{shape:{points:u}},l),s&&(s.setShape({points:r,stackedOnPoints:h}),Ds(s,{shape:{points:u,stackedOnPoints:c}},l));for(var d,f=[],p=e.status,g=0;g<p.length;g++)"="===p[g].cmd&&(d=t.getItemGraphicEl(p[g].idx1))&&f.push({el:d,ptIdx:g});a.animators&&a.animators.length&&a.animators[0].during(function(){for(var t=0;t<f.length;t++)f[t].el.attr("position",a.shape.__points[f[t].ptIdx])})}},remove:function(t){var n=this.group,i=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),i&&i.eachItemGraphicEl(function(t,e){t.__temp&&(n.remove(t),i.setItemGraphicEl(e,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}});function _p(t,e){return Math.round(t.length/2)}var xp={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}};function wp(t){return this._axes[t]}function bp(t){this._axes={},this._dimList=[],this.name=t||""}function Sp(t){bp.call(this,t)}bp.prototype={constructor:bp,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return S(this._dimList,wp,this)},getAxesByScale:function(e){return e=e.toLowerCase(),et(this.getAxes(),function(t){return t.scale.type===e})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var n=this._dimList,i=t instanceof Array?[]:{},o=0;o<n.length;o++){var r=n[o],a=this._axes[r];i[r]=a[e](t[r])}return i}},Sp.prototype={constructor:Sp,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,n){var i=this.getAxis("x"),o=this.getAxis("y");return(n=n||[])[0]=i.toGlobalCoord(i.dataToCoord(t[0])),n[1]=o.toGlobalCoord(o.dataToCoord(t[1])),n},clampData:function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,o=n.getExtent(),r=i.getExtent(),n=n.parse(t[0]),i=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(o[0],o[1]),n),Math.max(o[0],o[1])),e[1]=Math.min(Math.max(Math.min(r[0],r[1]),i),Math.max(r[0],r[1])),e},pointToData:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return(e=e||[])[0]=n.coordToData(n.toLocalCoord(t[0])),e[1]=i.coordToData(i.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")},getArea:function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),n=Math.min(t[0],t[1]),i=Math.min(e[0],e[1]);return new X(n,i,Math.max(t[0],t[1])-n,Math.max(e[0],e[1])-i)}},Q(Sp,bp);function Mp(t,e,n,i,o){Lf.call(this,t,e,n),this.type=i||"value",this.position=o||"bottom"}Mp.prototype={constructor:Mp,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},Q(Mp,Lf);var r={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},Cp={},Ip=(Cp.categoryAxis=g({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},r),Cp.valueAxis=g({boundaryGap:[0,0],splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#eee",width:1}}},r),Cp.timeAxis=T({scale:!0,min:"dataMin",max:"dataMax"},Cp.valueAxis),Cp.logAxis=T({scale:!0,logBase:10},Cp.valueAxis),["value","category","time","log"]);function Tp(r,t,a,e){D(Ip,function(o){t.extend({type:r+"Axis."+o,mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?xl(t):{};g(t,e.getTheme().get(o+"Axis")),g(t,this.getDefaultOption()),t.type=a(r,t),n&&_l(t,i,n)},optionUpdated:function(){"category"===this.option.type&&(this.__ordinalMeta=Md.createByAxisModel(this))},getCategories:function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:j([{},Cp[o+"Axis"],e],!0)})}),c.registerSubTypeDefaulter(r+"Axis",l(a,r))}var Dp=c.extend({type:"cartesian2dAxis",axis:null,init:function(){Dp.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){Dp.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){Dp.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});function kp(t,e){return e.type||(e.data?"category":"value")}g(Dp.prototype,df);o={offset:0};function Ap(t,e){return t.getCoordSysModel()===e}function Pp(t,e,n){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,n),this.model=t}Tp("x",Dp,kp,o),Tp("y",Dp,kp,o),c.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});s=Pp.prototype;function Op(t,e,n,i){n.getAxesOnZeroOf=function(){return o?[o]:[]};var o,r=t[e],t=n.model,e=t.get("axisLine.onZero"),n=t.get("axisLine.onZeroAxisIndex");if(e){if(null!=n)Lp(r[n])&&(o=r[n]);else for(var a in r)if(r.hasOwnProperty(a)&&Lp(r[a])&&!i[s(r[a])]){o=r[a];break}o&&(i[s(o)]=!0)}function s(t){return t.dim+"_"+t.index}}function Lp(t){return t&&"category"!==t.type&&"time"!==t.type&&(e=(t=(t=t).scale.getExtent())[0],t=t[1],!(0<e&&0<t||e<0&&t<0));var e}s.type="grid",s.axisPointerEnabled=!0,s.getRect=function(){return this._rect},s.update=function(t,e){var n=this._axesMap,i=(this._updateScale(t,this.model),D(n.x,function(t){rf(t.scale,t.model)}),D(n.y,function(t){rf(t.scale,t.model)}),{});D(n.x,function(t){Op(n,"y",t,i)}),D(n.y,function(t){Op(n,"x",t,i)}),this.resize(this.model,e)},s.resize=function(t,e,n){var a=vl(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()}),i=(this._rect=a,this._axesList);function o(){D(i,function(t){var e,n,i=t.isHorizontal(),o=i?[0,a.width]:[0,a.height],r=t.inverse?1:0;t.setExtent(o[r],o[1-r]),o=t,e=i?a.x:a.y,r=o.getExtent(),n=r[0]+r[1],o.toGlobalCoord="x"===o.dim?function(t){return t+e}:function(t){return n-t+e},o.toLocalCoord="x"===o.dim?function(t){return t-e}:function(t){return n-t+e}})}o(),!n&&t.get("containLabel")&&(D(i,function(t){var e,n,i;t.model.get("axisLabel.inside")||(e=hf(t))&&(n=t.isHorizontal()?"height":"width",i=t.model.get("axisLabel.margin"),a[n]-=e[n]+i,"top"===t.position?a.y+=e.height+i:"left"===t.position&&(a.x+=e.width+i))}),o())},s.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n){if(null==e)for(var i in n)if(n.hasOwnProperty(i))return n[i];return n[e]}},s.getAxes=function(){return this._axesList.slice()},s.getCartesian=function(t,e){if(null!=t&&null!=e)return this._coordsMap["x"+t+"y"+e];A(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,i=this._coordsList;n<i.length;n++)if(i[n].getAxis("x").index===t||i[n].getAxis("y").index===e)return i[n]},s.getCartesians=function(){return this._coordsList.slice()},s.convertToPixel=function(t,e,n){t=this._findConvertTarget(t,e);return t.cartesian?t.cartesian.dataToPoint(n):t.axis?t.axis.toGlobalCoord(t.axis.dataToCoord(n)):null},s.convertFromPixel=function(t,e,n){t=this._findConvertTarget(t,e);return t.cartesian?t.cartesian.pointToData(n):t.axis?t.axis.coordToData(t.axis.toLocalCoord(n)):null},s._findConvertTarget=function(t,e){var n,i,o=e.seriesModel,r=e.xAxisModel||o&&o.getReferringComponents("xAxis")[0],a=e.yAxisModel||o&&o.getReferringComponents("yAxis")[0],e=e.gridModel,s=this._coordsList;return o?m(s,n=o.coordinateSystem)<0&&(n=null):r&&a?n=this.getCartesian(r.componentIndex,a.componentIndex):r?i=this.getAxis("x",r.componentIndex):a?i=this.getAxis("y",a.componentIndex):e&&e.coordinateSystem===this&&(n=this._coordsList[0]),{cartesian:n,axis:i}},s.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},s._initCartesian=function(r,t,e){var a={left:!1,right:!1,top:!1,bottom:!1},s={x:{},y:{}},l={x:0,y:0};function n(o){return function(t,e){var n,i;Ap(t,r)&&(n=t.get("position"),"x"===o?"top"!==n&&"bottom"!==n&&(n=a.bottom?"top":"bottom"):"left"!==n&&"right"!==n&&(n=a.left?"right":"left"),a[n]=!0,i="category"===(n=new Mp(o,af(t),[0,0],t.get("type"),n)).type,n.onBand=i&&t.get("boundaryGap"),n.inverse=t.get("inverse"),(t.axis=n).model=t,n.grid=this,n.index=e,this._axesList.push(n),s[o][e]=n,l[o]++)}}t.eachComponent("xAxis",n("x"),this),t.eachComponent("yAxis",n("y"),this),l.x&&l.y?D((this._axesMap=s).x,function(i,o){D(s.y,function(t,e){var e="x"+o+"y"+e,n=new Sp(e);n.grid=this,n.model=r,this._coordsMap[e]=n,this._coordsList.push(n),n.addAxis(i),n.addAxis(t)},this)},this):(this._axesMap={},this._axesList=[])},s._updateScale=function(t,i){function o(e,n){D(e.mapDimension(n.dim,!0),function(t){n.scale.unionExtentFromData(e,bd(e,t))})}D(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(t){var e,n;Bp(t)&&(n=(e=zp(t))[0],e=e[1],Ap(n,i))&&Ap(e,i)&&(n=this.getCartesian(n.componentIndex,e.componentIndex),e=t.getData(),t=n.getAxis("x"),n=n.getAxis("y"),"list"===e.type)&&(o(e,t),o(e,n))},this)},s.getTooltipAxes=function(n){var i=[],o=[];return D(this.getCartesians(),function(t){var e=null!=n&&"auto"!==n?t.getAxis(n):t.getBaseAxis(),t=t.getOtherAxis(e);m(i,e)<0&&i.push(e),m(o,t)<0&&o.push(t)}),{baseAxes:i,otherAxes:o}};var Ep=["xAxis","yAxis"];function zp(n){return S(Ep,function(t){var e=n.getReferringComponents(t)[0];if(I&&!e)throw new Error(t+' "'+lt(n.get(t+"Index"),n.get(t+"Id"),0)+'" not found');return e})}function Bp(t){return"cartesian2d"===t.get("coordinateSystem")}Pp.create=function(i,o){var r=[];return i.eachComponent("grid",function(t,e){var n=new Pp(t,i,o);n.name="grid_"+e,n.resize(t,o,!0),t.coordinateSystem=n,r.push(n)}),i.eachSeries(function(t){if(Bp(t)){var e=zp(t),n=e[0],e=e[1],i=n.getCoordSysModel();if(I){if(!i)throw new Error('Grid "'+lt(n.get("gridIndex"),n.get("gridId"),0)+'" not found');if(n.getCoordSysModel()!==e.getCoordSysModel())throw new Error("xAxis and yAxis must use the same grid")}i=i.coordinateSystem;t.coordinateSystem=i.getCartesian(n.componentIndex,e.componentIndex)}}),r},Pp.dimensions=Pp.prototype.dimensions=Sp.prototype.dimensions,th.register("cartesian2d",Pp);function Rp(t,e){this.opt=e,this.axisModel=t,T(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new h,(t=new h({position:e.position.slice(),rotation:e.rotation})).updateTransform(),this._transform=t.transform,this._dumbGroup=t}var Np=Math.PI,Fp=(Rp.prototype={constructor:Rp,hasBuilder:function(t){return!!Fp[t]},add:function(t){Fp[t].call(this)},getGroup:function(){return this.group}},{axisLine:function(){var i,t,o,r,e,a,s,l=this.opt,n=this.axisModel;n.get("axisLine.show")&&(t=this.axisModel.axis.getExtent(),e=this._transform,i=[t[0],0],t=[t[1],0],e&&(zt(i,i,e),zt(t,t,e)),o=C({lineCap:"round"},n.getModel("axisLine.lineStyle").getLineStyle()),this.group.add(new Pa({anid:"line",subPixelOptimize:!0,shape:{x1:i[0],y1:i[1],x2:t[0],y2:t[1]},style:o,strokeContainThreshold:l.strokeContainThreshold||5,silent:!0,z2:1})),r=n.get("axisLine.symbol"),e=n.get("axisLine.symbolSize"),"number"==typeof(n=n.get("axisLine.symbolOffset")||0)&&(n=[n,n]),null!=r)&&("string"==typeof r&&(r=[r,r]),a=(e="string"!=typeof e&&"number"!=typeof e?e:[e,e])[0],s=e[1],D([{rotate:l.rotation+Math.PI/2,offset:n[0],r:0},{rotate:l.rotation-Math.PI/2,offset:n[1],r:Math.sqrt((i[0]-t[0])*(i[0]-t[0])+(i[1]-t[1])*(i[1]-t[1]))}],function(t,e){var n;"none"!==r[e]&&null!=r[e]&&(e=yf(r[e],-a/2,-s/2,a,s,o.stroke,!0),n=t.r+t.offset,n=[i[0]+n*Math.cos(l.rotation),i[1]-n*Math.sin(l.rotation)],e.attr({rotation:t.rotate,position:n,silent:!0,z2:11}),this.group.add(e))},this))},axisTickLabel:function(){var t,e,n,i,o=this.axisModel,r=this.opt,a=function(t,e,n){var i=e.axis,o=e.getModel("axisTick");if(o.get("show")&&!i.scale.isBlank()){for(var r=o.getModel("lineStyle"),n=n.tickDirection*o.get("length"),a=Up(i.getTicksCoords(),t._transform,n,T(r.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),"ticks"),s=0;s<a.length;s++)t.group.add(a[s]);return a}}(this,o,r),s=function(s,l,h){var u,c,t,d,f,p,g,m,y=l.axis,e=lt(h.axisLabelShow,l.get("axisLabel.show"));if(e&&!y.scale.isBlank())return u=l.getModel("axisLabel"),c=u.get("margin"),e=y.getViewLabels(),t=(lt(h.labelRotate,u.get("rotate"))||0)*Np/180,d=Vp(h.rotation,t,h.labelDirection),f=l.getCategories&&l.getCategories(!0),p=[],g=Gp(l),m=l.get("triggerEvent"),D(e,function(t,e){var n=t.tickValue,i=t.formattedLabel,t=t.rawLabel,o=u,r=(o=f&&f[n]&&f[n].textStyle?new _(f[n].textStyle,u,l.ecModel):o).getTextColor()||l.get("axisLine.lineStyle.color"),a=[y.dataToCoord(n),h.labelOffset+h.labelDirection*c],a=new ga({anid:"label_"+n,position:a,rotation:d.rotation,silent:g,z2:10});xs(a.style,o,{text:i,textAlign:o.getShallow("align",!0)||d.textAlign,textVerticalAlign:o.getShallow("verticalAlign",!0)||o.getShallow("baseline",!0)||d.textVerticalAlign,textFill:"function"==typeof r?r("category"===y.type?t:"value"===y.type?n+"":n,e):r}),m&&(a.eventData=Hp(l),a.eventData.targetType="axisLabel",a.eventData.value=t),s._dumbGroup.add(a),a.updateTransform(),p.push(a),s.group.add(a),a.decomposeTransform()}),p}(this,o,r),l=(s=s,a=a,cf((t=o).axis)||(h=t.get("axisLabel.showMinLabel"),t=t.get("axisLabel.showMaxLabel"),a=a||[],d=(s=s||[])[0],u=s[1],e=s[s.length-1],s=s[s.length-2],n=a[0],c=a[1],i=a[a.length-1],a=a[a.length-2],!1===h?(Wp(d),Wp(n)):Xp(d,u)&&(h?(Wp(u),Wp(c)):(Wp(d),Wp(n))),!1===t?(Wp(e),Wp(i)):Xp(s,e)&&(t?(Wp(s),Wp(a)):(Wp(e),Wp(i)))),this),h=o,u=r,c=h.axis,d=h.getModel("minorTick");if(d.get("show")&&!c.scale.isBlank()){var f=c.getMinorTicksCoords();if(f.length)for(var c=d.getModel("lineStyle"),p=u.tickDirection*d.get("length"),g=T(c.getLineStyle(),T(h.getModel("axisTick").getLineStyle(),{stroke:h.get("axisLine.lineStyle.color")})),m=0;m<f.length;m++)for(var y=Up(f[m],l._transform,p,g,"minorticks_"+m),v=0;v<y.length;v++)l.group.add(y[v])}},axisName:function(){var t,e,n,i,o,r,a,s,l=this.opt,h=this.axisModel,u=lt(l.axisName,h.get("name"));u&&(s=h.get("nameLocation"),i=l.nameDirection,t=h.getModel("nameTextStyle"),a=h.get("nameGap")||0,e=(r=this.axisModel.axis.getExtent())[0]>r[1]?-1:1,e=["start"===s?r[0]-e*a:"end"===s?r[1]+e*a:(r[0]+r[1])/2,Yp(s)?l.labelOffset+i*a:0],null!=(a=h.get("nameRotate"))&&(a=a*Np/180),Yp(s)?n=Vp(l.rotation,null!=a?a:l.rotation,i):(n=function(t,e,n,i){var o,n=Us(n-t.rotation),t=i[0]>i[1],i="start"===e&&!t||"start"!==e&&t;e=qs(n-Np/2)?(o=i?"bottom":"top","center"):qs(n-1.5*Np)?(o=i?"top":"bottom","center"):(o="middle",n<1.5*Np&&Np/2<n?i?"left":"right":i?"right":"left");return{rotation:n,textAlign:e,textVerticalAlign:o}}(l,s,a||0,r),null!=(o=l.axisNameAvailableWidth)&&(o=Math.abs(o/Math.sin(n.rotation)),isFinite(o)||(o=null))),i=t.getFont(),a=(s=h.get("nameTruncate",!0)||{}).ellipsis,r=lt(l.nameTruncateMaxWidth,s.maxWidth,o),l=null!=a&&null!=r?cl(u,r,i,a,{minChar:2,placeholder:s.placeholder}):u,o=h.get("tooltip",!0),(a={componentType:r=h.mainType,name:u,$vars:["name"]})[r+"Index"]=h.componentIndex,xs((s=new ga({anid:"name",__fullText:u,__truncatedText:l,position:e,rotation:n.rotation,silent:Gp(h),z2:1,tooltip:o&&o.show?C({content:u,formatter:function(){return u},formatterParams:a},o):null})).style,t,{text:l,textFont:i,textFill:t.getTextColor()||h.get("axisLine.lineStyle.color"),textAlign:t.get("align")||n.textAlign,textVerticalAlign:t.get("verticalAlign")||n.textVerticalAlign}),h.get("triggerEvent")&&(s.eventData=Hp(h),s.eventData.targetType="axisName",s.eventData.name=u),this._dumbGroup.add(s),s.updateTransform(),this.group.add(s),s.decomposeTransform())}}),Hp=Rp.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},Vp=Rp.innerTextLayout=function(t,e,n){var i,e=Us(e-t),t=qs(e)?(i=0<n?"top":"bottom","center"):qs(e-Np)?(i=0<n?"bottom":"top","center"):(i="middle",0<e&&e<Np?0<n?"right":"left":0<n?"left":"right");return{rotation:e,textAlign:t,textVerticalAlign:i}};var Gp=Rp.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)};function Wp(t){t&&(t.ignore=!0)}function Xp(t,e){var n,i=t&&t.getBoundingRect().clone(),o=e&&e.getBoundingRect().clone();if(i&&o)return ve(n=pe([]),n,-t.rotation),i.applyTransform(me([],n,t.getLocalTransform())),o.applyTransform(me([],n,e.getLocalTransform())),i.intersect(o)}function Yp(t){return"middle"===t||"center"===t}function Up(t,e,n,i,o){for(var r=[],a=[],s=[],l=0;l<t.length;l++){var h=t[l].coord,h=(a[0]=h,s[a[1]=0]=h,s[1]=n,e&&(zt(a,a,e),zt(s,s,e)),new Pa({anid:o+"_"+t[l].tickValue,subPixelOptimize:!0,shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,silent:!0}));r.push(h)}return r}var qp=D,jp=l;function Zp(t,e){var h,u,o,c,d,f,n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return h=n,e=e,o=(u=t).getComponent("tooltip"),c=u.getComponent("axisPointer"),d=c.get("link",!0)||[],f=[],qp(e.getCoordinateSystems(),function(a){var s,l,t,e,n;function i(t,e,n){var i,o=n.model.getModel("axisPointer",c),r=o.get("show");r&&("auto"!==r||t||Qp(o))&&(null==e&&(e=o.get("triggerTooltip")),r=(o=t?function(t,e,n,i,o,r){var a=e.getModel("axisPointer"),s={};qp(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){s[t]=M(a.get(t))}),s.snap="category"!==t.type&&!!r,"cross"===a.get("type")&&(s.type="line");e=s.label||(s.label={});null==e.show&&(e.show=!1),"cross"===o&&(o=a.get("label.show"),e.show=null==o||o,r||(o=s.lineStyle=a.get("crossStyle"))&&T(e,o.textStyle));return t.model.getModel("axisPointer",new _(s,n,i))}(n,l,c,u,t,e):o).get("snap"),t=Jp(n.model),i=e||r||"category"===n.type,e=h.axesInfo[t]={key:t,axis:n,coordSys:a,axisPointerModel:o,triggerTooltip:e,involveSeries:i,snap:r,useHandle:Qp(o),seriesModels:[]},s[t]=e,h.seriesInvolved|=i,null!=(r=function(t,e){for(var n=e.model,i=e.dim,o=0;o<t.length;o++){var r=t[o]||{};if(Kp(r[i+"AxisId"],n.id)||Kp(r[i+"AxisIndex"],n.componentIndex)||Kp(r[i+"AxisName"],n.name))return o}}(d,n)))&&((o=f[r]||(f[r]={axesInfo:{}})).axesInfo[t]=e,o.mapper=d[r].mapper,e.linkGroup=o)}a.axisPointerEnabled&&(t=Jp(a.model),s=h.coordSysAxesInfo[t]={},l=(h.coordSysMap[t]=a).model.getModel("tooltip",o),qp(a.getAxes(),jp(i,!1,null)),a.getTooltipAxes)&&o&&l.get("show")&&(t="axis"===l.get("trigger"),e="cross"===l.get("axisPointer.type"),n=a.getTooltipAxes(l.get("axisPointer.axis")),(t||e)&&qp(n.baseAxes,jp(i,!e||"cross",t)),e)&&qp(n.otherAxes,jp(i,"cross",!1))}),n.seriesInvolved&&function(o,t){t.eachSeries(function(n){var i=n.coordinateSystem,t=n.get("tooltip.trigger",!0),e=n.get("tooltip.show",!0);i&&"none"!==t&&!1!==t&&"item"!==t&&!1!==e&&!1!==n.get("axisPointer.show",!0)&&qp(o.coordSysAxesInfo[Jp(i.model)],function(t){var e=t.axis;i.getAxis(e.dim)===e&&(t.seriesModels.push(n),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=n.getData().count())})},this)}(n,t),n}function Kp(t,e){return"all"===t||b(t)&&0<=m(t,e)||t===e}function $p(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[Jp(t)]}function Qp(t){return!!t.get("handle.show")}function Jp(t){return t.type+"||"+t.id}var tg=Fc({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,n,i){var o,r,a,s,l,h;this.axisPointerClass&&(o=$p(o=t))&&(l=o.axisPointerModel,r=o.axis.scale,a=l.option,h=l.get("status"),null!=(s=l.get("value"))&&(s=r.parse(s)),l=Qp(l),null==h&&(a.status=l?"show":"hide"),(h=r.getExtent().slice())[0]>h[1]&&h.reverse(),(s=null==s||s>h[1]?h[1]:s)<h[0]&&(s=h[0]),a.value=s,l)&&(a.status=o.axis.scale.isBlank()?"hide":"show"),tg.superApply(this,"render",arguments),eg(this,t,0,n,0,!0)},updateAxisPointer:function(t,e,n,i,o){eg(this,t,0,n,0,!1)},remove:function(t,e){var n=this._axisPointer;n&&n.remove(e),tg.superApply(this,"remove",arguments)},dispose:function(t,e){ng(this,e),tg.superApply(this,"dispose",arguments)}});function eg(t,e,n,i,o,r){var a,s=tg.getAxisPointerClass(t.axisPointerClass);s&&((a=(a=$p(a=e))&&a.axisPointerModel)?(t._axisPointer||(t._axisPointer=new s)).render(e,a,i,r):ng(t,i))}function ng(t,e,n){var i=t._axisPointer;i&&i.dispose(e,n),t._axisPointer=null}var ig=[];function og(t,e,n){n=n||{};var t=t.coordinateSystem,i=e.axis,o={},r=i.getAxesOnZeroOf()[0],a=i.position,s=r?"onZero":a,i=i.dim,t=t.getRect(),t=[t.x,t.x+t.width,t.y,t.y+t.height],l={left:0,right:1,top:0,bottom:1,onZero:2},h=e.get("offset")||0,h="x"===i?[t[2]-h,t[3]+h]:[t[0]-h,t[1]+h],u=(r&&(u=r.toGlobalCoord(r.dataToCoord(0)),h[l.onZero]=Math.max(Math.min(u,h[1]),h[0])),o.position=["y"===i?h[l[s]]:t[0],"x"===i?h[l[s]]:t[3]],o.rotation=Math.PI/2*("x"===i?0:1),o.labelDirection=o.tickDirection=o.nameDirection={top:-1,bottom:1,left:-1,right:1}[a],o.labelOffset=r?h[l[a]]-h[l.onZero]:0,e.get("axisTick.inside")&&(o.tickDirection=-o.tickDirection),lt(n.labelInside,e.get("axisLabel.inside"))&&(o.labelDirection=-o.labelDirection),e.get("axisLabel.rotate"));return o.labelRotate="top"===s?-u:u,o.z2=1,o}tg.registerAxisPointerClass=function(t,e){if(I&&ig[t])throw new Error("axisPointer "+t+" exists");ig[t]=e},tg.getAxisPointerClass=function(t){return t&&ig[t]};var rg,ag,sg=["axisLine","axisTickLabel","axisName"],lg=["splitArea","splitLine","minorSplitLine"],hg=tg.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(e,t,n,i){this.group.removeAll();var o,r,a=this._axisGroup;this._axisGroup=new h,this.group.add(this._axisGroup),e.get("show")&&(r=og(o=e.getCoordSysModel(),e),r=new Rp(e,r),D(sg,r.add,r),this._axisGroup.add(r.getGroup()),D(lg,function(t){e.get(t+".show")&&this["_"+t](e,o)},this),Ps(a,this._axisGroup,e),hg.superCall(this,"render",e,t,n,i))},remove:function(){this.__splitAreaColors=null},_splitLine:function(t,e){var n=t.axis;if(!n.scale.isBlank())for(var t=t.getModel("splitLine"),i=t.getModel("lineStyle"),o=b(o=i.get("color"))?o:[o],r=e.coordinateSystem.getRect(),a=n.isHorizontal(),s=0,l=n.getTicksCoords({tickModel:t}),h=[],u=[],c=i.getLineStyle(),d=0;d<l.length;d++){var f=n.toGlobalCoord(l[d].coord),f=(a?(h[0]=f,h[1]=r.y,u[0]=f,u[1]=r.y+r.height):(h[0]=r.x,h[1]=f,u[0]=r.x+r.width,u[1]=f),s++%o.length),p=l[d].tickValue;this._axisGroup.add(new Pa({anid:null!=p?"line_"+l[d].tickValue:null,subPixelOptimize:!0,shape:{x1:h[0],y1:h[1],x2:u[0],y2:u[1]},style:T({stroke:o[f]},c),silent:!0}))}},_minorSplitLine:function(t,e){var n=t.axis,t=t.getModel("minorSplitLine").getModel("lineStyle"),i=e.coordinateSystem.getRect(),o=n.isHorizontal(),r=n.getMinorTicksCoords();if(r.length)for(var a=[],s=[],l=t.getLineStyle(),h=0;h<r.length;h++)for(var u=0;u<r[h].length;u++){var c=n.toGlobalCoord(r[h][u].coord);o?(a[0]=c,a[1]=i.y,s[0]=c,s[1]=i.y+i.height):(a[0]=i.x,a[1]=c,s[0]=i.x+i.width,s[1]=c),this._axisGroup.add(new Pa({anid:"minor_line_"+r[h][u].tickValue,subPixelOptimize:!0,shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:l,silent:!0}))}},_splitArea:function(t,e){var n=this,i=this._axisGroup,o=(t=t).axis;if(!o.scale.isBlank()){var t=t.getModel("splitArea"),r=t.getModel("areaStyle"),a=r.get("color"),s=e.coordinateSystem.getRect(),l=o.getTicksCoords({tickModel:t,clamp:!0});if(l.length){var h=a.length,u=n.__splitAreaColors,c=P(),d=0;if(u)for(var f=0;f<l.length;f++){var p=u.get(l[f].tickValue);if(null!=p){d=(p+(h-1)*f)%h;break}}for(var g=o.toGlobalCoord(l[0].coord),m=r.getAreaStyle(),a=b(a)?a:[a],f=1;f<l.length;f++){var y,v,_,x,w=o.toGlobalCoord(l[f].coord),g=o.isHorizontal()?(y=g,v=s.y,x=s.height,y+(_=w-y)):(y=s.x,v=g,_=s.width,v+(x=w-v)),w=l[f-1].tickValue;null!=w&&c.set(w,d),i.add(new O({anid:null!=w?"area_"+w:null,shape:{x:y,y:v,width:_,height:x},style:T({fill:a[d]},m),silent:!0})),d=(d+1)%h}n.__splitAreaColors=c}}}}),o=(hg.extend({type:"xAxis"}),hg.extend({type:"yAxis"}),Fc({type:"grid",render:function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new O({shape:t.coordinateSystem.getRect(),style:T({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),Pc(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),zc((rg="circle",ag=r="line",{seriesType:r,performRawSeries:!0,reset:function(a,t,e){var n=a.getData(),s=a.get("symbol"),l=a.get("symbolSize"),i=a.get("symbolKeepAspect"),h=a.get("symbolRotate"),u=it(s),c=it(l),d=it(h),f=u||c||d,o=!u&&s?s:rg,r=c?null:l;if(n.setVisual({legendSymbol:ag||o,symbol:o,symbolSize:r,symbolKeepAspect:i,symbolRotate:h}),!t.isSeriesFiltered(a))return{dataEach:n.hasItemOption||f?function(t,e){var n,i,o,r;f&&(r=a.getRawValue(e),n=a.getDataParams(e),u&&t.setItemVisual(e,"symbol",s(r,n)),c&&t.setItemVisual(e,"symbolSize",l(r,n)),d)&&t.setItemVisual(e,"symbolRotate",h(r,n)),t.hasItemOption&&(n=(r=t.getItemModel(e)).getShallow("symbol",!0),i=r.getShallow("symbolSize",!0),o=r.getShallow("symbolRotate",!0),r=r.getShallow("symbolKeepAspect",!0),null!=n&&t.setItemVisual(e,"symbol",n),null!=i&&t.setItemVisual(e,"symbolSize",i),null!=o&&t.setItemVisual(e,"symbolRotate",o),null!=r)&&t.setItemVisual(e,"symbolKeepAspect",r)}:null}}})),Ec({seriesType:"line",plan:nu(),reset:function(t){var u,c,e=t.getData(),d=t.coordinateSystem,f=t.pipelineContext.large;if(d)return u=S(d.dimensions,function(t){return e.mapDimension(t)}).slice(0,2),c=u.length,t=e.getCalculationInfo("stackResultDimension"),wd(e,u[0])&&(u[0]=t),wd(e,u[1])&&(u[1]=t),c&&{progress:function(t,e){for(var n,i,o=t.end-t.start,r=f&&new Float32Array(o*c),a=t.start,s=0,l=[],h=[];a<t.end;a++)i=1===c?(n=e.get(u[0],a),!isNaN(n)&&d.dataToPoint(n,null,h)):(n=l[0]=e.get(u[0],a),i=l[1]=e.get(u[1],a),!isNaN(n)&&!isNaN(i)&&d.dataToPoint(l,null,h)),f?(r[s++]=i?i[0]:NaN,r[s++]=i?i[1]:NaN):e.setItemLayout(a,i&&i.slice()||[NaN,NaN]);f&&e.setLayout("symbolPoints",r)}}}}),Oc(n.PROCESSOR.STATISTIC,{seriesType:"line",modifyOutputEnd:!0,reset:function(t,e,n){var i,o,r=t.getData(),a=t.get("sampling"),s=t.coordinateSystem;"cartesian2d"===s.type&&a&&(i=s.getBaseAxis(),s=s.getOtherAxis(i),i=i.getExtent(),i=Math.abs(i[1]-i[0]),1<(i=Math.round(r.count()/i)))&&("string"==typeof a?o=xp[a]:"function"==typeof a&&(o=a),o)&&t.setData(r.downSample(r.mapDimension(s.dim),1/i,o,_p))}}),Nc({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),Fc({type:"title",render:function(t,e,n){var i,o,r,a,s,l,h,u,c;this.group.removeAll(),t.get("show")&&(i=this.group,h=t.getModel("textStyle"),o=t.getModel("subtextStyle"),u=t.get("textAlign"),c=ht(t.get("textBaseline"),t.get("textVerticalAlign")),s=(h=new ga({style:xs({},h,{text:t.get("text"),textFill:h.getTextColor()},{disableBox:!0}),z2:10})).getBoundingRect(),l=t.get("subtext"),o=new ga({style:xs({},o,{text:l,textFill:o.getTextColor(),y:s.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),r=t.get("link"),a=t.get("sublink"),s=t.get("triggerEvent",!0),h.silent=!r&&!s,o.silent=!a&&!s,r&&h.on("click",function(){dl(r,"_"+t.get("target"))}),a&&o.on("click",function(){dl(a,"_"+t.get("subtarget"))}),h.eventData=o.eventData=s?{componentType:"title",componentIndex:t.componentIndex}:null,i.add(h),l&&i.add(o),s=i.getBoundingRect(),(l=t.getBoxLayoutParams()).width=s.width,l.height=s.height,l=vl(l,{width:n.getWidth(),height:n.getHeight()},t.get("padding")),u||("right"===(u="middle"===(u=t.get("left")||t.get("right"))?"center":u)?l.x+=l.width:"center"===u&&(l.x+=l.width/2)),c||("bottom"===(c="center"===(c=t.get("top")||t.get("bottom"))?"middle":c)?l.y+=l.height:"middle"===c&&(l.y+=l.height/2),c=c||"top"),i.attr("position",[l.x,l.y]),h.setStyle(n={textAlign:u,textVerticalAlign:c}),o.setStyle(n),s=i.getBoundingRect(),h=l.margin,(u=t.getItemStyle(["color","opacity"])).fill=t.get("backgroundColor"),c=new O({shape:{x:s.x-h[3],y:s.y-h[0],width:s.width+h[1]+h[3],height:s.height+h[0]+h[2],r:t.get("borderRadius")},style:u,subPixelOptimize:!0,silent:!0}),i.add(c))}}),gu.legend.selector),ug={all:{type:"all",title:M(o.all)},inverse:{type:"inverse",title:M(o.inverse)}},cg=Nc({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{},this._updateSelector(t)},mergeOption:function(t){cg.superCall(this,"mergeOption",t),this._updateSelector(t)},_updateSelector:function(t){var n=t.selector;b(n=!0===n?t.selector=["all","inverse"]:n)&&D(n,function(t,e){k(t)&&(t={type:t}),n[e]=g(t,ug[t.type])})},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}e||this.select(t[0].get("name"))}},_updateData:function(i){var o=[],r=[];i.eachRawSeries(function(t){var e,n=t.name;r.push(n),t.legendVisualProvider&&(n=t.legendVisualProvider.getAllNames(),i.isSeriesFiltered(t)||(r=r.concat(n)),n.length)?o=o.concat(n):e=!0,e&&Do(t)&&o.push(t.name)}),this._availableNames=r;var t=S(this.get("data")||o,function(t){return new _(t="string"!=typeof t&&"number"!=typeof t?t:{name:t},this,this.ecModel)},this);this._data=t},getData:function(){return this._data},select:function(t){var e=this.option.selected;"single"===this.get("selectedMode")&&D(this._data,function(t){e[t.get("name")]=!1}),e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},allSelect:function(){var t=this._data,e=this.option.selected;D(t,function(t){e[t.get("name",!0)]=!0})},inverseSelect:function(){var t=this._data,e=this.option.selected;D(t,function(t){t=t.get("name",!0);e.hasOwnProperty(t)||(e[t]=!0),e[t]=!e[t]})},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&0<=m(this._availableNames,t)},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",itemStyle:{borderWidth:0},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:" sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}}});function dg(t,e,n){var i,o={},r="toggleSelected"===t;return n.eachComponent("legend",function(n){r&&null!=i?n[i?"select":"unSelect"](e.name):"allSelect"===t||"inverseSelect"===t?n[t]():(n[t](e.name),i=n.isSelected(e.name)),D(n.getData(),function(t){var e,t=t.get("name");"\n"!==t&&""!==t&&(e=n.isSelected(t),o.hasOwnProperty(t)?o[t]=o[t]&&e:o[t]=e)})}),"allSelect"===t||"inverseSelect"===t?{selected:o}:{name:e.name,selected:o}}Lc("legendToggleSelect","legendselectchanged",l(dg,"toggleSelected")),Lc("legendAllSelect","legendselectall",l(dg,"allSelect")),Lc("legendInverseSelect","legendinverseselect",l(dg,"inverseSelect")),Lc("legendSelect","legendselected",l(dg,"select")),Lc("legendUnSelect","legendunselected",l(dg,"unSelect"));var fg=l,pg=D,gg=h,s=Fc({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new gg),this._backgroundEl,this.group.add(this._selectorGroup=new gg),this._isFirstRender=!0},getContentGroup:function(){return this._contentGroup},getSelectorGroup:function(){return this._selectorGroup},render:function(t,e,n){var i,o,r,a,s,l=this._isFirstRender;this._isFirstRender=!1,this.resetInner(),t.get("show",!0)&&(o=t.get("align"),i=t.get("orient"),o&&"auto"!==o||(o="right"===t.get("left")&&"vertical"===i?"right":"left"),a=t.get("selector",!0),s=t.get("selectorPosition",!0),this.renderInner(o,t,e,n,a,i,s=!a||s&&"auto"!==s?s:"horizontal"===i?"end":"start"),r=vl(e=t.getBoxLayoutParams(),i={width:n.getWidth(),height:n.getHeight()},n=t.get("padding")),r=vl(T({width:(o=this.layoutInner(t,o,r,l,a,s)).width,height:o.height},e),i,n),this.group.attr("position",[r.x-o.x,r.y-o.y]),this.group.add(this._backgroundEl=(l=o,s=el((a=t).get("padding")),(e=a.getItemStyle(["color","opacity"])).fill=a.get("backgroundColor"),l=new O({shape:{x:l.x-s[3],y:l.y-s[0],width:l.width+s[1]+s[3],height:l.height+s[0]+s[2],r:a.get("borderRadius")},style:e,silent:!0,z2:-1}))))},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},renderInner:function(l,h,u,c,t,e,n){var d=this.getContentGroup(),f=P(),p=h.get("selectedMode"),g=[];u.eachRawSeries(function(t){t.get("legendHoverLink")||g.push(t.id)}),pg(h.getData(),function(i,o){var t,e,n,r,a,s=i.get("name");this.newlineDisabled||""!==s&&"\n"!==s?(t=u.getSeriesByName(s)[0],f.get(s)||(t?(e=(a=t.getData()).getVisual("color"),n=a.getVisual("borderColor"),"function"==typeof e&&(e=e(t.getDataParams(0))),"function"==typeof n&&(n=n(t.getDataParams(0))),r=a.getVisual("legendSymbol")||"roundRect",a=a.getVisual("symbol"),this._createItem(s,o,i,h,r,a,l,e,n,p).on("click",fg(yg,s,null,c,g)).on("mouseover",fg(vg,t.name,null,c,g)).on("mouseout",fg(_g,t.name,null,c,g)),f.set(s,!0)):u.eachRawSeries(function(t){var e,n;f.get(s)||t.legendVisualProvider&&(t=t.legendVisualProvider).containName(s)&&(e=t.indexOfName(s),n=t.getItemVisual(e,"color"),t=t.getItemVisual(e,"borderColor"),this._createItem(s,o,i,h,"roundRect",null,l,n,t,p).on("click",fg(yg,null,s,c,g)).on("mouseover",fg(vg,null,s,c,g)).on("mouseout",fg(_g,null,s,c,g)),f.set(s,!0))},this),!I)||f.get(s)||console.warn(s+" series not exists. Legend data should be same with series name or data name.")):d.add(new gg({newline:!0}))},this),t&&this._createSelector(t,h,c,e,n)},_createSelector:function(t,r,a,e,n){var s=this.getSelectorGroup();pg(t,function(t){var e,n,i,o;e=(t=t).type,n=new ga({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){a.dispatchAction({type:"all"===e?"legendAllSelect":"legendInverseSelect"})}}),s.add(n),i=r.getModel("selectorLabel"),o=r.getModel("emphasis.selectorLabel"),_s(n.style,n.hoverStyle={},i,o,{defaultText:t.title,isRectText:!1}),gs(n)})},_createItem:function(t,e,n,i,o,r,a,s,l,h){var u=i.get("itemWidth"),c=i.get("itemHeight"),d=i.get("inactiveColor"),f=i.get("inactiveBorderColor"),p=i.get("symbolKeepAspect"),g=i.getModel("itemStyle"),m=i.isSelected(t),y=new gg,v=n.getModel("textStyle"),_=n.get("icon"),n=n.getModel("tooltip"),x=n.parentModel,w=yf(o=_||o,0,0,u,c,m?s:d,null==p||p),o=(y.add(mg(w,o,g,l,f,m)),_||!r||r===o&&"none"!==r||(_=yf(r="none"===r?"circle":r,(u-(w=.8*c))/2,(c-w)/2,w,w,m?s:d,null==p||p),y.add(mg(_,r,g,l,f,m))),"left"===a?u+5:-5),w=a,s=i.get("formatter"),p=t,_=("string"==typeof s&&s?p=s.replace("{name}",null!=t?t:""):"function"==typeof s&&(p=s(t)),y.add(new ga({style:xs({},v,{text:p,x:o,y:c/2,textFill:m?v.getTextColor():d,textAlign:w,textVerticalAlign:"middle"})})),new O({shape:y.getBoundingRect(),invisible:!0,tooltip:n.get("show")?C({content:t,formatter:x.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:i.componentIndex,name:t,$vars:["name"]}},n.option):null}));return y.add(_),y.eachChild(function(t){t.silent=!0}),_.silent=!h,this.getContentGroup().add(y),gs(y),y.__legendDataIndex=e,y},layoutInner:function(t,e,n,i,o,r){var a,s,l,h,u,c=this.getContentGroup(),d=this.getSelectorGroup(),n=(yl(t.get("orient"),c,t.get("itemGap"),n.width,n.height),c.getBoundingRect()),f=[-n.x,-n.y];return o?(yl("horizontal",d,t.get("selectorItemGap",!0)),a=[-(o=d.getBoundingRect()).x,-o.y],s=t.get("selectorButtonGap",!0),l=0===(t=t.getOrient().index)?"width":"height",h=0===t?"height":"width",u=0===t?"y":"x","end"===r?a[t]+=n[l]+s:f[t]+=o[l]+s,a[1-t]+=n[h]/2-o[h]/2,d.attr("position",a),c.attr("position",f),(r={x:0,y:0})[l]=n[l]+s+o[l],r[h]=Math.max(n[h],o[h]),r[u]=Math.min(0,o[u]+a[1-t]),r):(c.attr("position",f),this.group.getBoundingRect())},remove:function(){this.getContentGroup().removeAll(),this._isFirstRender=!0}});function mg(t,e,n,i,o,r){var a;return"line"!==e&&e.indexOf("empty")<0?(a=n.getItemStyle(),t.style.stroke=i,r||(a.stroke=o)):a=n.getItemStyle(["borderWidth","borderColor"]),t.setStyle(a)}function yg(t,e,n,i){_g(t,e,n,i),n.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),vg(t,e,n,i)}function vg(t,e,n,i){var o=n.getZr().storage.getDisplayList()[0];o&&o.useHoverLayer||n.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:i})}function _g(t,e,n,i){var o=n.getZr().storage.getDisplayList()[0];o&&o.useHoverLayer||n.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:i})}Oc(n.PROCESSOR.SERIES_FILTER,function(t){var n=t.findComponents({mainType:"legend"});n&&n.length&&t.filterSeries(function(t){for(var e=0;e<n.length;e++)if(!n[e].isSelected(t.name))return!1;return!0})}),c.registerSubTypeDefaulter("legend",function(){return"plain"});var xg=cg.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,n,i){var o=xl(t);xg.superCall(this,"init",t,e,n,i),wg(this,t,o)},mergeOption:function(t,e){xg.superCall(this,"mergeOption",t,e),wg(this,this.option,t)}});function wg(t,e,n){var i=[1,1];i[t.getOrient().index]=0,_l(e,n,{type:"box",ignoreSize:i})}var bg=h,Sg=["width","height"],Mg=["x","y"],Cg=s.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){Cg.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new bg),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new bg),this._showController},resetInner:function(){Cg.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,i,e,o,n,r,a){var s=this,l=(Cg.superCall(this,"renderInner",t,i,e,o,n,r,a),this._controllerGroup),h=i.get("pageIconSize",!0),t=(b(h)||(h=[h,h]),u("pagePrev",0),i.getModel("pageTextStyle"));function u(t,e){var n=t+"DataIndex",e=Os(i.get("pageIcons",!0)[i.getOrient().name][e],{onclick:nt(s._pageGo,s,n,i,o)},{x:-h[0]/2,y:-h[1]/2,width:h[0],height:h[1]});e.name=t,l.add(e)}l.add(new ga({name:"pageText",style:{textFill:t.getTextColor(),font:t.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),u("pageNext",1)},layoutInner:function(t,e,n,i,o,r){var a=this.getSelectorGroup(),s=t.getOrient().index,l=Sg[s],h=Mg[s],u=Sg[1-s],c=Mg[1-s],d=(o&&yl("horizontal",a,t.get("selectorItemGap",!0)),t.get("selectorButtonGap",!0)),f=a.getBoundingRect(),p=[-f.x,-f.y],g=M(n),n=(o&&(g[l]=n[l]-f[l]-d),this._layoutContentAndController(t,i,g,s,l,u,c));return o&&("end"===r?p[s]+=n[l]+d:(t=f[l]+d,p[s]-=t,n[h]-=t),n[l]+=f[l]+d,p[1-s]+=n[c]+n[u]/2-f[u]/2,n[u]=Math.max(n[u],f[u]),n[c]=Math.min(n[c],f[c]+p[1-s]),a.attr("position",p)),n},_layoutContentAndController:function(t,e,n,i,o,r,a){var s=this.getContentGroup(),l=this._containerGroup,h=this._controllerGroup,u=(yl(t.get("orient"),s,t.get("itemGap"),i?n.width:null,i?null:n.height),yl("horizontal",h,t.get("pageButtonItemGap",!0)),s.getBoundingRect()),c=h.getBoundingRect(),d=this._showController=u[o]>n[o],f=[-u.x,-u.y],e=(e||(f[i]=s.position[i]),[0,0]),p=[-c.x,-c.y],g=ht(t.get("pageButtonGap",!0),t.get("itemGap",!0)),f=(d&&("end"===t.get("pageButtonPosition",!0)?p[i]+=n[o]-c[o]:e[i]+=c[o]+g),p[1-i]+=u[r]/2-c[r]/2,s.attr("position",f),l.attr("position",e),h.attr("position",p),{x:0,y:0}),u=(f[o]=(d?n:u)[o],f[r]=Math.max(u[r],c[r]),f[a]=Math.min(0,c[a]+p[1-i]),l.__rectSize=n[o],d?((e={x:0,y:0})[o]=Math.max(n[o]-c[o]-g,0),e[r]=f[r],l.setClipPath(new O({shape:e})),l.__rectSize=e[o]):h.eachChild(function(t){t.attr({invisible:!0,silent:!0})}),this._getPageInfo(t));return null!=u.pageIndex&&Ds(s,{position:u.contentPosition},d&&t),this._updatePageInfoView(t,u),f},_pageGo:function(t,e,n){t=this._getPageInfo(e)[t];null!=t&&n.dispatchAction({type:"legendScroll",scrollDataIndex:t,legendId:e.id})},_updatePageInfoView:function(n,i){var o=this._controllerGroup,t=(D(["pagePrev","pageNext"],function(t){var e=null!=i[t+"DataIndex"],t=o.childOfName(t);t&&(t.setStyle("fill",e?n.get("pageIconColor",!0):n.get("pageIconInactiveColor",!0)),t.cursor=e?"pointer":"default")}),o.childOfName("pageText")),e=n.get("pageFormatter"),r=i.pageIndex,r=null!=r?r+1:0,a=i.pageCount;t&&e&&t.setStyle("text",k(e)?e.replace("{current}",r).replace("{total}",a):e({current:r,total:a}))},_getPageInfo:function(t){var e=t.get("scrollDataIndex",!0),n=this.getContentGroup(),i=this._containerGroup.__rectSize,o=t.getOrient().index,r=Sg[o],a=Mg[o],t=this._findTargetItemIndex(e),s=n.children(),e=s[t],l=s.length,h=l?1:0,u={contentPosition:n.position.slice(),pageCount:h,pageIndex:h-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(e){n=g(e);u.contentPosition[o]=-n.s;for(var c=t+1,d=n,f=n,p=null;c<=l;++c)(!(p=g(s[c]))&&f.e>d.s+i||p&&!m(p,d.s))&&(d=f.i>d.i?f:p)&&(null==u.pageNextDataIndex&&(u.pageNextDataIndex=d.i),++u.pageCount),f=p;for(c=t-1,d=n,f=n,p=null;-1<=c;--c)(p=g(s[c]))&&m(f,p.s)||!(d.i<f.i)||(f=d,null==u.pagePrevDataIndex&&(u.pagePrevDataIndex=d.i),++u.pageCount,++u.pageIndex),d=p}return u;function g(t){var e,n;if(t)return{s:n=(e=t.getBoundingRect())[a]+t.position[o],e:n+e[r],i:t.__legendDataIndex}}function m(t,e){return t.e>=e&&t.s<=e+i}},_findTargetItemIndex:function(n){var i,o;return this._showController?(this.getContentGroup().eachChild(function(t,e){t=t.__legendDataIndex;null==o&&null!=t&&(o=e),t===n&&(i=e)}),null!=i?i:o):0}});function Ig(t,e){var n,i,o=[],r=t.seriesIndex;return null==r||!(e=e.getSeriesByIndex(r))||null==(r=Ao(n=e.getData(),t))||r<0||b(r)?{point:[]}:(t=n.getItemGraphicEl(r),i=e.coordinateSystem,e.getTooltipPosition?o=e.getTooltipPosition(r)||[]:i&&i.dataToPoint?o=i.dataToPoint(n.getValues(S(i.dimensions,function(t){return n.mapDimension(t)}),r,!0))||[]:t&&((e=t.getBoundingRect().clone()).applyTransform(t.transform),o=[e.x+e.width/2,e.y+e.height/2]),{point:o,el:t})}Lc("legendScroll","legendscroll",function(t,e){var n=t.scrollDataIndex;null!=n&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(n)})});var Tg=D,Dg=l,kg=Po();function Ag(t,e,n,i,o){var r,a,s,l,h,u,c,d,f,p,g=t.axis;!g.scale.isBlank()&&g.containData(e)&&(t.involveSeries?(a=e,s=t.axis,l=s.dim,h=a,u=[],c=Number.MAX_VALUE,d=-1,Tg(t.seriesModels,function(e,t){var n,i=e.getData().mapDimension(l,!0);if(e.getAxisTooltipData)var o=e.getAxisTooltipData(i,a,s),r=o.dataIndices,o=o.nestestValue;else{if(!(r=e.getData().indicesOfNearest(i[0],a,"category"===s.type?.5:null)).length)return;o=e.getData().get(i[0],r[0])}null!=o&&isFinite(o)&&(i=a-o,(n=Math.abs(i))<=c)&&((n<c||0<=i&&d<0)&&(c=n,d=i,h=o,u.length=0),Tg(r,function(t){u.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}),p=(r={payloadBatch:u,snapToValue:h}).snapToValue,(f=r.payloadBatch)[0]&&null==o.seriesIndex&&C(o,f[0]),!i&&t.snap&&g.containData(p)&&null!=p&&(e=p),n.showPointer(t,e,f,o),n.showTooltip(t,r,p)):n.showPointer(t,e))}function Pg(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function Og(t,e,n,i){var o,r,n=n.payloadBatch,a=e.axis,s=a.model,l=e.axisPointerModel;e.triggerTooltip&&n.length&&(o=Jp(e=e.coordSys.model),(r=t.map[o])||(r=t.map[o]={coordSysId:e.id,coordSysIndex:e.componentIndex,coordSysType:e.type,coordSysMainType:e.mainType,dataByAxis:[]},t.list.push(r)),r.dataByAxis.push({axisDim:a.dim,axisIndex:s.componentIndex,axisType:s.type,axisId:s.id,value:i,valueLabelOpt:{precision:l.get("label.precision"),formatter:l.get("label.formatter")},seriesDataIndices:n.slice()}))}function Lg(t){var e=t.axis.model,n={},t=n.axisDim=t.axis.dim;return n.axisIndex=n[t+"AxisIndex"]=e.componentIndex,n.axisName=n[t+"AxisName"]=e.name,n.axisId=n[t+"AxisId"]=e.id,n}function Eg(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}Nc({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}});var zg=Po(),Bg=D;function Rg(t,e,n){var i,c,d;function o(t,u){c.on(t,function(e){n=d;var n,i,o={dispatchAction:r,pendings:i={showTip:[],hideTip:[]}};function r(t){var e=i[t.type];e?e.push(t):(t.dispatchAction=r,n.dispatchAction(t))}Bg(zg(c).records,function(t){t&&u(t,e,o.dispatchAction)});var t,a=o.pendings,s=d,l=a.showTip.length,h=a.hideTip.length;l?t=a.showTip[l-1]:h&&(t=a.hideTip[h-1]),t&&(t.dispatchAction=null,s.dispatchAction(t))})}y.node||(i=e.getZr(),zg(i).records||(zg(i).records={}),d=e,zg(c=i).initialized||(zg(c).initialized=!0,o("click",l(Fg,"click")),o("mousemove",l(Fg,"mousemove")),o("globalout",Ng)),(zg(i).records[t]||(zg(i).records[t]={})).handler=n)}function Ng(t,e,n){t.handler("leave",null,n)}function Fg(t,e,n,i){e.handler(t,n,i)}function Hg(t,e){y.node||(e=e.getZr(),(zg(e).records||{})[t]&&(zg(e).records[t]=null))}var Vg=Fc({type:"axisPointer",render:function(t,e,n){var e=e.getComponent("tooltip"),i=t.get("triggerOn")||e&&e.get("triggerOn")||"mousemove|click";Rg("axisPointer",n,function(t,e,n){"none"!==i&&("leave"===t||0<=i.indexOf(t))&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){Hg(e.getZr(),"axisPointer"),Vg.superApply(this._model,"remove",arguments)},dispose:function(t,e){Hg("axisPointer",e),Vg.superApply(this._model,"dispose",arguments)}}),Gg=Po(),Wg=M,Xg=nt;function Yg(){}function Ug(t,e,n,i){!function n(i,t){{var o;return A(i)&&A(t)?(o=!0,D(t,function(t,e){o=o&&n(i[e],t)}),!!o):i===t}}(Gg(n).lastProp,i)&&(Gg(n).lastProp=i,e?Ds(n,i,t):(n.stopAnimation(),n.attr(i)))}function qg(t,e){t[e.get("label.show")?"show":"hide"]()}function jg(t){return{position:t.position.slice(),rotation:t.rotation||0}}function Zg(t,e,n){var i=e.get("z"),o=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=o&&(t.zlevel=o),t.silent=n)})}function Kg(t,e,n,i,o){var r,a=$g(n.get("value"),e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get("label.precision"),formatter:n.get("label.formatter")}),n=n.getModel("label"),s=el(n.get("padding")||0),l=n.getFont(),h=ai(a,l),u=o.position,c=h.width+s[1]+s[3],h=h.height+s[0]+s[2],d=o.align,d=("right"===d&&(u[0]-=c),"center"===d&&(u[0]-=c/2),o.verticalAlign),f=("bottom"===d&&(u[1]-=h),"middle"===d&&(u[1]-=h/2),o=u,d=c,r=h,f=(i=i).getWidth(),i=i.getHeight(),o[0]=Math.min(o[0]+d,f)-d,o[1]=Math.min(o[1]+r,i)-r,o[0]=Math.max(o[0],0),o[1]=Math.max(o[1],0),n.get("backgroundColor"));f&&"auto"!==f||(f=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:c,height:h,r:n.get("borderRadius")},position:u.slice(),style:{text:a,textFont:l,textFill:n.getTextColor(),textPosition:"inside",textPadding:s,fill:f,stroke:n.get("borderColor")||"transparent",lineWidth:n.get("borderWidth")||0,shadowBlur:n.get("shadowBlur"),shadowColor:n.get("shadowColor"),shadowOffsetX:n.get("shadowOffsetX"),shadowOffsetY:n.get("shadowOffsetY")},z2:10}}function $g(t,e,n,i,o){t=e.scale.parse(t);var r,a=e.scale.getLabel(t,{precision:o.precision}),o=o.formatter;return o&&(r={value:lf(e,t),axisDimension:e.dim,axisIndex:e.index,seriesData:[]},D(i,function(t){var e=n.getSeriesByIndex(t.seriesIndex),t=t.dataIndexInside,e=e&&e.getDataParams(t);e&&r.seriesData.push(e)}),k(o)?a=o.replace("{value}",a):it(o)&&(a=o(r))),a}function Qg(t,e,n){var i=fe();return ve(i,i,n.rotation),ye(i,i,n.position),As([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}Ho((Yg.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,n,i){var o,r,a=e.get("value"),s=e.get("status");this._axisModel=t,this._axisPointerModel=e,this._api=n,!i&&this._lastValue===a&&this._lastStatus===s||(this._lastValue=a,this._lastStatus=s,i=this._group,o=this._handle,s&&"hide"!==s?(i&&i.show(),o&&o.show(),this.makeElOption(s={},a,t,e,n),(r=s.graphicKey)!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=r,r=this._moveAnimation=this.determineAnimation(t,e),i?(r=l(Ug,e,r),this.updatePointerEl(i,s,r,e),this.updateLabelEl(i,s,r,e)):(i=this._group=new h,this.createPointerEl(i,s,t,e),this.createLabelEl(i,s,t,e),n.getZr().add(i)),Zg(i,e,!0),this._renderHandle(a)):(i&&i.hide(),o&&o.hide()))},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var n,i=e.get("animation"),o=t.axis,r="category"===o.type,e=e.get("snap");return!(!e&&!r)&&("auto"===i||null==i?(n=this.animationThreshold,r&&o.getBandWidth()>n||!!e&&(r=$p(t).seriesDataCount,e=o.getExtent(),Math.abs(e[0]-e[1])/r>n)):!0===i)},makeElOption:function(t,e,n,i,o){},createPointerEl:function(t,e,n,i){var o=e.pointer;o&&(o=Gg(t).pointerEl=new Es[o.type](Wg(e.pointer)),t.add(o))},createLabelEl:function(t,e,n,i){e.label&&(e=Gg(t).labelEl=new O(Wg(e.label)),t.add(e),qg(e,i))},updatePointerEl:function(t,e,n){t=Gg(t).pointerEl;t&&e.pointer&&(t.setStyle(e.pointer.style),n(t,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,n,i){t=Gg(t).labelEl;t&&(t.setStyle(e.label.style),n(t,{shape:e.label.shape,position:e.label.position}),qg(t,i))},_renderHandle:function(t){var e,n,i,o,r,a;!this._dragging&&this.updateHandleTransform&&(e=this._axisPointerModel,n=this._api.getZr(),i=this._handle,o=e.getModel("handle"),a=e.get("status"),o.get("show")&&a&&"hide"!==a?(this._handle||(r=!0,i=this._handle=Os(o.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){oe(t.event)},onmousedown:Xg(this._onHandleDragMove,this,0,0),drift:Xg(this._onHandleDragMove,this),ondragend:Xg(this._onHandleDragEnd,this)}),n.add(i)),Zg(i,e,!1),i.setStyle(o.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"])),b(a=o.get("size"))||(a=[a,a]),i.attr("scale",[a[0]/2,a[1]/2]),function(t,e,n,i){var o=t[e];if(o){var r=o[cu]||o,a=o[fu];if(o[du]!==n||a!==i){if(null==n||!i)return t[e]=r;(o=t[e]=pu(r,n,"debounce"===i))[cu]=r,o[fu]=i,o[du]=n}}}(this,"_doDispatchAxisPointer",o.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,r)):(i&&n.remove(i),this._handle=null))},_moveHandleToValue:function(t,e){Ug(this._axisPointerModel,!e&&this._moveAnimation,this._handle,jg(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var n=this._handle;n&&(this._dragging=!0,t=this.updateHandleTransform(jg(n),[t,e],this._axisModel,this._axisPointerModel),this._payloadInfo=t,n.stopAnimation(),n.attr(jg(t)),Gg(n).lastProp=null,this._doDispatchAxisPointer())},_doDispatchAxisPointer:function(){var t,e;this._handle&&(t=this._payloadInfo,e=this._axisModel,this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]}))},_onHandleDragEnd:function(t){var e;this._dragging=!1,this._handle&&(e=this._axisPointerModel.get("value"),this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"}))},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var t=t.getZr(),e=this._group,n=this._handle;t&&e&&(this._lastGraphicKey=null,e&&t.remove(e),n&&t.remove(n),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}}}).constructor=Yg);r=Yg.extend({makeElOption:function(t,e,n,i,o){var r,a,s=n.axis,l=s.grid,h=i.get("type"),u=Jg(l,s).getOtherAxis(s).getGlobalExtent(),c=s.toGlobalCoord(s.dataToCoord(e,!0)),d=(h&&"none"!==h&&(a=(r=i).get("type"),r=r.getModel(a+"Style"),"line"===a?(d=r.getLineStyle()).fill=null:"shadow"===a&&((d=r.getAreaStyle()).stroke=null),a=d,(r=t0[h](s,c,u)).style=a,t.graphicKey=r.type,t.pointer=r),og(l.model,n));h=e,s=t,c=n,u=i,a=o,l=Rp.innerTextLayout((r=d).rotation,0,r.labelDirection),r.labelMargin=u.get("label.margin"),Kg(s,c,u,a,{position:Qg(c.axis,h,r),align:l.textAlign,verticalAlign:l.textVerticalAlign})},getHandleTransform:function(t,e,n){var i=og(e.axis.grid.model,e,{labelInside:!1});return i.labelMargin=n.get("handle.margin"),{position:Qg(e.axis,t,i),rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,n,i){var n=n.axis,o=n.grid,r=n.getGlobalExtent(!0),o=Jg(o,n).getOtherAxis(n).getGlobalExtent(),n="x"===n.dim?0:1,a=t.position,e=(a[n]+=e[n],a[n]=Math.min(r[1],a[n]),a[n]=Math.max(r[0],a[n]),(o[1]+o[0])/2),r=[e,e];r[n]=a[n];return{position:a,rotation:t.rotation,cursorPoint:r,tooltipOption:[{verticalAlign:"middle"},{align:"center"}][n]}}});function Jg(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var t0={line:function(t,e,n){var i;return i=[e,n[0]],e=[e,n[1]],n=e0(t),{type:"Line",subPixelOptimize:!0,shape:{x1:i[n=n||0],y1:i[1-n],x2:e[n],y2:e[1-n]}}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),o=n[1]-n[0];return{type:"Rect",shape:(e=[e-i/2,n[0]],n=[i,o],i=e0(t),{x:e[i=i||0],y:e[1-i],width:n[i],height:n[1-i]})}}};function e0(t){return"x"===t.dim?0:1}tg.registerAxisPointerClass("CartesianAxisPointer",r),Pc(function(t){var e;t&&(t.axisPointer&&0!==t.axisPointer.length||(t.axisPointer={}),e=t.axisPointer.link)&&!b(e)&&(t.axisPointer.link=[e])}),Oc(n.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=Zp(t,e)}),Lc({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},function(t,e,n){var r,a,i,s,l,o,h,u,c,d,f,p,g,m,y=t.currTrigger,v=[t.x,t.y],_=t,x=t.dispatchAction||nt(n.dispatchAction,n),w=e.getComponent("axisPointer").coordSysAxesInfo;if(w)return Eg(v)&&(v=Ig({seriesIndex:_.seriesIndex,dataIndex:_.dataIndex},e).point),r=Eg(v),a=_.axesInfo,i=w.axesInfo,s="leave"===y||Eg(v),l={},e={list:[],map:{}},h={showPointer:Dg(Pg,o={}),showTooltip:Dg(Og,e)},Tg(w.coordSysMap,function(t,e){var o=r||t.containPoint(v);Tg(w.coordSysAxesInfo[e],function(t,e){var n=t.axis,i=function(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}(a,t);s||!o||a&&!i||null!=(i=null!=(i=i&&i.value)||r?i:n.pointToData(v))&&Ag(t,i,h,!1,l)})}),u={},Tg(i,function(n,t){var i=n.linkGroup;i&&!o[t]&&Tg(i.axesInfo,function(t,e){var e=o[e];t!==n&&e&&(e=e.value,i.mapper&&(e=n.axis.scale.parse(i.mapper(e,Lg(t),Lg(n)))),u[n.key]=e)})}),Tg(u,function(t,e){Ag(i[e],t,h,!0,l)}),c=o,_=i,d=l.axesInfo=[],Tg(_,function(t,e){var n=t.axisPointerModel.option,e=c[e];e?(t.useHandle||(n.status="show"),n.value=e.value,n.seriesDataIndices=(e.payloadBatch||[]).slice()):t.useHandle||(n.status="hide"),"show"===n.status&&d.push({axisDim:t.axis.dim,axisIndex:t.axis.model.componentIndex,value:n.value})}),y=e,_=t,e=x,Eg(t=v)||!y.list.length?e({type:"hideTip"}):(x=((y.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{},e({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:_.tooltipOption,position:_.position,dataIndexInside:x.dataIndexInside,dataIndex:x.dataIndex,seriesIndex:x.seriesIndex,dataByCoordSys:y.list})),e=i,_=(t=n).getZr(),x="axisPointerLastHighlights",f=kg(_)[x]||{},p=kg(_)[x]={},Tg(e,function(t,e){t=t.axisPointerModel.option;"show"===t.status&&Tg(t.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;p[e]=t})}),g=[],m=[],D(f,function(t,e){p[e]||m.push(t)}),D(p,function(t,e){f[e]||g.push(t)}),m.length&&t.dispatchAction({type:"downplay",escapeConnect:!0,batch:m}),g.length&&t.dispatchAction({type:"highlight",escapeConnect:!0,batch:g}),l}),Nc({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var n0=D,i0=tl,o0=["","-webkit-","-moz-","-o-"];function r0(i){var t,e,o=[],n=i.get("transitionDuration"),r=i.get("backgroundColor"),a=i.getModel("textStyle"),s=i.get("padding");return n&&o.push((e="left "+n+"s "+(t="cubic-bezier(0.23, 1, 0.32, 1)")+",top "+n+"s "+t,S(o0,function(t){return t+"transition:"+e}).join(";"))),r&&(y.canvasSupported?o.push("background-Color:"+r):(o.push("background-Color:#"+je(r)),o.push("filter:alpha(opacity=70)"))),n0(["width","color","radius"],function(t){var e="border-"+t,n=i0(e),n=i.get(n);null!=n&&o.push(e+":"+n+("color"===t?"":"px"))}),o.push(function(n){var i=[],t=n.get("fontSize");(e=n.getTextColor())&&i.push("color:"+e),i.push("font:"+n.getFont());null==(e=n.get("lineHeight"))&&(e=Math.round(3*t/2)),t&&i.push("line-height:"+e+"px");var t=n.get("textShadowColor"),e=n.get("textShadowBlur")||0,o=n.get("textShadowOffsetX")||0,r=n.get("textShadowOffsetY")||0;return e&&i.push("text-shadow:"+o+"px "+r+"px "+e+"px "+t),n0(["decoration","align"],function(t){var e=n.get(t);e&&i.push("text-"+t+":"+e)}),i.join(";")}(a)),null!=s&&o.push("padding:"+el(s).join("px ")+"px"),o.join(";")+";"}function a0(t,e,n,i,o){var r,a,s=e&&e.painter;n?(n=s&&s.getViewportRoot())&&(r=t,a=document.body,jt(qt,n,i,o,!0))&&jt(r,a,qt[0],qt[1]):(t[0]=i,t[1]=o,(n=s&&s.getViewportRootOffset())&&(t[0]+=n.offsetLeft,t[1]+=n.offsetTop)),t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}function s0(t,e,n){if(y.wxa)return null;var i=document.createElement("div"),o=(i.domBelongToZr=!0,this.el=i,this._zr=e.getZr()),n=this._appendToBody=n&&n.appendToBody,r=(this._styleCoord=[0,0,0,0],a0(this._styleCoord,o,n,e.getWidth()/2,e.getHeight()/2),(n?document.body:t).appendChild(i),this._container=t,this._show=!1,this._hideTimeout,this);i.onmouseenter=function(){r._enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},i.onmousemove=function(t){var e;t=t||window.event,r._enterable||(e=o.handler,ne(o.painter.getViewportRoot(),t,!0),e.dispatch("mousemove",t))},i.onmouseleave=function(){r._enterable&&r._show&&r.hideLater(r._hideDelay),r._inContent=!1}}function l0(t,e,n,i){t[0]=n,t[1]=i,t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}function h0(t){var e=this._zr=t.getZr();this._styleCoord=[0,0,0,0],l0(this._styleCoord,e,t.getWidth()/2,t.getHeight()/2),this._show=!1,this._hideTimeout}s0.prototype={constructor:s0,_enterable:!0,update:function(t){var e=this._container,n=e.currentStyle||document.defaultView.getComputedStyle(e),e=e.style;"absolute"!==e.position&&"absolute"!==n.position&&(e.position="relative"),t.get("alwaysShowContent")&&this._moveTooltipIfResized()},_moveTooltipIfResized:function(){var t=this._styleCoord[2],e=this._styleCoord[3],t=t*this._zr.getWidth(),e=e*this._zr.getHeight();this.moveTo(t,e)},show:function(t){clearTimeout(this._hideTimeout);var e=this.el,n=this._styleCoord;e.style.cssText="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+r0(t)+";left:"+n[0]+"px;top:"+n[1]+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",e.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var n=this._styleCoord,t=(a0(n,this._zr,this._appendToBody,t,e),this.el.style);t.left=n[0]+"px",t.top=n[1]+"px"},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(nt(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){this.el.parentNode.removeChild(this.el)},getOuterSize:function(){var t,e=this.el.clientWidth,n=this.el.clientHeight;return document.defaultView&&document.defaultView.getComputedStyle&&(t=document.defaultView.getComputedStyle(this.el))&&(e+=parseInt(t.borderLeftWidth,10)+parseInt(t.borderRightWidth,10),n+=parseInt(t.borderTopWidth,10)+parseInt(t.borderBottomWidth,10)),{width:e,height:n}}},h0.prototype={constructor:h0,_enterable:!0,update:function(t){t.get("alwaysShowContent")&&this._moveTooltipIfResized()},_moveTooltipIfResized:function(){var t=this._styleCoord[2],e=this._styleCoord[3],t=t*this._zr.getWidth(),e=e*this._zr.getHeight();this.moveTo(t,e)},show:function(t){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0},setContent:function(t,e,n){this.el&&this._zr.remove(this.el);for(var i={},o=t,r="{marker",a=o.indexOf(r);0<=a;){var s=o.indexOf("|}"),l=o.substr(a+r.length,s-a-r.length);-1<l.indexOf("sub")?i["marker"+l]={textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:e[l],textOffset:[3,0]}:i["marker"+l]={textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:e[l]},a=(o=o.substr(s+1)).indexOf("{marker")}var h=n.getModel("textStyle"),u=h.get("fontSize"),c=n.get("textLineHeight"),d=(null==c&&(c=Math.round(3*u/2)),this.el=new ga({style:xs({},h,{rich:i,text:t,textBackgroundColor:n.get("backgroundColor"),textBorderRadius:n.get("borderRadius"),textFill:n.get("textStyle.color"),textPadding:n.get("padding"),textLineHeight:c}),z:n.get("z")}),this._zr.add(this.el),this);this.el.on("mouseover",function(){d._enterable&&(clearTimeout(d._hideTimeout),d._show=!0),d._inContent=!0}),this.el.on("mouseout",function(){d._enterable&&d._show&&d.hideLater(d._hideDelay),d._inContent=!1})},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el.getBoundingRect();return[t.width,t.height]},moveTo:function(t,e){var n;this.el&&(l0(n=this._styleCoord,this._zr,t,e),this.el.attr("position",[n[0],n[1]]))},hide:function(){this.el&&this.el.hide(),this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(nt(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){clearTimeout(this._hideTimeout),this.el&&this._zr.remove(this.el)},getOuterSize:function(){var t=this.getSize();return{width:t[0],height:t[1]}}};var u0=nt,c0=D,d0=v,f0=new O({shape:{x:-1,y:-1,width:2,height:2}});function p0(t){for(var e=t.pop();t.length;){var n=t.pop();n&&(_.isInstance(n)&&(n=n.get("tooltip",!0)),e=new _(n="string"==typeof n?{formatter:n}:n,e,e.ecModel))}return e}function g0(t,e){return t.dispatchAction||nt(e.dispatchAction,e)}function m0(t){return"center"===t||"middle"===t}Fc({type:"tooltip",init:function(t,e){var n,i;y.node||(n=(t=t.getComponent("tooltip")).get("renderMode"),this._renderMode=Bo(n),"html"===this._renderMode?(i=new s0(e.getDom(),e,{appendToBody:t.get("appendToBody",!0)}),this._newLine="<br/>"):(i=new h0(e),this._newLine="\n"),this._tooltipContent=i)},render:function(t,e,n){y.node||(this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent"),(e=this._tooltipContent).update(t),e.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow())},_initGlobalListener:function(){var i=this._tooltipModel.get("triggerOn");Rg("itemTooltip",this._api,u0(function(t,e,n){"none"!==i&&(0<=i.indexOf(t)?this._tryShow(e,n):"leave"===t&&this._hide(n))},this))},_keepShow:function(){var t,e=this._tooltipModel,n=this._ecModel,i=this._api;null!=this._lastX&&null!=this._lastY&&"none"!==e.get("triggerOn")&&(t=this,clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){i.isDisposed()||t.manuallyShowTip(e,n,i,{x:t._lastX,y:t._lastY})}))},manuallyShowTip:function(t,e,n,i){var o,r,a;i.from===this.uid||y.node||(o=g0(i,n),this._ticket="",a=i.dataByCoordSys,i.tooltip&&null!=i.x&&null!=i.y?((r=f0).position=[i.x,i.y],r.update(),r.tooltip=i.tooltip,this._tryShow({offsetX:i.x,offsetY:i.y,target:r},o)):a?this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:i.dataByCoordSys,tooltipOption:i.tooltipOption},o):null!=i.seriesIndex?this._manuallyAxisShowTip(t,e,n,i)||(a=(r=Ig(i,e)).point[0],t=r.point[1],null!=a&&null!=t&&this._tryShow({offsetX:a,offsetY:t,position:i.position,target:r.el},o)):null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target},o)))},manuallyHideTip:function(t,e,n,i){var o=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&o.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,i.from!==this.uid&&this._hide(g0(i,n))},_manuallyAxisShowTip:function(t,e,n,i){var o=i.seriesIndex,r=i.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=o&&null!=r&&null!=a){a=e.getSeriesByIndex(o);if(a){t=p0([a.getData().getItemModel(r),a,(a.coordinateSystem||{}).model,t]);if("axis"===t.get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:o,dataIndex:r,position:i.position}),!0}}},_tryShow:function(t,e){var n,i=t.target;this._tooltipModel&&(this._lastX=t.offsetX,this._lastY=t.offsetY,(n=t.dataByCoordSys)&&n.length?this._showAxisTooltip(n,t):i&&null!=i.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,i,e)):i&&i.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,i,e)):(this._lastDataByCoordSys=null,this._hide(e)))},_showOrMove:function(t,e){t=t.get("showDelay");e=nt(e,this),clearTimeout(this._showTimout),0<t?this._showTimout=setTimeout(e,t):e()},_showAxisTooltip:function(t,e){var h=this._ecModel,n=this._tooltipModel,i=[e.offsetX,e.offsetY],u=[],c=[],o=p0([e.tooltipOption,n]),d=this._renderMode,f=this._newLine,p={},r=(c0(t,function(t){c0(t.dataByAxis,function(o){var r,t,a=h.getComponent(o.axisDim+"Axis",o.axisIndex),s=o.value,l=[];a&&null!=s&&(r=$g(s,a.axis,h,o.seriesDataIndices,o.valueLabelOpt),D(o.seriesDataIndices,function(t){var e,n=h.getSeriesByIndex(t.seriesIndex),t=t.dataIndexInside,i=n&&n.getDataParams(t);i.axisDim=o.axisDim,i.axisIndex=o.axisIndex,i.axisType=o.axisType,i.axisId=o.axisId,i.axisValue=lf(a.axis,s),i.axisValueLabel=r,i&&(c.push(i),A(i=n.formatTooltip(t,!0,null,d))?(e=i.html,n=i.markers,g(p,n)):e=i,l.push(e))}),t=r,"html"!==d?u.push(l.join(f)):u.push((t?ol(t)+f:"")+l.join(f)))})},this),u.reverse(),u=u.join(this._newLine+this._newLine),e.position);this._showOrMove(o,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(o,r,i[0],i[1],this._tooltipContent,c):this._showTooltipContent(o,u,c,Math.random(),i[0],i[1],r,void 0,p)})},_showSeriesItemTooltip:function(t,e,n){var i,o,r,a,s=this._ecModel,l=e.seriesIndex,s=s.getSeriesByIndex(l),h=e.dataModel||s,u=e.dataIndex,e=e.dataType,c=h.getData(e),d=p0([c.getItemModel(u),h,s&&(s.coordinateSystem||{}).model,this._tooltipModel]),s=d.get("trigger");null!=s&&"item"!==s||(i=h.getDataParams(u,e),s=h.formatTooltip(u,!1,e,this._renderMode),r=A(s)?(o=s.html,s.markers):(o=s,null),a="item_"+h.name+"_"+u,this._showOrMove(d,function(){this._showTooltipContent(d,o,i,a,t.offsetX,t.offsetY,t.position,t.target,r)}),n({type:"showTip",dataIndexInside:u,dataIndex:c.getRawIndex(u),seriesIndex:l,from:this.uid}))},_showComponentItemTooltip:function(t,e,n){var i=e.tooltip,o=new _(i="string"==typeof i?{content:i,formatter:i}:i,this._tooltipModel,this._ecModel),r=o.get("content"),a=Math.random();this._showOrMove(o,function(){this._showTooltipContent(o,r,o.get("formatterParams")||{},a,t.offsetX,t.offsetY,t.position,e)}),n({type:"showTip",from:this.uid})},_showTooltipContent:function(n,t,i,e,o,r,a,s,l){var h,u,c;this._ticket="",n.get("showContent")&&n.get("show")&&(h=this._tooltipContent,u=n.get("formatter"),a=a||n.get("position"),t=t,u&&"string"==typeof u?t=sl(u,i,!0):"function"==typeof u&&(c=u0(function(t,e){t===this._ticket&&(h.setContent(e,l,n),this._updatePosition(n,a,o,r,h,i,s))},this),this._ticket=e,t=u(i,e,c)),h.setContent(t,l,n),h.show(n),this._updatePosition(n,a,o,r,h,i,s))},_updatePosition:function(t,e,n,i,o,r,a){var s,l=this._api.getWidth(),h=this._api.getHeight(),u=(e=e||t.get("position"),o.getSize()),c=t.get("align"),d=t.get("verticalAlign"),f=a&&a.getBoundingRect().clone();a&&f.applyTransform(a.transform),b(e="function"==typeof e?e([n,i],r,o.el,f,{viewSize:[l,h],contentSize:u.slice()}):e)?(n=d0(e[0],l),i=d0(e[1],h)):A(e)?(e.width=u[0],e.height=u[1],n=(r=vl(e,{width:l,height:h})).x,i=r.y,d=c=null):i=(n=(s="string"==typeof e&&a?function(t,e,n){var i=n[0],o=n[1],r=0,a=0,s=e.width,l=e.height;switch(t){case"inside":r=e.x+s/2-i/2,a=e.y+l/2-o/2;break;case"top":r=e.x+s/2-i/2,a=e.y-o-5;break;case"bottom":r=e.x+s/2-i/2,a=e.y+l+5;break;case"left":r=e.x-i-5,a=e.y+l/2-o/2;break;case"right":r=e.x+s+5,a=e.y+l/2-o/2}return[r,a]}(e,f,u):function(t,e,n,i,o,r,a){var n=n.getOuterSize(),s=n.width,n=n.height;null!=r&&(i<t+s+r?t-=s+r:t+=r);null!=a&&(o<e+n+a?e-=n+a:e+=a);return[t,e]}(n,i,o,l,h,c?null:20,d?null:20))[0],s[1]),c&&(n-=m0(c)?u[0]/2:"right"===c?u[0]:0),d&&(i-=m0(d)?u[1]/2:"bottom"===d?u[1]:0),t.get("confine")&&(r=n,a=i,e=l,f=h,c=(c=o).getOuterSize(),d=c.width,c=c.height,r=Math.min(r+d,e)-d,a=Math.min(a+c,f)-c,r=Math.max(r,0),a=Math.max(a,0),n=(s=[r,a])[0],i=s[1]),o.moveTo(n,i)},_updateContentNotChangedOnAxis:function(n){var t=this._lastDataByCoordSys,r=!!t&&t.length===n.length;return r&&c0(t,function(t,e){var t=t.dataByAxis||{},o=(n[e]||{}).dataByAxis||[];(r&=t.length===o.length)&&c0(t,function(t,e){var e=o[e]||{},n=t.seriesDataIndices||[],i=e.seriesDataIndices||[];(r&=t.value===e.value&&t.axisType===e.axisType&&t.axisId===e.axisId&&n.length===i.length)&&c0(n,function(t,e){e=i[e];r&=t.seriesIndex===e.seriesIndex&&t.dataIndex===e.dataIndex})})}),this._lastDataByCoordSys=n,!!r},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){y.node||(this._tooltipContent.dispose(),Hg("itemTooltip",e))}}),Lc({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),Lc({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){}),t.Axis=Lf,t.List=nd,t.Model=_,t.PRIORITY=n,t.color=e,t.connect=function(e){var t;return b(e)&&(t=e,e=null,x(t,function(t){null!=t.group&&(e=t.group)}),e=e||"g_"+Ic++,x(t,function(t){t.group=e})),Mc[e]=!0,e},t.dataTool={},t.dependencies=Yu,t.disConnect=Dc,t.disconnect=Sa,t.dispose=function(t){"string"==typeof t?t=Sc[t]:t instanceof $u||(t=kc(t)),t instanceof $u&&!t.isDisposed()&&t.dispose()},t.env=y,t.extendChartView=function(t){return ru.extend(t)},t.extendComponentModel=Nc,t.extendComponentView=Fc,t.extendSeriesModel=function(t){return qh.extend(t)},t.format=xa,t.getCoordinateSystemDimensions=function(t){if(t=th.get(t))return t.getDimensionsInfo?t.getDimensionsInfo():t.dimensions.slice()},t.getInstanceByDom=kc,t.getInstanceById=function(t){return Sc[t]},t.getMap=function(t){return(t=Fu(t))&&t[0]&&{geoJson:t[0].geoJSON,specialAreas:t[0].specialAreas}},t.graphic=Rf,t.helper=ma,t.init=function(t,e,n){if(I){if(+mo.replace(".","")<+Yu.zrender.replace(".",""))throw new Error("zrender/src 4.3.2 is too old for ECharts 4.9.0. Current version need ZRender "+Yu.zrender+"+");if(!t)throw new Error("Initialize failed: invalid dom.")}var i,o,r=kc(t);return r?I&&console.warn("There is a chart instance already initialized on the dom."):(!I||!at(t)||"CANVAS"===t.nodeName.toUpperCase()||(t.clientWidth||n&&null!=n.width)&&(t.clientHeight||n&&null!=n.height)||console.warn("Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload."),(r=new $u(t,e,n)).id="ec_"+Cc++,Sc[r.id]=r,zo(t,Tc,r.id),i=r,o="__connectUpdateStatus",x(mc,function(t,e){i._messageCenter.on(e,function(t){var e,n;!Mc[i.group]||0===i[o]||t&&t.escapeConnect||(e=i.makeActionFromEvent(t),n=[],x(Sc,function(t){t!==i&&t.group===i.group&&n.push(t)}),a(n,0),x(n,function(t){1!==t[o]&&t.dispatchAction(e)}),a(n,2))})})),r;function a(t,e){for(var n=0;n<t.length;n++)t[n][o]=e}},t.matrix=we,t.number=Ma,t.parseGeoJSON=Sf,t.parseGeoJson=u,t.registerAction=Lc,t.registerCoordinateSystem=function(t,e){th.register(t,e)},t.registerLayout=Ec,t.registerLoading=Rc,t.registerMap=function(t,e,n){Nu(t,e,n)},t.registerPostUpdate=function(t){_c.push(t)},t.registerPreprocessor=Pc,t.registerProcessor=Oc,t.registerTheme=Ac,t.registerVisual=zc,t.setCanvasCreator=function(t){q("createCanvas",t)},t.throttle=pu,t.util=Bf,t.vector=N,t.version="4.9.0",t.zrender=cn});