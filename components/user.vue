<template>
	<gracePage :customHeader="false">
		<view slot="gBody">
			<!-- 头部 -->
			<view class="person-head">
				<image class="grace-list-image ucenter-face-image" :src="logo | imgPath" mode="widthFix"></image>
				<view class="person-head-box"  @tap="toUrl(hasLogin?'/pages_user/pages/user/info':'/pages/login/login')">
					<view v-if="hasLogin">
						<view class="person-head-nickname">
							{{ userInfo && userInfo.name ? userInfo.name : userName }}  
							<text v-if="isManager && userInfo.myRoles" class="grace-desc"> ( {{ userInfo.myRoles.includes('GroupLeader') || userInfo.myRoles.includes('VicGroupLeader') ? '负责人' : '管理人员'}} )</text>
						</view>
						<!-- <view class="person-head-username">ID: {{ userInfo.idNo}}</view> -->
						<view class="person-head-username" v-if="userInfo.company">企业：{{ userInfo.company || '' }}</view>
					</view>
					<view v-else>
						<view class="person-head-nickname">请登录</view>
					</view>
				</view>
				<view class="person-head-right" style="margin-top: 80rpx;" v-if="hasLogin"  @click="toUrl(hasLogin?'/pages_user/pages/user/message':'/pages/login/login')">
					<text class="grace-fixed-msg-icon grace-icons icon-msg grace-white"></text>
				</view>
			</view>
			<!-- 列表区 -->
			<view class="grace-list grace-body">
				<!-- 九宫格 -->
				<view class="grace-grids grids-border-wrap" v-if="hasLogin">
					<text class="iconfont icon-MBEfenggeduosetubiao-tixing"></text>
					<view class="grace-grids-items grids-border-item grace-relative" v-for="(item,index) in userPower" :key="item.path" @click="toUrl(hasLogin?`${item.path}`:'/pages/login/login')" v-show="index < 6">
						<text class="grace-grids-icon grace-icons" :class="item.icon"></text>
						<text v-if="item.title === '体检信息'" :class="{badge:physicalExaminationNotice && JSON.stringify(physicalExaminationNotice) !== '{}'}">
						</text>
						<text v-if="item.title === '防护用品领用'" :class="{badge:defendproducts}">
						</text>
						<text class="grace-grids-text grace-list-title-text">{{ item.title }}</text> 
					</view>
				<!-- 	<view class="grace-grids-items grids-border-item grace-relative">
						<text class="grace-grids-icon grace-icons">12</text>
						<text class="grace-grids-text grace-list-title-text">全生命周期管理</text>
					</view> -->
				</view>
				<!-- #ifdef MP-WEIXIN -->
					<view class="grace-list-items" @click="toUrl('/pages/login/bindWxInfo') " v-if="hasLogin&& ( !userInfo.wx || !userInfo.wx.openId )" >
					  <text class="grace-list-icon grace-icons icon-weixin grace-green"></text>
					  <view class="grace-list-body grace-border-b">
						<view class="grace-list-title">
						  <text class="grace-list-title-text">绑定微信</text>
						</view>
					  </view>
					  <text class="grace-list-arrow-right grace-icons icon-arrow-right"></text>
					</view>
				<!-- #endif -->
				<view class="grace-list-items" v-for="(item, index) in userPower" :key="item.path" @click="toUrl(hasLogin?`${item.path}`:'/pages/login/login')" v-show="index >= 4">
					<text  class="grace-list-icon grace-icons" :class="item.icon"></text>
					<view class="grace-list-body grace-border-b">
						<view class="grace-list-title">
							<text class="grace-list-title-text">{{ item.title }}</text>
							<!-- <text class="grace-badge grace-bg-red">12</text> -->
						</view>
					</view>
					<text class="grace-list-arrow-right grace-icons icon-arrow-right"></text>
				</view>

				<view class="grace-list-items" @click="toRocketChat" v-if="hasLogin">
					<text class="grace-list-icon grace-icons icon-comments grace-green"></text>
					<view class="grace-list-body grace-border-b">
						<view class="grace-list-title">
							<text class="grace-list-title-text">互动交流</text>
						</view>
					</view>
					<text class="grace-list-arrow-right grace-icons icon-arrow-right"></text>
				</view>
				
				<view class="grace-list-items" @click="handleLogout" v-if="hasLogin">
					<text class="grace-list-icon grace-icons icon-logoff grace-red"></text>
					<view class="grace-list-body grace-border-b">
						<view class="grace-list-title">
							<text class="grace-list-title-text">退出登录</text>
						</view>
					</view>
					<text class="grace-list-arrow-right grace-icons icon-arrow-right"></text>
				</view>


				<view class="grace-list-items">
					<text class="grace-list-icon grace-icons icon-set grace-light-blue"></text>
					<view class="grace-list-body grace-border-b">
						<view class="grace-list-title">
							<text class="grace-list-title-text">当前版本</text>
						</view>
					</view>
					<text style="font-size: 26rpx;color: #666;">v {{ version }}</text>
				</view>
				
			</view>
		</view>
		
	</gracePage>
</template>

<script>
	// import graceBoxBanner from "@/graceUI/components/graceBoxBanner.vue";
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	import userApi from '@/api/user.js';
	import config from '@/common.js';
	import employeeApi from '@/api/employee.js' 
	// const defaultAvatar = require('@/static/defaultAvatar.png');
	import { imgPath } from '@/common.js';

	export default {
		// components: {
		// 	graceBoxBanner
		// },
		data() {
			return {
				physicalExaminationNotice:{},
				logo:'',
				userName:'',
				statusBarHeight: 0, // 顶部状态栏的高度
				EnterpriseID: '', // 当前所属企业的id,或者说最近工作的那家企业id
				defendproducts: false,
				// defaultAvatar: defaultAvatar, // 默认的头像
			};
		},
		computed: {
			...mapGetters([
				'hasLogin',
				'userInfo',
				'userPower',
				'isManager'
			]),
			version(){
				return config.version || '';
			}
		},
		watch:{
			userInfo(val){
				this.getUserInfo(val)
			},
		},
		created() {
			this.getUserInfo(this.userInfo)
			if(this.hasLogin && this.userInfo){
				this.EnterpriseID = this.userInfo.companyId.length ? this.userInfo.companyId[this.userInfo.companyId.length-1] : '';
			}
			if(this.userInfo.companyId){
				this.getphysicalExaminationNotice();
				this.getDefendproducts();
			}
		},
		methods: {
			async toRocketChat(){
				const res = await userApi.getChatToken();
				if(res.status === 200 && res.data){
            		window.open(res.data, '_blank');
				}
			},
			async getphysicalExaminationNotice(){
				console.log(this.userInfo.idNo)
				console.log(this.EnterpriseID)
				if(!this.EnterpriseID || !this.userInfo.idNo) return;
				const EnterpriseID = Array.isArray(this.userInfo.companyId) ? this.userInfo.companyId[this.userInfo.companyId.length - 1] : this.userInfo.companyId;
				let res = await userApi.getphysicalExaminationNotice({
					EnterpriseID,
					idNo:this.userInfo.idNo,
				});
				console.log(res)
				if(res.status === 200){
					// if(res.data.show){
						this.physicalExaminationNotice = res.data
						uni.setStorageSync('physicalExaminationNotice', res.data)
					// }else{
					// 	this.physicalExaminationNotice = {}
					// 	uni.setStorageSync('physicalExaminationNotice', {})
					// }
				}
			},
			async getDefendproducts(){
				const userInfo = this.userInfo
				const res = await employeeApi.getDefendproducts(userInfo);
				if(!res || !res.date) return;
				res.data.forEach(item => {
					const restDate = ( Date.parse(item.date)-Date.parse(new Date()) )/(1*24*60*60*1000);
					if(restDate>0 && restDate<8){
						this.defendproducts = true;
					}
				});
			},
			getUserInfo(val){
				const loginType = uni.getStorageSync('loginType')
				if(!this.hasLogin) return
				this.logo = val.logo || 'https://avatar.bbs.miui.com/images/noavatar_small.gif'
				this.userName = val.name || val.userName 
			},
			...mapActions(['logout']), // 退出登录
			// 跳转到某个页面
			toUrl(url) {
				console.log(url, 123);
				// debugger
				uni.navigateTo({
					url
				});
			},
			handleLogout(){
				this.logout();
			}
		},
		filters: {
			imgPath: img => imgPath(img),
		},
	}
</script>

<style scoped>
	.grace-body{
		width: 100%;
		box-sizing: border-box;
	}
	.orange{
		color: #f37b1d!important;
	}
	.badge{
		position: absolute;
		/* padding: 0.5px 3px;
		border-radius: 50%; */
		left:53px;
		top: 18px;
		background-color: red;
		/* font-size: 12px;
		color: white; */
		width: 6px;
		height: 6px;
		border-radius: 50%;
	}
	.demo-face{width:84rpx; height:84rpx; margin-right:12px;}
	/* .demo-face .grace-icons{
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		font-size: 20px;
	} */
	.ucenter-face,
	.ucenter-face-image{
		width:100rpx !important; 
		height:100rpx !important;
	}
	.person-head {
		display: flex;
		flex-direction: row;
		align-items: center;
		height: 150px;
		padding: 0 20px;
		/* background: url(../static/userBg.png) 100% 100% no-repeat; */
		background-image:linear-gradient(to bottom, #3688FF,#4d65f3);
		/* background: linear-gradient(to right, #0081ff, #36bbff); */
		/* background-image: linear-gradient(45deg, #00B3FF, #B100FF); */
	}
	/* #ifdef MP-WEIXIN */
	.person-head{
		padding-top: 10rpx;
	}
	/* #endif */

	.person-head-box {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: flex-start;
		margin-left: 40rpx;
		line-height: 44rpx;
	}

	.person-head-nickname {
		font-size: 18px;
		font-weight: 500;
		color: #fff;
		margin-bottom: 8rpx;
	}
	.person-head-right{
		flex:1;
		text-align: right;
	}

	.person-head-username {
		font-size: 14px;
		font-weight: 500;
		color: #fff;
	}

	.grace-list {
		background-color: #fff;
		padding-top: 5px;
		height: calc(100vh - 240px);
		overflow-y: auto;
	}
	.grace-icons{
		width: 36px;
		height: 36px;
		line-height: 36px;
		font-size: 19px;
	}
	.grids-border-wrap{
		border-bottom: 7px solid #F6F7F8;
	}
</style>
