<template>
  <view class="upload-container">
    <u-upload
      :fileList="fileList"
      :name="name"
      :previewFullImage="true"
      :deletable="false"
      :showProgress="true"
      :showUploadList="true"
      multiple
      :maxCount="maxCount"
      @afterRead="handleAfterRead"
      @delete="handleDelete"
      accept="all"
    ></u-upload>
    <view style="font-size: 12px; color: #606266;">支持 png, jpg, jpeg等格式</view>
  </view>
</template>

<script>
export default {
  props: {
    fileList: {
      type: Array,
      default: () => [],
    },
    name: {
      type: String,
      required: true,
    },
    maxCount: {
      type: Number,
      default: 1,
    },
    uploadUrl: {
      type: String,
      required: true,
    },
    diagnosisId: {
      type: String || Number || undefined,
      required: true,
    },
  },
  methods: {
    async handleAfterRead(event) {
      const lists = event.file;
      const fileListLen = this.fileList.length;
      lists.forEach((item) => {
        this.fileList.push({
          ...item,
          status: "uploading",
          message: "上传中",
        });
      });

      for (let i = 0; i < lists.length; i++) {
        try {
          const filePath = lists[i].url;
          const result = await new Promise((resolve, reject) => {
            uni.uploadFile({
              url: this.uploadUrl,
              filePath: filePath,
              name: 'file',
              formData: {
                diagnosisId: this.diagnosisId,
              },
              success: (res) => {
                try {
                  const responseData = JSON.parse(res.data);
                  resolve(responseData);
                } catch (e) {
                  reject(new Error('响应数据解析失败'));
                }
              },
              fail: (err) => {
                reject(new Error(err.errMsg || '上传失败'));
              },
            });
          });
          const item = this.fileList[fileListLen + i];
          this.$set(this.fileList, fileListLen + i, {
            ...item,
            status: "success",
            message: "",
            url: result.data.data.url,
            fileClassify: result.data.data.fileClassify,
            fileId: result.data.data.id,
          });
          // if(this.diagnosisId){
          //   this.$set(this.fileList, fileListLen + i, {
          //   ...item,
          //   status: "success",
          //   message: "",
          //   url: result.data.data.url,
          // });
          // }else{
          //   this.$set(this.fileList, fileListLen + i, {
          //   ...item,
          //   status: "success",
          //   message: "",
          //   url: result.data.data.url,
          //   fileClassify: result.data.data.fileClassify,
          //   fileId: result.data.data.id,
          //   });
          // }
        } catch (error) {
          console.error('上传失败:', error);
          this.$set(this.fileList, fileListLen + i, {
            ...this.fileList[fileListLen + i],
            status: "error",
            message: "上传失败",
          });
        }
      }
    },
    handleDelete(event) {
      this.fileList.splice(event.index, 1);
    },
  },
};
</script>

<style scoped>
.upload-container {
  display: flex;
  flex-direction: column;
}
</style>